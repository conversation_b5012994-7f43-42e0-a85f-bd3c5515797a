{"name": "neware-uicc-portal", "version": "0.1.1", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build --configuration production", "build:test": "ng build --configuration test", "build:prod": "ng build --configuration prod", "build:neware": "ng build --configuration neware", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "reinstall:core": "npm uninstall @ng-nwui/core --save && npm install @ng-nwui/core@latest --save-exact", "reinstall:@ng-nwui-frame/core": "npm uninstall @ng-nwui-frame/core --save && npm install @ng-nwui-frame/core --save-exact", "reinstall:@ng-nwui-frame/merchant": "npm uninstall @ng-nwui-frame/merchant-manage --save && npm install @ng-nwui-frame/merchant-manage@latest --save-exact", "reinstall:@ng-nwui-frame/privilege": "npm uninstall @ng-nwui-frame/privilege-manage --save && npm install @ng-nwui-frame/privilege-manage@latest --save-exact", "reinstall:@ng-nwui-frame/system": "npm uninstall @ng-nwui-frame/system-manage --save && npm install @ng-nwui-frame/system-manage@latest --save-exact"}, "private": true, "dependencies": {"@angular/animations": "~12.2.0", "@angular/cdk": "^12.2.13", "@angular/common": "~12.2.0", "@angular/compiler": "~12.2.0", "@angular/core": "~12.2.0", "@angular/forms": "~12.2.0", "@angular/platform-browser": "~12.2.0", "@angular/platform-browser-dynamic": "~12.2.0", "@angular/router": "~12.2.0", "@ng-nwui-frame/core": "0.1.2", "@ng-nwui-frame/merchant-manage": "0.1.5", "@ng-nwui-frame/privilege-manage": "0.1.4", "@ng-nwui-frame/system-manage": "0.1.3", "@ng-nwui/components": "0.1.85", "@ng-nwui/core": "0.1.85", "@ng-nwui/integration": "0.1.85", "@types/file-saver": "^2.0.4", "crypto-js": "^4.2.0", "compressorjs": "^1.2.1", "file-saver": "^2.0.5", "jsencrypt": "^3.3.2", "luxon": "^3.3.0", "rxjs": "~6.6.0", "sweetalert2": "11.1.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.4", "@angular/cli": "~12.2.4", "@angular/compiler-cli": "~12.2.0", "@fortawesome/fontawesome-free": "5.15.4", "@types/crypto-js": "^4.1.3", "@types/jasmine": "~3.8.0", "@types/luxon": "^3.3.0", "@types/node": "^12.11.1", "bootstrap": "4.6.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "jquery": "1.9.1", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "moment": "^2.29.1", "popper.js": "^1.16.1", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.3.5"}}