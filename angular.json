{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"version": "1.0.0", "neware-uicc-portal": {"projectType": "application", "schematics": {"@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/neware-uicc-portal", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [{"input": "node_modules/jquery/jquery.min.js", "inject": true, "bundleName": "nwui.plugins.jquery"}, {"input": "node_modules/popper.js/dist/umd/popper.min.js", "inject": true, "bundleName": "nwui.plugins.popper"}, {"input": "node_modules/bootstrap/dist/js/bootstrap.min.js", "inject": true, "bundleName": "nwui.plugins.bootstrap"}]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "neware": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.neware.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "neware-uicc-portal:build:production"}, "development": {"browserTarget": "neware-uicc-portal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "neware-uicc-portal:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "neware-uicc-portal"}