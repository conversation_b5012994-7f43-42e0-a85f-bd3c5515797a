{"i18n_nwui_components_dialog_loading": "正在加载...", "i18n_nwui_components_dialog_confirm": "确定", "i18n_nwui_components_dialog_cancel": "取消", "i18n_nwui_components_dialog_ok": "好的", "i18n_nwui_components_date_format": "YYYY-MM-DD", "i18n_nwui_components_date_buttons": "确定#取消", "i18n_nwui_components_date_weeks": "日#一#二#三#四#五#六", "i18n_nwui_components_date_months": "一月#二月#三月#四月#五月#六月#七月#八月#九月#十月#十一月#十二月", "i18n_nwui_components_date_ranges": "今天#昨天#最近7天#最近30天#这个月#上个月#自定义", "i18n_nwui_components_datetime_months": "一月#二月#三月#四月#五月#六月#七月#八月#九月#十月#十一月#十二月", "i18n_nwui_components_datetime_weeks": "一#二#三#四#五#六#日", "i18n_nwui_components_datetime_now": "现在", "i18n_nwui_components_datetime_apply": "确定", "i18n_nwui_components_datetime_cancel": "取消", "i18n_nwui_components_input_file_upload_success": "上传成功!", "i18n_nwui_components_input_file_upload_failed": "上传失败! 请重新上传或者重新选择文件", "i18n_nwui_components_input_file_over_maxsize": "选择的文件大小超限, 请重新选择! 最大可传: ${}, 实际大小: ${}", "i18n_nwui_components_input_multiple_placeholder": "输入并敲击确认健", "i18n_nwui_components_table_empty_result": "没有找到匹配的记录", "i18n_nwui_components_tree_table_empty_result": "无内容显示", "i18n_nwui_components_select_searching": "查询中...", "i18n_nwui_components_select_loading": "加载中...", "i18n_nwui_components_select_empty_result": "没有找到匹配的记录", "i18n_nwui_components_select_single_placeholder": "请选择...", "i18n_nwui_components_select_multiple_placeholder": "请选择或输入...", "i18n_nwui_components_select_pagination_info_uncompleted": "总共 ${} 条数据, 当前已加载 ${} 条数据", "i18n_nwui_components_select_pagination_info_completed": "总共 ${} 条数据, 当前已加载完毕", "i18n_nwui_components_select_tree_loading": "加载中...", "i18n_nwui_components_select_tree_empty_result": "无内容", "i18n_nwui_components_select_tree_placeholder": "请选择...", "i18n_nwui_components_select_tree_select_all": "全选", "i18n_nwui_components_pagination_pagesize": "显示第#到第#条记录, 总共#条记录 每页显示#条记录", "i18n_nwui_components_stepper_next": "下一步", "i18n_nwui_components_stepper_previous": "上一步", "i18n_nwui_components_stepper_finish": "完成", "i18n_nwui_integration_relogin_login_expired": "登录失效", "i18n_nwui_integration_relogin_relogin": "重新登录", "i18n_nwui_integration_relogin_switch_account": "切换账户", "i18n_nwui_integration_relogin_please_input_password": "请输入您的密码", "i18n_nwui_integration_relogin_login_error_retry": "登录失败，请重试", "i18n_nwui_integration_relogin_switch_error": "切换失败，请重试", "i18n_login": "登录", "i18n_username": "邮箱", "i18n_password": "密码", "i18n_header.logout": "登出", "i18n_header.changePassword": "修改密码", "i18n_language": "语言", "i18n_forgot_password": "忘记密码", "i18n_dashboard": "主页面板", "i18n_order": "订单", "i18n_order_create": "订单创建", "i18n_order_query": "订单查询", "i18n_resource": "资源管理", "i18n_sim": "SIM", "i18n_sim_query": "SIM查询", "i18n_sim_recycle": "SIM回收", "----i18n_public ----": "可公用部分相关", "i18n_public.action": "操作", "i18n_public.success": "操作成功", "i18n_public.error": "Error", "i18n_public.update.success": "更新成功", "i18n_public.jobResult": "查看结果", "i18n_public.nextBatch": "下一批", "i18n_public.query.result.condition": "查询条件", "i18n_public.query.result.title": "查询结果", "i18n_public.search.button": "查询", "i18n_public.reset.button": "重置", "i18n_public.submit.button": "提交", "i18n_public.save_all.button": "保存所有", "i18n_public.cancel.button": "取消", "i18n_public.write_off.button": "注销", "i18n_public.close.button": "关闭", "i18n_public.delete.button": "删除", "i18n_public.new.button": "新增", "i18n_public.edit.button": "编辑", "i18n_public.active.button": "激活", "i18n_public.lock.button": "锁定", "i18n_public.offline.button": "下线", "i18n_public.detail.button": "详情", "i18n_public.setting.button": "设定", "i18n_public.create.button": "新建", "i18n_public.invalid.button": "失效", "i18n_public.delete.tips": "您确定删除吗？", "i18n_public.terminate.tips": "您确定终止吗？", "i18n_public.write_off.tips": "您确定注销吗？", "i18n_public.unbind.tips": "您确定解绑吗？", "i18n_public.submit.tips": "您确定提交吗？", "i18n_public.download.tips": "您确定下载吗？", "i18n_public.publish.tips": "您确定发布吗？", "i18n_public.offline.tips": "您确定下线吗？", "i18n_public.edit.tips": "您确定编辑吗？", "i18n_public.create.tips": "您确定新建吗？", "i18n_public.lock.tips": "您确定锁定吗？", "i18n_public.unlock.tips": "您确定解锁吗？", "i18n_public.recovery.tips": "您确定回收吗？", "i18n_public.active.tips": "您确定激活吗？", "i18n_public.online.tips": "您确定上线吗？", "i18n_public.release.tips": "您确定发布吗？", "i18n_public.field.required": "这个字段是必须的。", "i18n_public.field.input.required": "这个字段是必须的。", "i18n_public.field.input.more.than.zero": "这个字段是必须的且大于0。", "i18n_public.table.single.select.switch.tips": "您确定要放弃当前操作吗？", "i18n_public.query.select.please-holder.tips": "请选择...", "i18n_public.query.input.please-holder.tips": "请输入...", "i18n_pub_tips": "提示", "i18n_public_support": "支持", "i18n_public_not_support": "不支持", "i18n_public.validator.mobile.length.error": "请输入 7-11 位数字或数字加+-组合。", "i18n_public.public.from.length_tips": "最多只允许输入${}个字符。", "i18n_public.validator.positive-integer.tips": "请输入大于0的正整数。", "i18n_public.validator.money.tips": "请输入正确的金额,最多允许三位小数。", "i18n_public.validator.money-three-decimal.tips": "请输入数字,最多允许三位小数。", "i18n_public.validator.positive-integer-range.tips": "请输入${}~${}的正整数。", "i18n_public.validator.natural-number.tips": "请输入大于0的整数。", "i18n_public.validator.max.tips": "最大允许输入999。", "i18n_public.validator.max.num.tips": "最大允许输入${}。", "i18n_public.validator.pattern": "请输入正确的格式。", "i18n_public.add.button": "添加", "i18n_public.unlimited.text": "无限制", "i18n_public.all.text": "All", "i18n_public.custom": "自定义", "i18n_public.select-a-record.tips": "请至少选择一条记录进行操作。", "i18n_public.select-a-card.tips": "请至少选择一个卡片进行操作。", "i18n_public.table.tag": "标签", "i18n_public.table.serial-number": "#", "i18n_public.text.yes": "是", "i18n_public.text.no": "否", "i18n_public.email.pattern": "请输入一个有效的邮箱地址", "i18n_public.download.button": "下载", "i18n_public.download.button.excel": "XLSX", "i18n_public.download.button.csv": "CSV", "i18n_public.download.button.txt": "TXT", "i18n_public.verify.product-select-max": "最多选择${}个产品。", "i18n_public.verify.date-range-max": "日期范围不能超过${}个月。", "i18n_public.undone-tips": "没有可操作的记录。", "i18n_public.selected-max-tips": "最多选中${}条记录进行操作。", "i18n_public.verify.imsi-regular": "请输入15位数字。", "i18n_public.verify.imei-regular": "请输入15位数字。", "i18n_public.verify.msisdn-regular": "请输入8-15位数字。", "i18n_public.verify.sim-regular": "请输入19或20位数字或字母。", "i18n_public.verify.num-rang": "请输入${}~${}范围内整数。", "i18n_public.tenant": "租户", "i18n_public.org": "组织机构", "i18n_public.status": "状态", "i18n_public.batch.job-id": "任务编号", "i18n_public.batch.job-name": "任务名称", "i18n_public.batch.remark": "备注", "i18n_public.batch.download-max": "当导出的记录数超出${}时, 将会以批量任务的方式异步下载。", "i18n_public.max.length": "最多只允许输入${}个字符。", "i18n_public.min.length": "最少输入${}个字符。", "i18n_public.batch.exec-mode.tips": "提示: 当批量任务数量超出${}时，建议“制定日程安排”。", "i18n_public.batch.file-upload.download": "下载样例文件,文件填充数据后需要通过PGP加密后上传", "i18n_public.batch.resource.range.max": "每批次最大不超过${}记录数/任务数。", "i18n_public.batch.batch-file": "批次文件", "i18n_public.batch.file.err.tips": "第${}行：${}", "i18n_public.batch.resource.begin-le-end": "${} 开始应该小于或等于 ${} 结束。", "i18n_public.batch.resource.end-ge-begin": "${} 结束应该大于或等于 ${} 开始。", "i18n_public.batch.schedule-time.tips": "计划时间不能早于当前时间。", "i18n_public.more": "更多", "----remark-merchant ----": "merchant 使用", "i18n_merchant.mgmt.query.title": "租户管理", "i18n_merchant.form.merchant": "租户", "i18n_merchant.form.status": "状态", "i18n_merchant.mgmt.form.merchant-name": "租户名称", "i18n_merchant.mgmt.form.status": "状态", "i18n_merchant.mgmt.from.remark": "备注", "i18n_merchant.mgmt.form.status-draft": "草稿", "i18n_merchant.mgmt.form.status-offline": "已下线", "i18n_merchant.mgmt.form.status-published": "已发布", "i18n_merchant.mgmt.table.tenant-id": "租户标识", "i18n_merchant.mgmt.table.tenant-code": "租户编码", "i18n_merchant.mgmt.table.name": "租户名称", "i18n_merchant.mgmt.table.alias": "租户简称", "i18n_merchant.mgmt.table.www": "网址", "i18n_merchant.mgmt.table.location": "地区", "i18n_merchant.mgmt.table.time-zone": "时区", "i18n_merchant.mgmt.table.currency": "币种", "i18n_merchant.mgmt.table.status": "状态", "i18n_merchant.mgmt.table.template": "模板", "i18n_merchant.mgmt.table.setting.template": "模板编辑", "i18n_merchant.mgmt.table.status.release": "上线", "i18n_merchant.mgmt.table.status.offline": "下线", "i18n_merchant.mgmt.table.status.unlocked": "解锁", "i18n_merchant.mgmt.table.status.delete": "删除", "i18n_merchant.mgmt.table.button.info": "详情", "i18n_merchant.mgmt.table.button.baseInfo": "完善基本信息", "i18n_merchant.mgmt.table.button.interface": "设置", "i18n_merchant.mgmt.table.button.authorization": "业务授权", "i18n_merchant.mgmt.table.button.new": "新建", "i18n_merchant.mgmt.table.button.edit": "编辑", "i18n_merchant.mgmt.table.button.publish": "上线", "i18n_merchant.mgmt.table.button.offline": "下线", "i18n_merchant.mgmt.table.button.delete": "删除", "i18n_merchant.mgmt.table.button.write-off": "注销", "i18n_merchant.mgmt.table.button.grant-deny": "菜单管理", "i18n_merchant.mgmt.table.button.err.secretkey": "字段只支持数字加字母", "i18n_merchant.mgmt.table.button.err.sftppath": "远程目录格式错误，需要以'/'开头", "i18n_merchant.mgmt.table.button.err.port": "请输入正确端口，范围 0 ~ 65535", "i18n_merchant.mgmt.table.button.setting": "设定", "i18n_merchant.mgmt.table.button.api.group": "接口组", "i18n_merchant.mgmt.table.button.setting.app": "APP管理", "i18n_merchant.mgmt.table.button.setting.cdr": "CDR管理", "i18n_merchant.mgmt.table.provider": "供应商", "i18n_merchant.mgmt.new.step.title.function-menu": "功能&菜单", "i18n_merchant.mgmt.new.step.title.chooseFuncAndMenu": "选择菜单及功能", "i18n_merchant.mgmt.new.step.title.merchant-admin": "租户管理员", "i18n_merchant.mgmt.new.step.title.create.merchant-admin": "创建管理员", "i18n_merchant.mgmt.new.tenant_id.error": "该租户编码已存在", "i18n_merchant.mgmt.new.tenant_id.pattern": "租户编码为两位大写字母", "i18n_merchant.mgmt.basic-info.tab.title.organization": "组织", "i18n_merchant.mgmt.basic-info.tab.title.menu": "菜单", "i18n_merchant.mgmt.basic-info.tab.title.role": "角色", "i18n_merchant.mgmt.basic-info.tab.title.staff": "员工", "i18n_merchant.mgmt.basic-info.org.orgName": "组织机构", "i18n_merchant.mgmt.basic-info.org.name": "姓名", "i18n_merchant.mgmt.basic-info.org.email": "邮箱", "i18n_merchant.mgmt.basic-info.org.roleName": "角色", "i18n_merchant.mgmt.basic-info.org.statusName": "状态", "i18n_merchant.mgmt.new.form.url.err": "请输入正确的网址, 比如https://www.xxxxxx.com。", "i18n_merchant.mgmt.new.form.email": "邮箱", "i18n_merchant.mgmt.new.form.admin-name": "管理员名称", "i18n_merchant.mgmt.new.form.password": "密码", "i18n_merchant.mgmt.new.form.confirm-password": "确认密码", "i18n_merchant.mgmt.new.form.mobile": "移动电话", "i18n_merchant.mgmt.new.form.fax": "传真", "i18n_merchant.mgmt.new.form.remark": "备注", "i18n_merchant.mgmt.new.form.menu-function.tips": "请至少选择一个菜单。", "i18n_merchant.mgmt.new.form.email.pattern": "请输入一个有效的邮箱地址。", "i18n_merchant.mgmt.new.form.password.format.charset": "密码由8~20位的数字、字母以及^&#?~!$<>'\"\\|+%/等特殊字符构成。", "i18n_merchant.mgmt.new.form.password.not_same": "俩次密码输入不一致。", "i18n_merchant.mgmt.grant.table.title.menu": "目录", "i18n_merchant.mgmt.grant.table.title.menu-grant": "菜单权限", "i18n_merchant.mgmt.grant.table.title.function-grant": "功能权限", "i18n_merchant.interface.query.title": "租户接入", "i18n_merchant.interface.form.merchant": "租户", "i18n_merchant.interface.form.app-name": "APP名称", "i18n_merchant.interface.form.merchant-name": "租户名称", "i18n_merchant.interface.form.status": "状态", "i18n_merchant.interface.form.status-all": "-- 所有 --", "i18n_merchant.interface.table.merchant-name": "租户", "i18n_merchant.interface.table.organization": "发展渠道", "i18n_merchant.interface.table.detail": "APP 资料", "i18n_merchant.interface.table.appid": "APP ID", "i18n_merchant.interface.table.name": "APP名称", "i18n_merchant.interface.table.sub-domain": "子域名", "i18n_merchant.interface.table.version": "API版本", "i18n_merchant.interface.table.domain": "子域名", "i18n_merchant.interface.table.group": "API分组", "i18n_merchant.interface.table.status": "状态", "i18n_merchant.interface.table.status.update.time": "更新时间", "i18n_merchant.interface.table.description": "备注", "i18n_merchant.interface.table.button.info": "基本信息", "i18n_merchant.interface.table.button.new": "新建", "i18n_merchant.interface.table.button.edit": "编辑", "i18n_merchant.interface.table.button.publish": "发布", "i18n_merchant.interface.table.button.offline": "下架", "i18n_merchant.interface.table.button.grant-deny": "授权/回收", "i18n_merchant.interface.table.button.help": "帮助", "i18n_merchant.interface.table.error": "表单有误，请检查！", "i18n_merchant.interface.basic-info.title": "租户接口详情", "i18n_merchant.interface.basic-info.tab.title.basic-info": "填写APP资料", "i18n_merchant.interface.basic-info.tab.title.interface-list": "API授权", "i18n_merchant.interface.basic-info.tab.title.app.info": "APP 资料", "i18n_merchant.interface.basic-info.tab.title.api.List": "API 列表", "i18n_merchant.interface.basic-info.tab.title.error-code": "错误码", "i18n_merchant.interface.basic-info.merchant": "租户", "i18n_merchant.interface.basic-info.orgName": "组织机构", "i18n_merchant.cdr.basic-info.orgName": "组织机构", "i18n_merchant.cdr.new.form.index": "序号", "i18n_merchant.cdr.new.form.password": "密码", "i18n_merchant.interface.basic-info.auth-key": "鉴权钥匙", "i18n_merchant.interface.basic-info.domain": "子域名", "i18n_merchant.interface.basic-info.version": "接口版本", "i18n_merchant.interface.basic-info.ip-list": "可访问的IP列表", "i18n_merchant.interface.basic-info.description": "备注", "i18n_merchant.interface.basic-info.error-code.table.code": "错误码", "i18n_merchant.interface.basic-info.error-code.table.description": "备注", "i18n_merchant.interface.basic-info.index": "序号", "i18n_merchant.interface.basic-info.password": "密码", "i18n_merchant.interface.api.title.security": "传输安全", "i18n_merchant.interface.api.title.secretKey": "密钥", "i18n_merchant.interface.api.title.info": "详情", "i18n_merchant.interface.api.title.view": "查看", "i18n_merchant.interface.api.button.grant": "授权", "i18n_merchant.interface.api.button.org.grant": "授权组织机构", "i18n_merchant.interface.api.button.deny": "回收", "i18n_merchant.interface.api.button.save": "保存", "i18n_merchant.interface.api.button.modify": "修改并保存", "i18n_merchant.interface.api.table.name": "接口名称", "i18n_merchant.interface.api.table.API": "API", "i18n_merchant.interface.api.table.url": "URL", "i18n_merchant.interface.api.table.grant": "授予 / 回收", "i18n_merchant.interface.api.table.status-grant": "已授权 ", "i18n_merchant.interface.api.table.status-deny": "未授权", "i18n_merchant.interface.api.label.interface-name": "接口名称", "i18n_merchant.interface.api.label.url": "访问链接", "i18n_merchant.interface.api.label.feedback-url": "回调地址", "i18n_merchant.interface.api.label.feedback-url-error": "请输入正确的回调地址,例如：https://www.xxx.com/api/callback。", "i18n_merchant.interface.api.label.subDomain": "子域名仅允许输入数字和字母，不允许其他符号。", "i18n_merchant.interface.api.label.request-template": "请求参数", "i18n_merchant.interface.api.label.response-template": "返回参数", "i18n_merchant.interface.api.label.response-template.view": "查看返回参数", "i18n_merchant.interface.api.label.description": "备注", "i18n_merchant.interface.new.title": "新建API账号", "i18n_merchant.interface.new.step.title.basic-info": "基本信息", "i18n_merchant.interface.new.step.title.interface-list": "接口列表", "i18n_merchant.interface.new.form.merchant": "租户", "i18n_merchant.interface.new.form.organization": "组织机构", "i18n_merchant.interface.new.form.organization-unlimited": "不限制", "i18n_merchant.interface.new.form.domain": "子域名", "i18n_merchant.interface.new.form.version": "接口版本", "i18n_merchant.interface.new.form.ip-list": "可访问的IP列表", "i18n_merchant.interface.new.form.ip-list-unlimited": "不限制", "i18n_merchant.interface.new.form.tps_tpd_title": "TPS/TPD", "i18n_merchant.interface.new.form.tps_title": "/秒", "i18n_merchant.interface.new.form.zero_tip": "0为无限制", "i18n_merchant.interface.new.form.tpd_title": "/天", "i18n_merchant.interface.new.form.tps": "请输入0-10000内的数字", "i18n_merchant.interface.new.form.tpd": "请输入0-100000000内的数字", "i18n_merchant.interface.new.form.description": "备注", "i18n_merchant.interface.new.form.domain.error": "子域名已存在", "i18n_merchant.interface.new.form.ip-list.error": "鉴权IP列表格式错误", "i18n_merchant.interface.new.form.ip-list.tips": "鉴权IP列表格式如 '***************,..,192.168.200.xxx'", "i18n_merchant.interface.new.form.feedbackUrl.tips": "请输入正确的回调地址, 比如https://www.xxxxxx.com。", "i18n_merchant.interface.new.form.ip.record.error": "至少选中一条记录", "i18n_merchant.interface.edit.step.title": "编辑", "i18n_merchant.interface.edit.form.merchant": "租户", "i18n_merchant.interface.edit.form.organization": "组织机构 ", "i18n_merchant.interface.edit.form.auth-key": "鉴权钥匙", "i18n_merchant.interface.edit.form.domain": "子域名", "i18n_merchant.interface.edit.form.version": "接口版本", "i18n_merchant.interface.edit.form.ip-list": "可访问的IP列表", "i18n_merchant.interface.edit.form.description": "备注", "i18n_merchant.interface.form.domain.error": "子域名已存在", "i18n_merchant.interface.param": "接口参数", "i18n_merchant.org.panel.title": "组织机构", "i18n_merchant.org.tenant": "租户", "i18n_merchant.org.org": "组织机构", "i18n_merchant.org.info.title": "基本信息", "i18n_merchant.org.tree.tips": "提示：所选节点不支持创建下一级组织机构。", "i18n_merchant.org.info.parent-org": "上级组织", "i18n_merchant.org.info.org": "组织机构", "i18n_merchant.org.info.remark": "备注", "i18n_merchant.org.info.status": "状态", "i18n_merchant.org.public.status_in_active": "未激活", "i18n_merchant.org.public.status_active": "激活", "i18n_merchant.org.public.status_locked": "锁定", "i18n_merchant.org.org.required": "请输入组织机构。", "i18n_merchant.org.status.required": "请选择状态。", "i18n_merchant.org.remark.max_length": "最多只允许输入300个字符。", "i18n_merchant.org.staff.title": "工号", "i18n_merchant.org.staff.table.orgName": "组织机构", "i18n_merchant.org.staff.table.name": "姓名", "i18n_merchant.org.staff.table.email": "邮箱", "i18n_merchant.org.staff.table.roleName": "角色", "i18n_merchant.org.staff.table.statusName": "状态", "i18n_merchant.org.new.title": "新建", "i18n_merchant.org.new.parent-org": "上级组织", "i18n_merchant.org.new.org": "组织机构", "i18n_merchant.org.new.remark": "备注", "i18n_merchant.org.edit.title": "编辑", "i18n_merchant.org.edit.parent-org": "上级组织", "i18n_merchant.org.edit.org": "组织机构", "i18n_merchant.org.edit.remark": "备注", "i18n_merchant.org.delete.title": "删除", "i18n_merchant.org.info.lock.button": "锁定", "i18n_merchant.org.info.unlock.button": "解锁", "i18n_merchant.org.info.active.button": "激活", "----  system_logs_app  ----": "", "i18n_system_logs_app.header": "查询条件", "i18n_system_logs_app.result": "查询结果", "i18n_system_logs_app.tenant": "租户名称", "i18n_system_logs_app.resources": "IMSI / SIM / MSISDN", "i18n_system_logs_app.appName": "APP", "i18n_system_logs_app.apiCode": "API", "i18n_system_logs_app.invokeId": "交易ID", "i18n_system_logs_app.createDate": "开始日期 - 结束日期", "i18n_system_logs_app.feedBackDate": "回调开始日期 - 回调结束日期", "i18n_system_logs_app.feedBackTime": "回调时间", "i18n_system_logs_app.resources.start": "开始 IMSI / SIM / MSISDN", "i18n_system_logs_app.resources.end": "结束 IMSI / SIM / MSISDN", "i18n_system_logs_app.sim.start": "SIM 开始", "i18n_system_logs_app.sim.end": "SIM 结束", "i18n_system_logs_app.msisdn.start": "MSISDN 开始", "i18n_system_logs_app.msisdn.end": "MSISDN 结束", "i18n_system_logs_app.resources.err": "必须为数字类型", "i18n_system_logs_app.table.button.result": "回调信息", "i18n_system_logs_app.table.button.invoke": "请求信息", "i18n_system_logs_app.table.button.detail": "详情", "i18n_system_logs_app.table.tenant": "租户", "i18n_system_logs_app.table.org": "组织机构", "i18n_system_logs_app.table.msisdn": "MSISDN", "i18n_system_logs_app.table.imsi": "IMSI", "i18n_system_logs_app.table.sim": "SIM", "i18n_system_logs_app.table.apiCode": "API", "i18n_system_logs_app.table.appId": "APP ID", "i18n_system_logs_app.table.appName": "APP", "i18n_system_logs_app.table.status": "状态", "i18n_system_logs_app.table.invokeId": "交易ID", "i18n_system_logs_app.table.createTime": "操作时间", "i18n_system_logs_app.table.request": "回调请求参数模板", "i18n_system_logs_app.table.response": "回调返回参数模板", "i18n_system_logs_app.request.url": "请求链接", "i18n_system_logs_app.request.ip": "访问IP", "i18n_system_logs_app.result.code": "错误码", "i18n_system_logs_app.result.id": "回调ID", "i18n_system_logs_app.result.adress": "回调地址", "i18n_system_logs_app.result.request": "回调请求", "i18n_system_logs_app.result.message": "结果信息", "i18n_system_logs_app.download.resourcesType": "资源类型", "i18n_system_logs_app.table.status.success": "成功", "i18n_system_logs_app.table.status.failure": "失败", "----remark-privilege----": "Privilege 使用", "i18n_privilege.job": "我的任务", "i18n_privilege.detail": "详情", "i18n_privilege.password": "密码", "i18n_privilege.change_password": "修改密码", "i18n_privilege.original_password": "原始密码", "i18n_privilege.new_password": "新密码", "i18n_privilege.password.unequal": "两次输入的密码不一致。", "i18n_privilege.password.format": "密码由8~20位的数字、字母以及^&#?~!$<>'\"\\|+%/等特殊字符构成。", "i18n_privilege.confirm_password": "确认密码", "i18n_privilege.email.pattern": "请输入一个有效的邮箱地址。", "i18n_privilege.max.length": "最多只允许输入${}个字符。", "i18n_privilege.fax.pattern": "请输入一个有效的传真地址。", "i18n_privilege.fax": "传真", "i18n_privilege.address": "地址", "i18n_privilege.description": "描述", "i18n_privilege.tenant": "租户", "i18n_privilege.name": "姓名", "i18n_privilege.alias": "别名", "i18n_privilege.email": "邮箱", "i18n_privilege.mobile": "号码", "i18n_privilege.org": "组织机构", "i18n_privilege.status": "状态", "i18n_privilege.role": "角色", "i18n_privilege.last_login_time": "最后登录时间", "i18n_privilege.freeze": "冻结", "i18n_privilege.remark": "备注", "i18n_privilege.staff.status_active": "激活", "i18n_privilege.staff.status_locked": "锁定", "i18n_privilege.create_staff": "新建工号", "i18n_privilege.new": "新建", "i18n_privilege.edit": "编辑", "i18n_privilege.delete": "删除", "i18n_privilege.lock": "锁定/解锁", "i18n_privilege.reset_password": "重置密码", "i18n_privilege.choose.way": "请为账号 ", "i18n_privilege.thaw": "解冻", "i18n_privilege.reset.way": "选择密码重置方式", "i18n_privilege.password.not_same": "俩次密码输入不一致。", "i18n_privilege.error": "操作失败", "i18n_privilege.success": "操作成功", "---- privilege_staff ----": "", "i18n_privilege_staff.detail": "详情", "i18n_privilege_staff.password": "密码", "i18n_privilege_staff.password.format": "密码由8~20位的数字、字母以及^&#?~!$<>'\"\\|+%/等特殊字符构成。", "i18n_privilege_staff.confirm_password": "确认密码", "i18n_privilege_staff.email.pattern": "请输入一个有效的邮箱地址。", "i18n_privilege_staff.max.length": "最多只允许输入${}个字符。", "i18n_privilege_staff.fax.pattern": "请输入一个有效的传真地址。", "i18n_privilege_staff.fax": "传真", "i18n_privilege_staff.address": "地址", "i18n_privilege_staff.description": "描述", "i18n_privilege_staff.tenant": "租户", "i18n_privilege_staff.name": "姓名", "i18n_privilege_staff.alias": "别名", "i18n_privilege_staff.email": "邮箱", "i18n_privilege_staff.mobile": "号码", "i18n_privilege_staff.org": "组织机构", "i18n_privilege_staff.status": "状态", "i18n_privilege_staff.role": "角色", "i18n_privilege_staff.last_login_time": "最后登录时间", "i18n_privilege_staff.freeze": "冻结", "i18n_privilege_staff.remark": "备注", "i18n_privilege_staff.staff.status_active": "激活", "i18n_privilege_staff.staff.status_locked": "锁定", "i18n_privilege_staff.create_staff": "新建工号", "i18n_privilege_staff.new": "新建", "i18n_privilege_staff.edit": "编辑", "i18n_privilege_staff.delete": "删除", "i18n_privilege_staff.lock": "锁定/解锁", "i18n_privilege_staff.reset_password": "重置密码", "i18n_privilege_staff.choose.way": "请为账号 ", "i18n_privilege_staff.thaw": "解冻", "i18n_privilege_staff.reset.way": "选择密码重置方式", "i18n_privilege_staff.password.not_same": "俩次密码输入不一致。", "---- privilege_role ----": "", "i18n_privilege_role.tenant": "租户", "i18n_privilege_role.role.tree.label": "角色", "i18n_privilege_role.info.title": "基本信息", "i18n_privilege_role.info.role.name": "角色名", "i18n_privilege_role.info.role.status": "状态", "i18n_privilege_role.info.role.remark": "备注", "i18n_privilege_role.info.status.active": "激活", "i18n_privilege_role.new.title": "新建", "i18n_privilege_role.new.role.name": "角色名", "i18n_privilege_role.new.role.remark": "备注", "i18n_privilege_role.edit.title": "编辑", "i18n_privilege_role.edit.role.name": "角色名", "i18n_privilege_role.edit.role.remark": "备注", "i18n_privilege_role.delete.title": "删除", "i18n_privilege_role.grant.menu.title": "菜单", "i18n_privilege_role.grant.deny.title": "菜单", "i18n_privilege_role.grant.panel": "拥有权限", "i18n_privilege_role.grant.title.menu": "菜单目录", "i18n_privilege_role.grant.title.menu.grant": "菜单权限", "i18n_privilege_role.grant.title.func": "功能权限", "i18n_privilege_role.staff.title": "员工", "i18n_privilege_role.staff.table.orgName": "组织机构", "i18n_privilege_role.staff.table.name": "员工", "i18n_privilege_role.staff.table.email": "电子邮箱", "i18n_privilege_role.staff.table.roleName": "角色", "i18n_privilege_role.staff.table.statusName": "状态", "i18n_privilege_role.staff.table.status.inactive": "未激活", "i18n_privilege_role.staff.table.status.active": "激活", "i18n_privilege_role.staff.table.status.locked": "锁定", "i18n_privilege_role.public.from.length_tips": "最多只允许输入${}个字符。", "i18n_privilege_role.public.button.submit": "提交", "i18n_privilege_role.public.button.cancel": "取消", "------ system_logs_login   --------- ": "", "i18n_system_logs_login.title": "登录日志", "i18n_system_logs_login.form.mvno": "租户", "i18n_system_logs_login.form.date": "开始日期 - 结束日期", "i18n_system_logs_login.form.email": "邮箱", "i18n_system_logs_login.table.org": "组织机构", "i18n_system_logs_login.table.email": "邮箱", "i18n_system_logs_login.table.login-ip": "登录IP", "i18n_system_logs_login.table.login-area": "登录地区", "i18n_system_logs_login.table.login-result": "登录结果", "i18n_system_logs_login.table.login-time": "登录时间", "i18n_system_logs_login.table.tenant-name": "租户", "i18n_system_logs_login.table.logout-time": "登出时间", "i18n_system_logs_login.table.title": "查询结果", "i18n_system_logs_login.download.tip": "请点击查询后下载。", "i18n_system_logs_login.download.button": "下载", "------ system_logs_operation   --------- ": "", "i18n_system_logs_operation.title": "操作日志", "i18n_system_logs_operation.form.mvno": "租户", "i18n_system_logs_operation.form.date": "开始结束时间", "i18n_system_logs_operation.form.email": "邮箱", "i18n_system_logs_operation.table.title": "查询结果", "i18n_system_logs_operation.table.mvno": "租户", "i18n_system_logs_operation.table.business-sn": "任务编号", "i18n_system_logs_operation.table.bussiness_type": "业务类型", "i18n_system_logs_operation.table.org": "组织机构", "i18n_system_logs_operation.table.email": "邮箱", "i18n_system_logs_operation.table.request_time": "操作时间", "i18n_system_logs_operation.table.status_name": "操作结果", "i18n_system_logs_operation.table.error.message": "错误信息", "i18n_system_logs_operation.download.tip": "请点击查询后下载。", "i18n_system_logs_operation.download.button": "下载", "------ system_cache   --------- ": "", "i18n_system_cache.refresh.all": "刷新全部", "i18n_system_cache.refresh": "刷新", "i18n_system_cache.cache.reload": "缓存重载", "i18n_system_cache.cache.code": "缓存编码", "i18n_system_cache.cache.name": "缓存名称", "i18n_system_cache.last.refresh.time": "最晚刷新时间", "i18n_system_cache.operator": "操作者", "i18n_system_cache.operator.no.record": "请选择一条记录", "i18n_system_cache.cache.group": "缓存组", "------ task   --------- ": "", "i18n_system_task.run": "立即执行", "i18n_system_task.edit": "编辑", "i18n_system_task.resume": "恢复", "i18n_system_task.pause": "暂停", "i18n_system_task.refresh.all": "刷新全部", "i18n_system_task.table.group": "任务组", "i18n_system_task.table.name": "任务名称", "i18n_system_task.table.status": "任务状态", "i18n_system_task.table.cron.expression": "Cron 表达式", "i18n_system_task.table.cron.bean": "<PERSON><PERSON>", "i18n_system_task.table.cron.remark": "备注", "i18n_system_task.schedule.console": "定时任务", "i18n_system_task.schedule.job": "定时任务", "i18n_system_task.edit_title": "编辑", "i18n_system_task.edit_name": "任务名称", "i18n_system_task.edit_expression": "Cron 表达式", "-------  System Setting ----------": "", "i18n_system_setting": "系统设定", "i18n_system_setting_login_title1": "登录", "i18n_system_setting_login_title2": "登录", "i18n_system_setting_login_value_err1": "请输入长度32位16进制秘钥", "-------  Log Mgmt ----------": "", "i18n_tag_mgmt_code": "标签编码", "i18n_tag_mgmt_category": "标签类型", "i18n_tag_mgmt_checked": "勾选", "i18n_tag_mgmt_description": "描述", "i18n_tag_mgmt_cannot_be_deleted_root_tips": "无法删除根节点！", "i18n_tag_mgmt_exsis_profile_tips": "该标签存在关联资源，是否删除关联资源？", "i18n_tag_mgmt_un_create_tips": "无法定义多级标签", "i18n_tag_mgmt_sys_one_tag": "系统预定义单层标签", "i18n_tag_mgmt_sys_tree_tag": "系统预定义树型标签", "i18n_tag_mgmt_busi_one_tag": "业务定义单层标签", "i18n_tag_mgmt_busi_tree_tag": "业务定义树型标签", "i18n_tag_mgmt_value": "值", "i18n_tag_mgmt_profile_tag": "资源标签", "i18n_tag_mgmt_tag_template": "标签模板", "i18n_tag_mgmt_tag_detail": "标签详情", "i18n_tag_mgmt_tag_un_checked_tip": "未勾选标签！", "----i18n_export ----": "导出相关", "i18n_export_title_info": "导出信息", "i18n_export_title_records": "导出记录", "i18n_export_job_id": "批次ID", "i18n_export_gpg_key": "GPG公钥", "i18n_export_format": "文件导出格式", "i18n_export_remark": "备注", "i18n_export_separator": "分隔符", "i18n_export_field": "导出字段", "i18n_export_expired_time": "过期时间", "i18n_export_button": "导出", "i18n_export_by-query-checked": "按选中导出", "i18n_export_by-query-all": "按结果导出", "i18n_export_by-file": "按文件导出", "i18n_export_public_key_tips1": "如找不到目标公钥，请前往", "i18n_export_public_key_tips2": "中新增。", "i18n_export_public_key_my_key": "我的公钥", "i18n_public.export.button_tip1": "暂无查询结果可下载！", "i18n_public.export.button_tip2": "请重新点击查询后再下载！", "----console_jobs----": "console_jobs 使用", "i18n_console_jobs.schedule.console": "定时任务", "i18n_console_jobs.schedule.job": "定时任务", "i18n_console_jobs.batch.job": "批量任务", "i18n_console_jobs.run": "立即执行", "i18n_console_jobs.edit": "编辑", "i18n_console_jobs.resume": "恢复", "i18n_console_jobs.pause": "暂停", "i18n_console_jobs.delete": "删除", "i18n_console_jobs.refresh.all": "刷新全部", "i18n_console_jobs.refresh": "刷新", "i18n_console_jobs.task.group": "任务组", "i18n_console_jobs.task.name": "任务名称", "i18n_console_jobs.task.status": "任务状态", "i18n_console_jobs.cron.expression": "Cron 表达式", "i18n_console_jobs.tenant": "租户", "i18n_console_jobs.start.end": "开始日期 - 结束日期", "i18n_console_jobs.business.name": "业务名称", "i18n_console_jobs.batch.id": "任务编号", "i18n_console_jobs.batch.name": "任务名称", "i18n_console_jobs.start.time": "开始时间", "i18n_console_jobs.create.time": "创建时间", "i18n_console_jobs.create.by": "创建人", "i18n_console_jobs.finish.time": "完成时间", "i18n_console_jobs.status": "状态", "i18n_console_jobs.serial": "业务流水", "i18n_console_jobs.error.reason": "错误信息", "i18n_console_jobs.item.keyword": "子项编码/内容", "i18n_console_logs.user.item": "子项", "i18n_console_jobs.vendor": "供应商", "i18n_console_jobs.type": "任务类型", "i18n_console_jobs.mnc": "MNC", "i18n_console_jobs.maker": "制造商", "i18n_console_jobs.capacity": "容量", "i18n_console_jobs.card.type": "分类", "i18n_console_jobs.table.query.title": "查询结果", "i18n_console_jobs.table.cancel.task": "取消任务", "i18n_console_jobs.table.adjust.task.time": "调整日程安排", "i18n_console_jobs.cancel.task.tips": "确定取消任务吗？", "i18n_console_jobs.cancel.task.success.tips": "取消任务成功！", "i18n_console_jobs.cancel.task.failure.tips": "取消任务失败！", "i18n_console_jobs.edit.task.time.tips": "确认调整执行时间吗？", "i18n_console_jobs.edit.task.time.success.tips": "调整执行时间成功！", "i18n_console_jobs.edit.task.time.failure.tips": "调整执行时间失败！任务状态需为待处理。", "i18n_console_jobs.edit.task.time.info.null.tips": "执行日期不可为空！", "i18n_console_jobs.edit.batchId.null.tips": "请选择一条记录！", "i18n_console_jobs.remark": "备注", "i18n_console_jobs.detail.job-info": "任务信息", "i18n_console_jobs.detail.query-params": "查询参数", "i18n_console_jobs.detail.checked-list": "选中列表", "i18n_console_jobs.detail.business-data": "业务数据", "i18n_console_jobs.detail.range": "号段范围", "i18n_console_jobs.detail.uploaded-file": "上传文件", "i18n_console_jobs.detail.checked-item": "选中项", "i18n_console_jobs.detail.target-tenant": "目标租户", "i18n_console_jobs.detail.target-org": "目标组织机构", "i18n_console_jobs.detail.sim.target-maker": "目标制造商", "i18n_console_jobs.detail.sim.target-type": "目标分类", "i18n_console_jobs.detail.sim.target-capacity": "目标容量", "i18n_console_jobs.detail.msisdn.target-msisdnType": "目标MSISDN类型", "i18n_console_jobs.detail.resource.type": "资源类型", "i18n_console_jobs.detail.resource.sim-begin": "SIM 开始", "i18n_console_jobs.detail.resource.sim-end": "SIM 结束", "i18n_console_jobs.detail.resource.imsi-begin": "IMSI 开始", "i18n_console_jobs.detail.resource.imsi-end": "IMSI 结束", "i18n_console_jobs.detail.resource.msisdn-begin": "MSISDN 开始", "i18n_console_jobs.detail.resource.msisdn-end": "MSISDN 结束", "i18n_console_jobs.detail.resource.begin": "开始", "i18n_console_jobs.detail.resource.end": "结束", "i18n_console_jobs.detail.busi.tenant": "租户", "i18n_console_jobs.detail.busi.org": "组织机构", "i18n_console_jobs.detail.busi.brand": "品牌", "i18n_console_jobs.detail.busi.vendor": "供应商", "i18n_console_jobs.detail.busi.cs-status": "CS域", "i18n_console_jobs.detail.busi.sim": "SIM", "i18n_console_jobs.detail.busi.imsi": "IMSI", "i18n_console_jobs.detail.busi.msisdn": "MSISDN", "i18n_console_jobs.cache.reload": "缓存重载", "i18n_console_jobs.cache.code": "缓存编码", "i18n_console_jobs.cache.name": "缓存名称", "i18n_console_jobs.last.refresh.time": "最晚刷新时间", "i18n_console_jobs.operator": "操作者", "i18n_console_jobs.operator.no.record": "请选择一条记录", "i18n_console_jobs.cache.group": "缓存组", "i18n_console_logs.batch.logs": "批量日志", "i18n_console_logs.submit.time": "提交时间", "i18n_console_logs.start.time": "开始时间", "i18n_console_logs.schedule.time": "预约时间", "i18n_console_logs.operation.result": "总计 | 成功 | 等待 | 失败 ", "i18n_console_logs.operation.details.result": "操作结果", "i18n_console_logs.finish.time": "完成时间", "i18n_console_logs.total": "合计", "i18n_console_logs.success.cnt": "成功数量", "i18n_console_logs.fail.cnt": "失败数量", "i18n_console_logs.batch.item": "子项编码", "i18n_console_logs.content": "内容", "i18n_console_logs.result": "处理结果", "i18n_console_logs.failure": "失败", "i18n_console_logs.download_tips": "没有需要下载的记录", "i18n_console_logs.user.business": "业务类型", "i18n_console_jobs.export.file": "下载目标文件", "i18n_console_logs.user.detail.total": "总数", "i18n_console_logs.user.detail.success": "成功", "i18n_console_logs.user.detail.failure": "失败", "i18n_console_logs.user.detail.warning": "等待", "----factory----": "factory 使用", "i18n_production.create.name": "创建工号", "i18n_production.create.time": "创建时间", "i18n_production.update.name": "更新工号", "i18n_production.update.time": "更新时间", "i18n_production.key.word": "关键字", "i18n_factory.form.name": "工厂名称", "i18n_factory.form.short.name": "工厂名称缩写", "i18n_factory.form.contact.name": "联系人", "i18n_factory.form.contact.phone": "联系电话", "----customer----": "customer 使用", "i18n_customer.form.name": "客户名称", "i18n_customer.form.short.name": "客户简称", "i18n_customer.form.contact.name": "客户联系人", "i18n_customer.form.contact.phone": "客户联系电话", "----email----": "email 使用", "i18n_email.form.operation": "业务编码", "i18n_email.form.subject": "邮件标题", "i18n_email.form.send.to": "收件人", "i18n_email.form.send.by": "发送人", "i18n_email.form.cc.to": "抄送人", "i18n_email.form.content": "邮件内容模板", "----key----": "key 使用", "i18n_key.form.operation": "业务编码", "i18n_key.form.key.type": "密钥类型", "i18n_key.form.key.alg": "算法", "i18n_key.form.key.file": "密钥文件"}