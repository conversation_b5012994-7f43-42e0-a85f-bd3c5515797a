{"i18n_nwui_components_dialog_loading": "Loading...", "i18n_nwui_components_dialog_confirm": "Confirm", "i18n_nwui_components_dialog_cancel": "Cancel", "i18n_nwui_components_dialog_ok": "OK", "i18n_nwui_components_date_format": "MM/DD/YYYY", "i18n_nwui_components_date_buttons": "Apply#Cancel", "i18n_nwui_components_date_weeks": "Su#Mo#Tu#We#Th#Fr#Sa", "i18n_nwui_components_date_months": "January#February#March#April#May#June#July#August#September#October#November#December", "i18n_nwui_components_date_ranges": "Today#Yesterday#Last 7 Days#Last 30 Days#This Month#Last Month#Custom", "i18n_nwui_components_datetime_months": "Jan#Feb#Mar#April#May#Jun#Jul#Aug#Sep#Oct#Nov#Dec", "i18n_nwui_components_datetime_weeks": "Mo#Tu#We#Th#Fr#Sa#Su", "i18n_nwui_components_datetime_now": "Now", "i18n_nwui_components_datetime_apply": "Apply", "i18n_nwui_components_datetime_cancel": "Cancel", "i18n_nwui_components_input_file_upload_success": "Uploaded successfully!", "i18n_nwui_components_input_file_upload_failed": "Uploaded failed! Please re-upload or re-select file.", "i18n_nwui_components_input_file_over_maxsize": "File size exceeds the limit, re-select again please! Maxsize limit: ${}, your file size: ${}", "i18n_nwui_components_input_multiple_placeholder": "Type sth and press the Enter key", "i18n_nwui_components_table_empty_result": "No matching records found", "i18n_nwui_components_tree_table_empty_result": "No records found", "i18n_nwui_components_select_searching": "Searching...", "i18n_nwui_components_select_loading": "Loading...", "i18n_nwui_components_select_empty_result": "No matches found", "i18n_nwui_components_select_single_placeholder": "Please select...", "i18n_nwui_components_select_multiple_placeholder": "Please select or input...", "i18n_nwui_components_select_pagination_info_uncompleted": "Total of ${} rows, currently loaded ${} rows", "i18n_nwui_components_select_pagination_info_completed": "Total of ${} rows, currently loaded all", "i18n_nwui_components_select_tree_loading": "Loading...", "i18n_nwui_components_select_tree_empty_result": "No record", "i18n_nwui_components_select_tree_placeholder": "Please select...", "i18n_nwui_components_select_tree_select_all": "Select All", "i18n_nwui_components_pagination_pagesize": "Showing#to#of#rows#rows per page", "i18n_nwui_components_stepper_next": "Next", "i18n_nwui_components_stepper_previous": "Previous", "i18n_nwui_components_stepper_finish": "Finish", "i18n_nwui_integration_relogin_login_expired": "Login Expired", "i18n_nwui_integration_relogin_relogin": "<PERSON><PERSON><PERSON><PERSON>", "i18n_nwui_integration_relogin_switch_account": "Switch Account", "i18n_nwui_integration_relogin_please_input_password": "Please input your password", "i18n_nwui_integration_relogin_login_error_retry": "<PERSON><PERSON> failed, please retry", "i18n_nwui_integration_relogin_switch_error": "Switch failed, please retry", "i18n_login": "<PERSON><PERSON>", "i18n_username": "Email", "i18n_password": "Password", "i18n_header.logout": "Logout", "i18n_header.changePassword": "Change Password", "i18n_language": "Language", "i18n_forgot_password": "Forgot password", "i18n_dashboard": "Dashboard", "i18n_order": "Order", "i18n_order_create": "Order Create", "i18n_order_query": "Order Query", "i18n_resource": "Resource", "i18n_sim": "SIM", "i18n_sim_query": "SIM Query", "i18n_sim_recycle": "SIM Recycle", "----i18n_public ----": "可公用部分相关", "i18n_public.action": "Action", "i18n_public.success": "Operation Successful", "i18n_public.error": "Operation Error", "i18n_public.update.success": "Update Success", "i18n_public.jobResult": "Query Result", "i18n_public.nextBatch": "Next One", "i18n_public.query.result.condition": "Condition", "i18n_public.query.result.title": "Result", "i18n_public.search.button": "Query", "i18n_public.reset.button": "Reset", "i18n_public.submit.button": "Submit", "i18n_public.save_all.button": "Save All", "i18n_public.cancel.button": "Cancel", "i18n_public.write_off.button": "Write Off", "i18n_public.close.button": "Close", "i18n_public.delete.button": "Delete", "i18n_public.new.button": "New", "i18n_public.edit.button": "Edit", "i18n_public.active.button": "Active", "i18n_public.lock.button": "Lock", "i18n_public.offline.button": "Offline", "i18n_public.detail.button": "Details", "i18n_public.setting.button": "Setting", "i18n_public.create.button": "New", "i18n_public.invalid.button": "Expire", "i18n_public.delete.tips": "Are you sure to delete？", "i18n_public.terminate.tips": "Are you sure you want to terminate?", "i18n_public.write_off.tips": "Are you sure to write off？", "i18n_public.unbind.tips": "Are you sure to unbind？", "i18n_public.submit.tips": "Are you sure to submit？", "i18n_public.download.tips": "Are you sure to download？", "i18n_public.publish.tips": "Are you sure to publish？", "i18n_public.offline.tips": "Are you sure to offline？", "i18n_public.edit.tips": "Are you sure to edit？", "i18n_public.create.tips": "Are you sure to create？", "i18n_public.lock.tips": "Are you sure to lock？", "i18n_public.unlock.tips": "Are you sure to unlock？", "i18n_public.recovery.tips": "Are you sure to recovery？", "i18n_public.active.tips": "Are you sure to active？", "i18n_public.online.tips": "Are you sure to online？", "i18n_public.release.tips": "Are you sure to release？", "i18n_public.field.required": "This field is required.", "i18n_public.field.input.required": "This field is required.", "i18n_public.field.input.more.than.zero": "This field is required and greater than 0.", "i18n_public.table.single.select.switch.tips": "Are you sure to give up this operation?", "i18n_public.query.select.please-holder.tips": "Please select ...", "i18n_public.query.input.please-holder.tips": "Please enter...", "i18n_pub_tips": "Tips", "i18n_public_support": "Support", "i18n_public_not_support": "Not Support", "i18n_public.validator.mobile.length.error": "Please enter 7-11 digits or a combination of digits plus +-.", "i18n_public.public.from.length_tips": "You have entered more than the maximum ${} characters.", "i18n_public.validator.positive-integer.tips": "Please enter a positive integer greater than 0.", "i18n_public.validator.money.tips": "Please enter the correct amount,three decimal places are allowed at most.", "i18n_public.validator.money-three-decimal.tips": "Please enter a number, with a maximum of three decimal places allowed.", "i18n_public.validator.positive-integer-range.tips": "Please enter a positive integer between ${} and ${}.", "i18n_public.validator.natural-number.tips": "Please enter an integer greater than 0.", "i18n_public.validator.max.tips": "Maximum allowable input 999.", "i18n_public.validator.max.num.tips": "Maximum allowable input ${}.", "i18n_public.validator.pattern": "Please enter the correct format.", "i18n_public.add.button": "Add", "i18n_public.unlimited.text": "Unlimited", "i18n_public.all.text": "All", "i18n_public.custom": "Custom", "i18n_public.select-a-record.tips": "Please select at least one record for operation.", "i18n_public.select-a-card.tips": "Please select at least one card for operation.", "i18n_public.table.tag": "Tags", "i18n_public.table.serial-number": "#", "i18n_public.text.yes": "Yes", "i18n_public.text.no": "No", "i18n_public.email.pattern": "Please enter a valid email address", "i18n_public.download.button": "Download", "i18n_public.download.button.excel": "XLSX", "i18n_public.download.button.csv": "CSV", "i18n_public.download.button.txt": "TXT", "i18n_public.verify.product-select-max": "Select a maximum of ${} product.", "i18n_public.verify.date-range-max": "The date range cannot exceed ${} months.", "i18n_public.undone-tips": "There are no operable records.", "i18n_public.selected-max-tips": "A maximum of ${} records can be selected.", "i18n_public.verify.imsi-regular": "Please input 15 digits.", "i18n_public.verify.imei-regular": "Please input 15 digits.", "i18n_public.verify.msisdn-regular": "Please input 8-15 digits.", "i18n_public.verify.sim-regular": "Please input 19-20 digits or letters.", "i18n_public.verify.num-rang": "Please enter an integer in the range of ${}~${}.", "i18n_public.tenant": "Tenant", "i18n_public.org": "Organization", "i18n_public.status": "Status", "i18n_public.batch.job-id": "Job ID", "i18n_public.batch.job-name": "Job Name", "i18n_public.batch.remark": "Remark", "i18n_public.batch.download-max": "It will be downloaded and exported as a batch job when the records is greater than ${}.", "i18n_public.max.length": "You have entered more than the maximum ${} characters.", "i18n_public.min.length": "The ${} character you entered is less than the minimum number of characters.", "i18n_public.batch.exec-mode.tips": "Tips: When the batch operation size exceeds ${}, “Make a schedule” is recommended.", "i18n_public.batch.file-upload.download": "Download the sample file,After filling the data in the file, it needs to be encrypted with PGP and uploaded", "i18n_public.batch.resource.range.max": "Maximum number of records/tasks per batch is ${}.", "i18n_public.batch.batch-file": "Batch File", "i18n_public.batch.file.err.tips": "Line ${}: ${}", "i18n_public.batch.resource.begin-le-end": "The ${} Start must be less then or equal to the ${} End.", "i18n_public.batch.resource.end-ge-begin": "The ${} End must be greater then or equal to the ${} Start.", "i18n_public.batch.schedule-time.tips": "The schedule time cannot be earlier than the current time.", "i18n_public.more": "More", "----remark-merchant ----": "merchant 使用", "i18n_merchant.mgmt.query.title": "Tenant Mgmt", "i18n_merchant.form.merchant": "Tenant", "i18n_merchant.form.status": "Status", "i18n_merchant.mgmt.form.merchant-name": "Tenant Name", "i18n_merchant.mgmt.form.status": "Status", "i18n_merchant.mgmt.from.remark": "Remark", "i18n_merchant.mgmt.form.status-draft": "Draft", "i18n_merchant.mgmt.form.status-offline": "Offline", "i18n_merchant.mgmt.form.status-published": "Online", "i18n_merchant.mgmt.table.tenant-id": "Tenant ID", "i18n_merchant.mgmt.table.tenant-code": "Tenant Code", "i18n_merchant.mgmt.table.name": "Tenant Name", "i18n_merchant.mgmt.table.alias": "Tenant <PERSON>", "i18n_merchant.mgmt.table.www": "WWW", "i18n_merchant.mgmt.table.location": "Located In", "i18n_merchant.mgmt.table.time-zone": "Time Zone", "i18n_merchant.mgmt.table.currency": "<PERSON><PERSON><PERSON><PERSON>", "i18n_merchant.mgmt.table.status": "Status", "i18n_merchant.mgmt.table.template": "Template", "i18n_merchant.mgmt.table.setting.template": "Edit", "i18n_merchant.mgmt.table.status.release": "Online", "i18n_merchant.mgmt.table.status.offline": "Offline", "i18n_merchant.mgmt.table.status.unlocked": "Unlocked", "i18n_merchant.mgmt.table.status.delete": "Delete", "i18n_merchant.mgmt.table.button.info": "Details", "i18n_merchant.mgmt.table.button.baseInfo": "Complete Basic Info", "i18n_merchant.mgmt.table.button.interface": "Setting", "i18n_merchant.mgmt.table.button.authorization": "Business Authorization", "i18n_merchant.mgmt.table.button.new": "New", "i18n_merchant.mgmt.table.button.edit": "Edit", "i18n_merchant.mgmt.table.button.publish": "Online", "i18n_merchant.mgmt.table.button.offline": "Offline", "i18n_merchant.mgmt.table.button.delete": "Delete", "i18n_merchant.mgmt.table.button.write-off": "Write-off", "i18n_merchant.mgmt.table.button.grant-deny": "Menu Mgmt.", "i18n_merchant.mgmt.table.button.err.secretkey": "The field supports only digits and letters", "i18n_merchant.mgmt.table.button.err.sftppath": "Wrong format for remote directory. Need to start with '/'", "i18n_merchant.mgmt.table.button.err.port": "Enter the correct port number. The value ranges from 0 to 65535", "i18n_merchant.mgmt.table.button.setting": "Setting", "i18n_merchant.mgmt.table.button.api.group": "Api Group", "i18n_merchant.mgmt.table.button.setting.app": "APP Mgmt.", "i18n_merchant.mgmt.table.button.setting.cdr": "CDR Mgmt.", "i18n_merchant.mgmt.table.provider": "Provider", "i18n_merchant.mgmt.new.step.title.function-menu": "Function&Menu", "i18n_merchant.mgmt.new.step.title.chooseFuncAndMenu": "Grant Menu & Function", "i18n_merchant.mgmt.new.step.title.merchant-admin": "Tenant <PERSON>", "i18n_merchant.mgmt.new.step.title.create.merchant-admin": "Create an Administrator", "i18n_merchant.mgmt.new.tenant_id.error": "The tenant code already exists", "i18n_merchant.mgmt.new.tenant_id.pattern": "The tenant code is two uppercase letters", "i18n_merchant.mgmt.basic-info.tab.title.organization": "Organization", "i18n_merchant.mgmt.basic-info.tab.title.menu": "<PERSON><PERSON>", "i18n_merchant.mgmt.basic-info.tab.title.role": "Role", "i18n_merchant.mgmt.basic-info.tab.title.staff": "Staff", "i18n_merchant.mgmt.basic-info.org.orgName": "Organization Name", "i18n_merchant.mgmt.basic-info.org.name": "Name", "i18n_merchant.mgmt.basic-info.org.email": "Email", "i18n_merchant.mgmt.basic-info.org.roleName": "Role", "i18n_merchant.mgmt.basic-info.org.statusName": "Status", "i18n_merchant.mgmt.new.form.url.err": "Please input the correct url, eg https://www.xxxxxx.com.", "i18n_merchant.mgmt.new.form.email": "Email", "i18n_merchant.mgmt.new.form.admin-name": "Admin Name", "i18n_merchant.mgmt.new.form.password": "Password", "i18n_merchant.mgmt.new.form.confirm-password": "Confirm Password", "i18n_merchant.mgmt.new.form.mobile": "Mobile", "i18n_merchant.mgmt.new.form.fax": "Fax", "i18n_merchant.mgmt.new.form.remark": "Remark", "i18n_merchant.mgmt.new.form.menu-function.tips": "Please select at least 1 menu.", "i18n_merchant.mgmt.new.form.email.pattern": "Please enter a valid email address.", "i18n_merchant.mgmt.new.form.password.format.charset": "The password consists of 8~20 digits、letters and special characters such as ^&#?~!$<>'\"\\|+%/etc.", "i18n_merchant.mgmt.new.form.password.not_same": "Two entered password are inconsistent.", "i18n_merchant.mgmt.grant.table.title.menu": "Catalog", "i18n_merchant.mgmt.grant.table.title.menu-grant": "Menu Permission", "i18n_merchant.mgmt.grant.table.title.function-grant": "Functional Permission", "i18n_merchant.interface.query.title": "Tenant APIs", "i18n_merchant.interface.form.merchant": "Tenant", "i18n_merchant.interface.form.app-name": "APP Name", "i18n_merchant.interface.form.merchant-name": "Tenant Name", "i18n_merchant.interface.form.status": "Status", "i18n_merchant.interface.form.status-all": "-- ALL --", "i18n_merchant.interface.table.merchant-name": "Tenant", "i18n_merchant.interface.table.organization": "Organization", "i18n_merchant.interface.table.detail": "APP Info.", "i18n_merchant.interface.table.appid": "APP ID", "i18n_merchant.interface.table.name": "APP Name", "i18n_merchant.interface.table.sub-domain": "Sub-Domain", "i18n_merchant.interface.table.version": "API Version", "i18n_merchant.interface.table.domain": "Domain", "i18n_merchant.interface.table.group": "API Group", "i18n_merchant.interface.table.status": "Status", "i18n_merchant.interface.table.status.update.time": "Update Time", "i18n_merchant.interface.table.description": "Description", "i18n_merchant.interface.table.button.info": "Basic Info", "i18n_merchant.interface.table.button.new": "New", "i18n_merchant.interface.table.button.edit": "Edit", "i18n_merchant.interface.table.button.publish": "Publish", "i18n_merchant.interface.table.button.offline": "Offline", "i18n_merchant.interface.table.button.grant-deny": "Grant & Deny", "i18n_merchant.interface.table.button.help": "Help", "i18n_merchant.interface.table.error": "Form error, please check!", "i18n_merchant.interface.basic-info.title": "Tenant Interface Details", "i18n_merchant.interface.basic-info.tab.title.basic-info": "Complete APP profile", "i18n_merchant.interface.basic-info.tab.title.interface-list": "Grant API", "i18n_merchant.interface.basic-info.tab.title.app.info": "APP Information", "i18n_merchant.interface.basic-info.tab.title.api.List": "API List", "i18n_merchant.interface.basic-info.tab.title.error-code": "Error Code", "i18n_merchant.interface.basic-info.merchant": "Tenant", "i18n_merchant.interface.basic-info.orgName": "Organization", "i18n_merchant.cdr.basic-info.orgName": "Organization", "i18n_merchant.cdr.new.form.index": "Index", "i18n_merchant.cdr.new.form.password": "Password", "i18n_merchant.interface.basic-info.auth-key": "<PERSON>th<PERSON> Key", "i18n_merchant.interface.basic-info.domain": "Sub-Domain", "i18n_merchant.interface.basic-info.version": "Version ", "i18n_merchant.interface.basic-info.ip-list": "Accessible IP List", "i18n_merchant.interface.basic-info.description": "Description", "i18n_merchant.interface.basic-info.error-code.table.code": "Error Code", "i18n_merchant.interface.basic-info.error-code.table.description": "Description", "i18n_merchant.interface.basic-info.index": "Index", "i18n_merchant.interface.basic-info.password": "Password", "i18n_merchant.interface.api.title.security": "Transmission Security", "i18n_merchant.interface.api.title.secretKey": "Secret Key", "i18n_merchant.interface.api.title.info": "Details", "i18n_merchant.interface.api.title.view": "View", "i18n_merchant.interface.api.button.grant": "<PERSON>", "i18n_merchant.interface.api.button.org.grant": "<PERSON>", "i18n_merchant.interface.api.button.deny": "<PERSON><PERSON>", "i18n_merchant.interface.api.button.save": "Save", "i18n_merchant.interface.api.button.modify": "Modify & Save", "i18n_merchant.interface.api.table.name": "Interface Name", "i18n_merchant.interface.api.table.API": "API", "i18n_merchant.interface.api.table.url": "URL", "i18n_merchant.interface.api.table.grant": "Grant & Deny", "i18n_merchant.interface.api.table.status-grant": "ON ", "i18n_merchant.interface.api.table.status-deny": "OFF", "i18n_merchant.interface.api.label.interface-name": "Interface Name", "i18n_merchant.interface.api.label.url": "Access Url", "i18n_merchant.interface.api.label.feedback-url": "Feedback URL", "i18n_merchant.interface.api.label.feedback-url-error": "Please input the correct callback address, eg:https://www.xxx.com/api/callback", "i18n_merchant.interface.api.label.subDomain": "Only digits and letters are allowed in a subdomain name. No other symbols are allowed.", "i18n_merchant.interface.api.label.request-template": "Request Body", "i18n_merchant.interface.api.label.response-template": "Response Body", "i18n_merchant.interface.api.label.response-template.view": "View Response Body", "i18n_merchant.interface.api.label.description": "Description", "i18n_merchant.interface.new.title": "New API Account", "i18n_merchant.interface.new.step.title.basic-info": "Basic Info", "i18n_merchant.interface.new.step.title.interface-list": "Interface List", "i18n_merchant.interface.new.form.merchant": "Tenant", "i18n_merchant.interface.new.form.organization": "Organization", "i18n_merchant.interface.new.form.organization-unlimited": "unlimited", "i18n_merchant.interface.new.form.domain": "Sub-Domain", "i18n_merchant.interface.new.form.version": "Version", "i18n_merchant.interface.new.form.ip-list": "Accessible IP List", "i18n_merchant.interface.new.form.ip-list-unlimited": "unlimited", "i18n_merchant.interface.new.form.tps_tpd_title": "TPS/TPD", "i18n_merchant.interface.new.form.tps_title": "/Second", "i18n_merchant.interface.new.form.zero_tip": "Zero is unlimited.", "i18n_merchant.interface.new.form.tpd_title": "/Day", "i18n_merchant.interface.new.form.tps": "Please enter a number from 0 to 10000", "i18n_merchant.interface.new.form.tpd": "Please enter a number from 0 to *********", "i18n_merchant.interface.new.form.description": "Description", "i18n_merchant.interface.new.form.domain.error": "Authority Domain is already exists", "i18n_merchant.interface.new.form.ip-list.error": "Error format with Accessible IP List", "i18n_merchant.interface.new.form.ip-list.tips": "The format of the IP address should be '***************,..,192.168.200.xxx'", "i18n_merchant.interface.new.form.feedbackUrl.tips": "Please input the correct callback address, eg https://www.xxxxxx.com.", "i18n_merchant.interface.new.form.ip.record.error": "Please select at least one record to operate", "i18n_merchant.interface.edit.step.title": "Edit", "i18n_merchant.interface.edit.form.merchant": "Tenant", "i18n_merchant.interface.edit.form.organization": "Organization", "i18n_merchant.interface.edit.form.auth-key": "<PERSON>th<PERSON> Key", "i18n_merchant.interface.edit.form.domain": "Sub-Domain", "i18n_merchant.interface.edit.form.version": "Version", "i18n_merchant.interface.edit.form.ip-list": "Accessible IP List", "i18n_merchant.interface.edit.form.description": "Description", "i18n_merchant.interface.form.domain.error": "The subdomain name already exists", "i18n_merchant.interface.param": "Interface Param", "i18n_merchant.org.panel.title": "Organization", "i18n_merchant.org.tenant": "Tenant", "i18n_merchant.org.org": "Organization", "i18n_merchant.org.info.title": "Info", "i18n_merchant.org.tree.tips": "Tips: The selected node does not support the creation of a lower-level organization.", "i18n_merchant.org.info.parent-org": "Parent", "i18n_merchant.org.info.org": "Organization", "i18n_merchant.org.info.remark": "Remark", "i18n_merchant.org.info.status": "Status", "i18n_merchant.org.public.status_in_active": "In-Active", "i18n_merchant.org.public.status_active": "Active", "i18n_merchant.org.public.status_locked": "Locked", "i18n_merchant.org.org.required": "Please enter organization.", "i18n_merchant.org.status.required": "Please choose status.", "i18n_merchant.org.remark.max_length": "You have entered more than the maximum 300 characters.", "i18n_merchant.org.staff.title": "Staff", "i18n_merchant.org.staff.table.orgName": "Organization Name", "i18n_merchant.org.staff.table.name": "Name", "i18n_merchant.org.staff.table.email": "Email", "i18n_merchant.org.staff.table.roleName": "Role", "i18n_merchant.org.staff.table.statusName": "Status", "i18n_merchant.org.new.title": "New", "i18n_merchant.org.new.parent-org": "Parent", "i18n_merchant.org.new.org": "Organization", "i18n_merchant.org.new.remark": "Remark", "i18n_merchant.org.edit.title": "Edit", "i18n_merchant.org.edit.parent-org": "Parent", "i18n_merchant.org.edit.org": "Organization", "i18n_merchant.org.edit.remark": "Remark", "i18n_merchant.org.delete.title": "Delete", "i18n_merchant.org.info.lock.button": "Lock", "i18n_merchant.org.info.unlock.button": "Unlock", "i18n_merchant.org.info.active.button": "Active", "----App Log ----": "", "i18n_system_logs_app.header": "Query conditions", "i18n_system_logs_app.result": "Query Result", "i18n_system_logs_app.tenant": "Tenant", "i18n_system_logs_app.resources": "IMSI / SIM / MSISDN", "i18n_system_logs_app.appName": "APP", "i18n_system_logs_app.apiCode": "API", "i18n_system_logs_app.invokeId": "Transaction ID", "i18n_system_logs_app.createDate": "Start Date - End Date", "i18n_system_logs_app.feedBackDate": "Feedback Start Date - Feedback End Date", "i18n_system_logs_app.feedBackTime": "Feedback Time", "i18n_system_logs_app.resources.start": "Start IMSI / SIM / MSISDN", "i18n_system_logs_app.resources.end": "End IMSI / SIM / MSISDN", "i18n_system_logs_app.sim.start": "SIM Start", "i18n_system_logs_app.sim.end": "SIM End", "i18n_system_logs_app.msisdn.start": "MSISDN Start", "i18n_system_logs_app.msisdn.end": "MSISDN End", "i18n_system_logs_app.resources.err": "The value must be number", "i18n_system_logs_app.table.button.result": "<PERSON><PERSON><PERSON>", "i18n_system_logs_app.table.button.invoke": "Request", "i18n_system_logs_app.table.button.detail": "Detail", "i18n_system_logs_app.table.tenant": "Tenant", "i18n_system_logs_app.table.org": "Organization", "i18n_system_logs_app.table.msisdn": "MSISDN", "i18n_system_logs_app.table.imsi": "IMSI", "i18n_system_logs_app.table.sim": "SIM", "i18n_system_logs_app.table.apiCode": "API", "i18n_system_logs_app.table.appId": "APP ID", "i18n_system_logs_app.table.appName": "APP", "i18n_system_logs_app.table.status": "Status", "i18n_system_logs_app.table.invokeId": "Transaction ID", "i18n_system_logs_app.table.createTime": "Operation Time", "i18n_system_logs_app.table.request": "Feedback request body", "i18n_system_logs_app.table.response": "Feedback response body", "i18n_system_logs_app.request.url": "Request URL", "i18n_system_logs_app.request.ip": "Request IP", "i18n_system_logs_app.result.code": "Result Code", "i18n_system_logs_app.result.id": "Feedback ID", "i18n_system_logs_app.result.adress": "Feedback URL", "i18n_system_logs_app.result.request": "Request Body", "i18n_system_logs_app.result.message": "Result Message", "i18n_system_logs_app.download.resourcesType": "Resources Type", "i18n_system_logs_app.table.status.success": "Success", "i18n_system_logs_app.table.status.failure": "Failure", "----remark-privilege----": "Privilege 使用", "i18n_privilege.job": "My Job", "i18n_privilege.detail": "Details", "i18n_privilege.password": "Password", "i18n_privilege.change_password": "Change Password", "i18n_privilege.original_password": "Original Password", "i18n_privilege.new_password": "New Password", "i18n_privilege.password.unequal": "Two entered password are inconsistent.", "i18n_privilege.password.format": "The password consists of 8~20 digits、letters and special characters such as ^&#?~!$<>'\"\\|+%/etc.", "i18n_privilege.confirm_password": "Confirm Password", "i18n_privilege.email.pattern": "Please enter a valid email address.", "i18n_privilege.max.length": "You have entered more than the maximum ${} characters.", "i18n_privilege.fax.pattern": "Please enter a valid fax address.", "i18n_privilege.fax": "Fax", "i18n_privilege.address": "Address", "i18n_privilege.description": "Description", "i18n_privilege.tenant": "Tenant", "i18n_privilege.name": "Staff Name", "i18n_privilege.alias": "<PERSON><PERSON>", "i18n_privilege.email": "Email", "i18n_privilege.mobile": "Mobile", "i18n_privilege.org": "Organization", "i18n_privilege.status": "Status", "i18n_privilege.role": "Role", "i18n_privilege.last_login_time": "Last Login Time", "i18n_privilege.freeze": "Freeze", "i18n_privilege.remark": "Remark", "i18n_privilege.staff.status_active": "Active", "i18n_privilege.staff.status_locked": "Locked", "i18n_privilege.create_staff": "New Staff", "i18n_privilege.new": "New", "i18n_privilege.edit": "Edit", "i18n_privilege.delete": "Delete", "i18n_privilege.lock": "Lock & Unlock", "i18n_privilege.reset_password": "Reset Password", "i18n_privilege.choose.way": "Please choose a way to reset the password of ", "i18n_privilege.thaw": "Unfreeze", "i18n_privilege.reset.way": " ", "i18n_privilege.password.not_same": "Please type the same password.", "i18n_privilege.error": "Error", "i18n_privilege.success": "Operational Success", "---- privilege_staff ----": "", "i18n_privilege_staff.detail": "Details", "i18n_privilege_staff.password": "Password", "i18n_privilege_staff.password.format": "The password consists of 8~20 digits、letters and special characters such as ^&#?~!$<>'\"\\|+%/etc.", "i18n_privilege_staff.confirm_password": "Confirm Password", "i18n_privilege_staff.email.pattern": "Please enter a valid email address.", "i18n_privilege_staff.max.length": "You have entered more than the maximum ${} characters.", "i18n_privilege_staff.fax.pattern": "Please enter a valid fax address.", "i18n_privilege_staff.fax": "Fax", "i18n_privilege_staff.address": "Address", "i18n_privilege_staff.description": "Description", "i18n_privilege_staff.tenant": "Tenant", "i18n_privilege_staff.name": "Staff Name", "i18n_privilege_staff.alias": "<PERSON><PERSON>", "i18n_privilege_staff.email": "Email", "i18n_privilege_staff.mobile": "Mobile", "i18n_privilege_staff.org": "Organization", "i18n_privilege_staff.status": "Status", "i18n_privilege_staff.role": "Role", "i18n_privilege_staff.last_login_time": "Last Login Time", "i18n_privilege_staff.freeze": "Freeze", "i18n_privilege_staff.remark": "Remark", "i18n_privilege_staff.staff.status_active": "Active", "i18n_privilege_staff.staff.status_locked": "Locked", "i18n_privilege_staff.create_staff": "New Staff", "i18n_privilege_staff.new": "New", "i18n_privilege_staff.edit": "Edit", "i18n_privilege_staff.delete": "Delete", "i18n_privilege_staff.lock": "Lock & Unlock", "i18n_privilege_staff.reset_password": "Reset Password", "i18n_privilege_staff.choose.way": "Please choose a way to reset the password of ", "i18n_privilege_staff.thaw": "Unfreeze", "i18n_privilege_staff.reset.way": " ", "i18n_privilege_staff.password.not_same": "Please type the same password.", "---- privilege_role ----": "", "i18n_privilege_role.tenant": "Tenant", "i18n_privilege_role.role.tree.label": "Role", "i18n_privilege_role.info.title": "Info", "i18n_privilege_role.info.role.name": "Role Name", "i18n_privilege_role.info.role.status": "Status", "i18n_privilege_role.info.role.remark": "Remark", "i18n_privilege_role.info.status.active": "Active", "i18n_privilege_role.new.title": "New", "i18n_privilege_role.new.role.name": "Role Name", "i18n_privilege_role.new.role.remark": "Remark", "i18n_privilege_role.edit.title": "Edit", "i18n_privilege_role.edit.role.name": "Role Name", "i18n_privilege_role.edit.role.remark": "Remark", "i18n_privilege_role.delete.title": "Delete", "i18n_privilege_role.grant.menu.title": "<PERSON><PERSON>", "i18n_privilege_role.grant.deny.title": "<PERSON><PERSON>", "i18n_privilege_role.grant.panel": "Have Permission", "i18n_privilege_role.grant.title.menu": "Catalog", "i18n_privilege_role.grant.title.menu.grant": "Menu Permissions", "i18n_privilege_role.grant.title.func": "Function Permissions", "i18n_privilege_role.staff.title": "Staff", "i18n_privilege_role.staff.table.orgName": "Organization", "i18n_privilege_role.staff.table.name": "Staff", "i18n_privilege_role.staff.table.email": "Email", "i18n_privilege_role.staff.table.roleName": "Role", "i18n_privilege_role.staff.table.statusName": "Status", "i18n_privilege_role.staff.table.status.inactive": "In-Active", "i18n_privilege_role.staff.table.status.active": "Active", "i18n_privilege_role.staff.table.status.locked": "Locked", "i18n_privilege_role.public.from.length_tips": "You have entered more than the maximum ${} characters.", "i18n_privilege_role.public.button.submit": "Submit", "i18n_privilege_role.public.button.cancel": "Cancel", "----Login Log ----": "", "i18n_system_logs_login.title": "Login Logs", "i18n_system_logs_login.form.mvno": "Tenant", "i18n_system_logs_login.form.date": "Start Date - End Date", "i18n_system_logs_login.form.email": "Email", "i18n_system_logs_login.table.org": "Organization", "i18n_system_logs_login.table.email": "Email", "i18n_system_logs_login.table.login-ip": "Login IP", "i18n_system_logs_login.table.login-area": "Login Area", "i18n_system_logs_login.table.login-result": "<PERSON><PERSON>t", "i18n_system_logs_login.table.login-time": "Login Time", "i18n_system_logs_login.table.tenant-name": "Tenant", "i18n_system_logs_login.table.logout-time": "Logout Time", "i18n_system_logs_login.table.title": "Query Result", "i18n_system_logs_login.download.tip": "Please click search to download.", "i18n_system_logs_login.download.button": "Download", "----Operation Log ----": "", "i18n_system_logs_operation.title": "Operation Logs", "i18n_system_logs_operation.form.mvno": "Tenant", "i18n_system_logs_operation.form.date": "Start And End Date", "i18n_system_logs_operation.form.email": "Email", "i18n_system_logs_operation.table.title": "Query Result", "i18n_system_logs_operation.table.mvno": "Tenant", "i18n_system_logs_operation.table.business-sn": "Job ID", "i18n_system_logs_operation.table.bussiness_type": "Bussiness Type", "i18n_system_logs_operation.table.org": "Organization", "i18n_system_logs_operation.table.email": "Email", "i18n_system_logs_operation.table.request_time": "Operation Time", "i18n_system_logs_operation.table.status_name": "Operation Result", "i18n_system_logs_operation.table.error.message": "Error Message", "i18n_system_logs_operation.download.tip": "Please click search to download.", "i18n_system_logs_operation.download.button": "Download", "------ cache   --------- ": "", "i18n_system_cache.refresh.all": "Refresh All", "i18n_system_cache.refresh": "Refresh", "i18n_system_cache.cache.reload": "<PERSON><PERSON>", "i18n_system_cache.cache.code": "Cache Code", "i18n_system_cache.cache.name": "<PERSON><PERSON>", "i18n_system_cache.last.refresh.time": "Last Refresh Time", "i18n_system_cache.operator": "Operator", "i18n_system_cache.operator.no.record": "Please select the record.", "i18n_system_cache.cache.group": "Cache Group", "------ task   --------- ": "", "i18n_system_task.run": "Run", "i18n_system_task.edit": "Edit", "i18n_system_task.resume": "Resume", "i18n_system_task.pause": "Pause", "i18n_system_task.refresh.all": "Refresh All", "i18n_system_task.table.group": "Task Group", "i18n_system_task.table.name": "Job Name", "i18n_system_task.table.status": "Task Status", "i18n_system_task.table.cron.expression": "Cron Expression", "i18n_system_task.table.cron.bean": "<PERSON><PERSON>", "i18n_system_task.table.cron.remark": "Remark", "i18n_system_task.schedule.console": "Timed Task", "i18n_system_task.schedule.job": "Schedule Job", "i18n_system_task.edit_title": "Edit", "i18n_system_task.edit_name": "Job Name", "i18n_system_task.edit_expression": "Cron Expression", "--------  System Setting ------": "", "i18n_system_setting": "System Setting", "i18n_system_setting_login_title1": "<PERSON><PERSON>", "i18n_system_setting_login_title2": "<PERSON><PERSON>", "i18n_system_setting_login_value_err1": "Enter a 32-bit hexadecimal key", "-------  Log Mgmt ----------": "", "i18n_tag_mgmt_code": "Code", "i18n_tag_mgmt_category": "Category", "i18n_tag_mgmt_checked": "Check", "i18n_tag_mgmt_description": "Description", "i18n_tag_mgmt_cannot_be_deleted_root_tips": "The root node cannot be deleted!", "i18n_tag_mgmt_exsis_profile_tips": "The label is associated with a resource. Delete the associated resource.", "i18n_tag_mgmt_un_create_tips": "Unable to define multi-level labels!", "i18n_tag_mgmt_sys_one_tag": "System pre-defines single-layer label", "i18n_tag_mgmt_sys_tree_tag": "System pre-defines tree label", "i18n_tag_mgmt_busi_one_tag": "Service definition Single-layer label", "i18n_tag_mgmt_busi_tree_tag": "Service definition tree label", "i18n_tag_mgmt_value": "Value", "i18n_tag_mgmt_profile_tag": "Profile Tag", "i18n_tag_mgmt_tag_template": "Tag Template", "i18n_tag_mgmt_tag_detail": "Tag Detail", "i18n_tag_mgmt_tag_un_checked_tip": "Unchecked tabs!", "----console_jobs----": "console_jobs 使用", "i18n_console_jobs.schedule.console": "Timed Task", "i18n_console_jobs.schedule.job": "Schedule Job", "i18n_console_jobs.batch.job": "<PERSON><PERSON>", "i18n_console_jobs.run": "Run", "i18n_console_jobs.edit": "Edit", "i18n_console_jobs.resume": "Resume", "i18n_console_jobs.pause": "Pause", "i18n_console_jobs.delete": "Delete", "i18n_console_jobs.refresh.all": "Refresh All", "i18n_console_jobs.refresh": "Refresh", "i18n_console_jobs.task.group": "Task Group", "i18n_console_jobs.task.name": "Job Name", "i18n_console_jobs.task.status": "Task Status", "i18n_console_jobs.cron.expression": "Cron Expression", "i18n_console_jobs.tenant": "Tenant", "i18n_console_jobs.start.end": "Start Date - End Date", "i18n_console_jobs.business.name": "Business", "i18n_console_jobs.batch.id": "Job ID", "i18n_console_jobs.batch.name": "Job Name", "i18n_console_jobs.start.time": "Start Time", "i18n_console_jobs.create.time": "Create Time", "i18n_console_jobs.create.by": "Creator", "i18n_console_jobs.finish.time": "Finish Time", "i18n_console_jobs.status": "Status", "i18n_console_jobs.serial": "Business SN", "i18n_console_jobs.error.reason": "Error Message", "i18n_console_jobs.item.keyword": "Item SN/Content", "i18n_console_logs.user.item": "<PERSON><PERSON>", "i18n_console_jobs.vendor": "<PERSON><PERSON><PERSON>", "i18n_console_jobs.type": "Job Type", "i18n_console_jobs.mnc": "MNC", "i18n_console_jobs.maker": "Maker", "i18n_console_jobs.capacity": "Capacity", "i18n_console_jobs.card.type": "Type", "i18n_console_jobs.table.query.title": "Results", "i18n_console_jobs.table.cancel.task": "Cancel Job", "i18n_console_jobs.table.adjust.task.time": "Adjust Schedule Time", "i18n_console_jobs.cancel.task.tips": "Are you sure to cancel the task?", "i18n_console_jobs.cancel.task.success.tips": "Cancel Task Success!", "i18n_console_jobs.cancel.task.failure.tips": "Cancel Task Failure!", "i18n_console_jobs.edit.task.time.tips": "Are you sure to adjust the execution time?", "i18n_console_jobs.edit.task.time.success.tips": "Adjust Time Success!", "i18n_console_jobs.edit.task.time.failure.tips": "Adjust Time Failure!", "i18n_console_jobs.edit.task.time.info.null.tips": "The date cannot be null!", "i18n_console_jobs.edit.batchId.null.tips": "Please select a record!", "i18n_console_jobs.remark": "Remark", "i18n_console_jobs.detail.job-info": "Job Info", "i18n_console_jobs.detail.query-params": "Query Params", "i18n_console_jobs.detail.checked-list": "Checked List", "i18n_console_jobs.detail.business-data": "Business Data", "i18n_console_jobs.detail.range": "Range", "i18n_console_jobs.detail.uploaded-file": "Uploaded File", "i18n_console_jobs.detail.checked-item": "Checked Item", "i18n_console_jobs.detail.target-tenant": "Target Tenant", "i18n_console_jobs.detail.target-org": "Target Organization", "i18n_console_jobs.detail.sim.target-maker": "Target Maker", "i18n_console_jobs.detail.sim.target-type": "Target Type", "i18n_console_jobs.detail.sim.target-capacity": "Target Capacity", "i18n_console_jobs.detail.msisdn.target-msisdnType": "Target MSISDN Type", "i18n_console_jobs.detail.resource.type": "Resource Type", "i18n_console_jobs.detail.resource.sim-begin": "SIM Start", "i18n_console_jobs.detail.resource.sim-end": "SIM End", "i18n_console_jobs.detail.resource.imsi-begin": "IMSI Start", "i18n_console_jobs.detail.resource.imsi-end": "IMSI End", "i18n_console_jobs.detail.resource.msisdn-begin": "MSISDN Start", "i18n_console_jobs.detail.resource.msisdn-end": "MSISDN End", "i18n_console_jobs.detail.resource.begin": "Start", "i18n_console_jobs.detail.resource.end": "End", "i18n_console_jobs.detail.busi.tenant": "Tenant", "i18n_console_jobs.detail.busi.org": "Organization", "i18n_console_jobs.detail.busi.brand": "Brand", "i18n_console_jobs.detail.busi.vendor": "<PERSON><PERSON><PERSON>", "i18n_console_jobs.detail.busi.cs-status": "CS Status", "i18n_console_jobs.detail.busi.sim": "SIM", "i18n_console_jobs.detail.busi.imsi": "IMSI", "i18n_console_jobs.detail.busi.msisdn": "MSISDN", "i18n_console_jobs.cache.reload": "<PERSON><PERSON>", "i18n_console_jobs.cache.code": "Cache Code", "i18n_console_jobs.cache.name": "<PERSON><PERSON>", "i18n_console_jobs.last.refresh.time": "Last Refresh Time", "i18n_console_jobs.operator": "Operator", "i18n_console_jobs.operator.no.record": "Please select the record.", "i18n_console_jobs.cache.group": "Cache Group", "i18n_console_logs.batch.logs": "<PERSON><PERSON> Logs", "i18n_console_logs.submit.time": "Submit Time", "i18n_console_logs.start.time": "Start Time", "i18n_console_logs.schedule.time": "Schedule Time", "i18n_console_logs.operation.result": "Total | Success | Waiting | Failure", "i18n_console_logs.operation.details.result": "Operation Result", "i18n_console_logs.finish.time": "Finish Time", "i18n_console_logs.total": "Total", "i18n_console_logs.success.cnt": "Success Cnt.", "i18n_console_logs.fail.cnt": "Failed Cnt.", "i18n_console_logs.batch.item": "Item SN", "i18n_console_logs.content": "Content", "i18n_console_logs.result": "Result", "i18n_console_logs.failure": "Failure", "i18n_console_logs.download_tips": "At least one record for download.", "i18n_console_logs.user.business": "Job Type", "i18n_console_jobs.export.file": "Download Output File", "i18n_console_logs.user.detail.total": "Total", "i18n_console_logs.user.detail.success": "Success", "i18n_console_logs.user.detail.failure": "Failure", "i18n_console_logs.user.detail.warning": "Warning", "----factory----": "factory 使用", "i18n_production.create.name": "Creator", "i18n_production.create.time": "Create Time", "i18n_production.update.name": "Updater", "i18n_production.update.time": "Update Time", "i18n_production.key.word": "Key word", "i18n_factory.form.name": "Factory Name", "i18n_factory.form.short.name": "Factory Abbreviation", "i18n_factory.form.contact.name": "Contact Person", "i18n_factory.form.contact.phone": "Contact Number", "----customer----": "customer 使用", "i18n_customer.form.name": "Customer Name", "i18n_customer.form.short.name": "Customer Short Name", "i18n_customer.form.contact.name": "Customer Contact", "i18n_customer.form.contact.phone": "Customer Contact Number", "----email----": "email 使用", "i18n_email.form.operation": "Business Code", "i18n_email.form.subject": "Email Subject", "i18n_email.form.send.to": "Recipient", "i18n_email.form.send.by": "Sender", "i18n_email.form.cc.to": "CC", "i18n_email.form.content": "<PERSON>ail Te<PERSON>late", "----key----": "key 使用", "i18n_key.form.operation": "Business Code", "i18n_key.form.key.type": "Key Type", "i18n_key.form.key.alg": "Algorithm", "i18n_key.form.key.file": "Key file"}