<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>UICC</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <style>
    #nw-initial-loader-wrapper {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 999999;
      background: #2F4050;
      opacity: 1;
      visibility: visible;
      transition: opacity 0.5s, visibility 0.5s;
    }

    #nw-initial-loader-wrapper.hidden {
      opacity: 0;
      visibility: hidden;
    }

    #nw-initial-loader-wrapper .nw-initial-loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 120px;
      height: 120px;
      transform: translate(-50%, -50%);
    }

    #nw-initial-loader-wrapper .nw-initial-loader .nw-initial-loader-loading {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #1ab394;
      -webkit-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
    }

    #nw-initial-loader-wrapper .nw-initial-loader .nw-initial-loader-loading:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #1c84c6;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #nw-initial-loader-wrapper .nw-initial-loader .nw-initial-loader-loading:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #23c6c8;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }

    #nw-initial-loader-wrapper .nw-initial-loader-title {
      color: #FFFFFF;
      position: absolute;
      font-size: 16px;
      top: calc(50% + 75px);
      left: 50%;
      transform: translateX(-50%);
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }
    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }
  </style>
</head>
<body>
<div id="nw-initial-loader-wrapper">
  <div class="nw-initial-loader">
    <div class="nw-initial-loader-loading"></div>
  </div>
  <div class="nw-initial-loader-title">System is Loading. Please wait...</div>
</div>
<app-root></app-root>
</body>
</html>
