import {<PERSON><PERSON><PERSON>er, FormGroup} from '@angular/forms';
import {NwDialogService, NwInputFileHook, NwModalService} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {HttpClient} from 'src/app/services/http.client';
import {DictTools} from '../../global/dict.tools';
import {DictCode} from '../../global/enums/dict-code.enum';
import {FormService} from '../form.service';
import {ConfigConstant} from '../../global/conts';
import {FunCodeGlobalConstant} from '../../global/conts/fun-code-global.constant';
import {DateUtils} from '../../global/utils';
import {SimCosTypeEnum} from '../../global/enums/sim.cos.type.enum';
import {SimPrintTypeEnum} from '../../global/enums/sim.print.type.enum';
import {OrderPolicyModalComponent} from './component/policy-modal/order-policy-modal.component';
import {NwModalRef} from '@ng-nwui/components/elements/dialogs/modal.service';
import {BusinessConstant} from "../../global/conts/business.constant";

export class OrderFormService extends FormService {

  rspAppIdJoy = '00';
  reuseModeOnce = '00';
  rspPinpukModeDesignated = '00';

  policyForm!: FormGroup;
  deliverForm!: FormGroup;

  policyId!: any;

  simTypeList!: any;
  simSpecList!: any;
  simCosTypeList!: any;
  simPrintTypeList!: any;
  policyIdList!: any;
  rspAppIdList!: any;
  reuseModeList!: any;
  rspPinpukModeList!: any;
  rspPoolIdNweList!: any;
  rspPoolIdJoyList!: any;
  rspPoolIdList!: any;

  rspCidPrefixNweList!: any;
  rspCidPrefixJoyList!: any;
  rspCidPrefixList!: any;
  simCosVersionList!: any;
  cardTypeList!: any;
  cardTypeStyle = 'min-width: 75px';

  fileConfig!: any;
  hook: NwInputFileHook = {
    handleBeforeUpload: (host, file) => {
      return file.text().then(() => {
        host.params = {opCode: FunCodeGlobalConstant.ORDER_CREATE};
      });
    }
  };

  locale = this.nwI18nService.getCurrentLocale() as string;

  dateConfig = {
    minDate: new Date(),
    autoApply: false,
    format: DateUtils.getDateFormatter(this.locale)
  };

  policyNwModalRef!: any;

  showReuseAmount = false;
  showSimCosFileId = false;
  showSimPrintFileId = false;
  showRspPinpukAttr = false;

  factorySearchConfigExtraParam = {excludeFactoryIdList: [BusinessConstant.FACTORY_ID_IMSI]};

  constructor(
    protected fb: FormBuilder,
    protected nwDialogService: NwDialogService,
    protected apiService: HttpClient,
    protected nwI18nService: NwI18nService,
    protected dictTools: DictTools,
    protected nwModalService: NwModalService) {
    super(fb, nwDialogService, apiService, nwI18nService, dictTools);
    this.fileConfig = ConfigConstant.getFileConfigBySuffix('*');
  }


  override initForm(): void {
    this.form = this.fb.group({
      contractNo: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(32)]],
      custId: [null, [this.homeService.requiredValidator]],
      factoryId: [null, [this.homeService.requiredValidator]],
      simType: ['00', [this.homeService.requiredValidator]],
      simQuantity: [null, [this.homeService.requiredValidator, this.homeService.positiveIntegerValidator]],
      simSpec: [null, [this.homeService.maxLengthValidatorFn(30)]],
      simCosType: [null, [this.homeService.requiredValidator]],
      simCosFileId: [null],
      simPrintType: [null, [this.homeService.requiredValidator]],
      simPrintFileId: [null],
      contractFileId1: [null, this.homeService.requiredValidator],
      contractFileId2: [null],
      contractRemark: [null, [this.homeService.maxLengthValidatorFn(512)]],
    });

    this.policyForm = this.fb.group({
      reuseMode: ['00', [this.homeService.requiredValidator]],
      reuseAmount: [1, [this.homeService.positiveIntegerValidator]],
      policyId: [null, [this.homeService.requiredValidator]],
      vendorId: [null, [this.homeService.requiredValidator]],

      cardType: ['ICCID'],
      rangeBeginIccid: [null],
      rangeEndIccid: [null],

      blockSize: [null],
      blockDataList: [null],

      rspAppId: [null, [this.homeService.requiredValidator]],
      rspCidPrefix: [null, [this.homeService.requiredValidator]],
      rspPoolId: [null, [this.homeService.requiredValidator]],
      rspPinpukMode: [null, this.homeService.requiredValidator],

      pin1: [null],
      puk1: [null],
      pin2: [null],
      puk2: [null],
    });

    this.deliverForm = this.fb.group({
      custDeliverDate: [null, [this.homeService.requiredValidator]],
      factoryDeliverDate: [null, [this.homeService.requiredValidator]],
      deliverRemark: [null, [this.homeService.maxLengthValidatorFn(512)]],
    });
  }

  override setForm(formValue: any): void {
    this.form.patchValue(formValue, {emitEvent: false});
    this.policyForm.patchValue(formValue, {emitEvent: false});

    // 时间格式处理
    this.deliverForm.patchValue({
      ...formValue,
      factoryDeliverDate: new Date(DateUtils.formatCompactDateToLocale(formValue?.factoryDeliverDate, this.locale)),
      custDeliverDate: new Date(DateUtils.formatCompactDateToLocale(formValue?.custDeliverDate, this.locale))
    }, {emitEvent: false});
    this.policyId = formValue.policyId;
  }

  override getDictList(): void {
    this.dictTools.getMulitDictList([
      DictCode.SIM_TYPE, DictCode.SIM_SPEC, DictCode.SIM_COS_TYPE, DictCode.SIM_PRINT_TYPE, DictCode.POLICY_ID, DictCode.RSP_APP,
      DictCode.REUSE_MODE, DictCode.RSP_PINPUK_MODE, DictCode.RSP_POOL_ID_NWE, DictCode.RSP_POOL_ID_JOY, DictCode.RSP_CID_PREFIX_NWE,
      DictCode.RSP_CID_PREFIX_JOY, DictCode.SIM_COS_VERSION
    ]).subscribe(resp => {
      this.simTypeList = resp[DictCode.SIM_TYPE];
      this.simSpecList = resp[DictCode.SIM_SPEC];
      this.simCosTypeList = resp[DictCode.SIM_COS_TYPE];
      this.simPrintTypeList = resp[DictCode.SIM_PRINT_TYPE];
      this.policyIdList = resp[DictCode.POLICY_ID];
      this.rspAppIdList = resp[DictCode.RSP_APP];
      this.reuseModeList = resp[DictCode.REUSE_MODE];
      this.rspPinpukModeList = resp[DictCode.RSP_PINPUK_MODE];
      this.rspPoolIdNweList = resp[DictCode.RSP_POOL_ID_NWE];
      this.rspPoolIdJoyList = resp[DictCode.RSP_POOL_ID_JOY];
      this.rspCidPrefixNweList = resp[DictCode.RSP_CID_PREFIX_NWE];
      this.rspCidPrefixJoyList = resp[DictCode.RSP_CID_PREFIX_JOY];
      this.simCosVersionList = resp[DictCode.SIM_COS_VERSION];
    });

    this.cardTypeList = [{id: 'ICCID', text: 'ICCID'}];
  }


  override buildParams(): any {
    const formValue = super.buildParams();
    return {...formValue, ...this.policyForm.value, ...this.deliverForm.value};
  }

  rspAppIdChange(value: any): void {
    this.policyForm.get('rspPoolId')?.reset();
    this.policyForm.get('rspCidPrefix')?.reset();
    if (!value) {
      this.rspPoolIdList = [];
      this.rspCidPrefixList = [];
      return;
    }
    this.rspPoolIdList = value === this.rspAppIdJoy ? this.rspPoolIdJoyList : this.rspPoolIdNweList;
    this.rspCidPrefixList = value === this.rspAppIdJoy ? this.rspCidPrefixJoyList : this.rspCidPrefixNweList;
  }

  buildFactorySelectConfig(): any {
    return this.homeService.buildFactorySelectConfig(this.factorySearchConfigExtraParam);
  }

  simCosTypeChange(value: any): void {
    this.form.get('simCosFileId')?.reset();
    if (value === SimCosTypeEnum.SELF) {
      this.form.get('simCosFileId')?.setValidators(this.homeService.requiredValidator);
    } else {
      this.form.get('simCosFileId')?.removeValidators(this.homeService.requiredValidator);
    }
    this.showSimCosFileId = value === SimCosTypeEnum.SELF;
  }

  simPrintTypeChange(value: any): void {
    this.form.get('simPrintFileId')?.reset();
    if (value === SimPrintTypeEnum.NEW_DESIGN) {
      this.form.get('simPrintFileId')?.setValidators(this.homeService.requiredValidator);
    } else {
      this.form.get('simPrintFileId')?.removeValidators(this.homeService.requiredValidator);
    }
    this.showSimPrintFileId = value === SimPrintTypeEnum.NEW_DESIGN;
  }

  reuseModeChange(value: any): void {
    if (!value) {
      return;
    }
    this.policyForm.patchValue({reuseAmount: 1});
    this.showReuseAmount = value !== this.reuseModeOnce;
  }

  rspPinpukModeChange(value: any): void {
    if (!value) {
      return;
    }
    this.showRspPinpukAttr = value === this.rspPinpukModeDesignated;

    const rspPinpukAttrControls = [this.policyForm.get('pin1'), this.policyForm.get('puk1'), this.policyForm.get('pin2'),
      this.policyForm.get('puk2')];

    if (this.showRspPinpukAttr) {
      rspPinpukAttrControls.forEach(v => v?.setValidators(this.homeService.requiredValidator));
    } else {
      rspPinpukAttrControls.forEach(v => v?.removeValidators(this.homeService.requiredValidator));
    }
    rspPinpukAttrControls.forEach(v => v?.reset());
  }

  clickPolicyId(): void {
    const data = {...this.policyForm.value, simQuantity: this.form?.value?.simQuantity};
    this.policyNwModalRef = this.nwModalService.open(OrderPolicyModalComponent, ConfigConstant.xlPlugSizeModal(data));
    this.policyNwModalRef.onClose().subscribe((resp: any) => {
      if (!!resp) {
        this.policyForm.patchValue(resp);
        this.policyId = resp?.policyId;
      }
    });
  }

  destroy(): void {
    if (!!this.policyNwModalRef) {
      this.policyNwModalRef.close();
    }
  }

  switchStep(value: any): void {
    const stepIndex = value?.fromStep?._stepIndex;
    if (stepIndex === 0){
      if (!this.form.valid) {
        NwFormUtils.validate(this.form);
      }
    } else if (stepIndex === 1) {
      if (!this.policyForm.valid) {
        NwFormUtils.validate(this.policyForm);
      }
    } else if (stepIndex === 2) {
      if (!this.deliverForm.valid) {
        NwFormUtils.validate(this.deliverForm);
      }
    }
  }


  override resetForm(): void {
     NwFormUtils.reset(this.form);
     NwFormUtils.reset(this.policyForm);
     NwFormUtils.reset(this.deliverForm);
  }
}
