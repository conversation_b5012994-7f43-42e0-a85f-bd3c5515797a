<nw-panel>
  <nw-panel-header label="{{'内部生产审核'| nwi18n}}" [icons]="['close']">
  </nw-panel-header>
  <nw-panel-content>
    <nw-stepper (finishStep)="submit()">
      <nw-step label="{{'采购合同'|nwi18n}}">
        <div>
          <nw-form-field label="{{'采购订单号'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.contractNo}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'客户名称'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.custName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'制卡工厂'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.factoryName}}"></nw-input>
          </nw-form-field>

          <nw-form-field label="{{'制卡类型'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.simTypeName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'制卡数量'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.simQuantity}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'制卡规格'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.simSpecName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'COS类型'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.simCosTypeName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'COS版本'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required *ngIf="data?.simCosType === SimCosTypeEnum.SELF">
            <nw-input disabled value="{{data?.simCosFileName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'版面供装方式'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.simPrintTypeName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'版面文件'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" *ngIf="data?.simPrintType === SimPrintTypeEnum.NEW_DESIGN && data?.simPrintFileId" required>
            <button nw-button color="white" icon="fa-download" (click)="download(data?.simPrintFileId)">{{'i18n_public.download.button'| nwi18n}}</button>
          </nw-form-field>
          <nw-form-field label="{{'采购合同附件1'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" *ngIf="data?.contractFileId1" required>
            <button nw-button color="white" icon="fa-download" (click)="download(data?.contractFileId1)">{{'i18n_public.download.button'| nwi18n}}</button>
          </nw-form-field>
          <nw-form-field label="{{'采购合同附件2'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" *ngIf="data?.contractFileId2">
            <button nw-button color="white" icon="fa-download" (click)="download(data?.contractFileId2)">{{'i18n_public.download.button'| nwi18n}}</button>
          </nw-form-field>
          <nw-form-field label="{{'备注'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6">
            <nw-textarea disabled value="{{data?.contractRemark}}"></nw-textarea>

          </nw-form-field>
        </div>
      </nw-step>
      <nw-step label="{{'制卡数据'|nwi18n}}">
        <div>
          <nw-form-field label="{{'复用模式'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.reuseModeName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'复用次数'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.reuseAmount}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'智能选号策略'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.policyName}}"></nw-input>
          </nw-form-field>

          <ng-container *ngIf="data?.policyId === PolicyEnum.SPECIFIED_RANGE">
            <nw-form-field label="{{'首ICCID' | nwi18n}}"
                           layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" >
              <nw-input-group>
                <nw-input nw-input-group-prepend disabled value="{{'ICCID'}}" [style]="formService.cardTypeStyle"></nw-input>
                <nw-input disabled value="{{data?.beginIccid}}"></nw-input>
              </nw-input-group>
            </nw-form-field>
            <nw-form-field label="{{'尾ICCID' | nwi18n}}"
                           layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" >
              <nw-input-group>
                <nw-input nw-input-group-prepend disabled value="{{'ICCID'}}" [style]="formService.cardTypeStyle"></nw-input>
                <nw-input disabled value="{{data?.endIccid}}"></nw-input>
              </nw-input-group>
            </nw-form-field>
          </ng-container>

          <ng-container *ngIf="data?.policyId === PolicyEnum.BLOCK">

          </ng-container>

          <nw-form-field label="{{'预览生产数据'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <button nw-button outline color="white" size="small" (click)="formService.previewSimData(data?.orderId)"
                    icon="fa fa-graduation-cap"></button>
          </nw-form-field>

          <nw-form-field label="{{'RSP+平台'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.rspAppName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'RSP+CID前缀'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.rspCidPrefix}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'RSP+资源池'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.rspPoolName}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'RSP+PinPuk生成模式'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{data?.rspPinpukModeName}}"></nw-input>
          </nw-form-field>
        </div>
      </nw-step>
      <nw-step label="{{'制卡交付'|nwi18n}}">
        <div>
          <nw-form-field label="{{'卡厂交付日期'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{DateUtils.formatCompactDateToLocale(data?.factoryDeliverDate, locale)}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'客户交付日期'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input disabled value="{{DateUtils.formatCompactDateToLocale(data?.custDeliverDate, locale)}}"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'备注'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6">
            <nw-textarea disabled value="{{data?.deliverRemark}}"></nw-textarea>
          </nw-form-field>
        </div>
      </nw-step>
      <nw-step label="{{'审核处理'|nwi18n}}">
        <div [formGroup]="formService.form">
          <nw-form-field label="{{'审核结果'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-radio-group formControlName="result"
                            inline="true">
              <nw-radio *ngFor="let item of formService?.auditResultList"
                        [value]="item.id">
                {{item.text}}
              </nw-radio>
            </nw-radio-group>
          </nw-form-field>
          <nw-form-field label="{{'原因'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6">
            <nw-textarea formControlName="remark">
            </nw-textarea>
          </nw-form-field>
        </div>
      </nw-step>
    </nw-stepper>
  </nw-panel-content>
</nw-panel>
