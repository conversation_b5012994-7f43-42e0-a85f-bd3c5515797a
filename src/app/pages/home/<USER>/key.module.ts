import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DictTools} from '../../global/dict.tools';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule,
  NwDatetimeModule,
  NwDatetimeRangeModule,
  NwDialogModule,
  NwFormFieldModule, NwInputFileModule,
  NwInputGroupModule,
  NwInputModule, NwInputMultipleModule,
  NwLabelModule,
  NwModalService,
  NwPanelModule,
  NwRadioModule,
  NwScrollAnchorModule,
  NwSelectModule,
  NwSelectTreeModule,
  NwStepperModule,
  NwSwitchModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwTreeModule,
  NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {KeyRoutingModule} from './key-routing.module';
import {KeyMgmtComponent} from './mgmt/key-mgmt.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NwAuthModule} from "@ng-nwui/integration";
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {KeyDetailComponent} from "./detail/key-detail.component";
import {KeyCreateComponent} from "./create/key-create.component";
import {KeyEditComponent} from "./edit/key-edit.component";

@NgModule({
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    KeyRoutingModule,
    NwInputGroupModule,
    PipeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwStepperModule,
    NwDatetimeModule,
    NwInputMultipleModule,
    NwInputFileModule
  ],
  declarations: [
    KeyMgmtComponent,
    KeyDetailComponent,
    KeyCreateComponent,
    KeyEditComponent
  ],
  providers: [DictTools, NwModalService]
})
export class KeyModule {
}
