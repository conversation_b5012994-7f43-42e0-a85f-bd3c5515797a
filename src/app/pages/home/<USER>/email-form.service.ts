import {<PERSON><PERSON><PERSON><PERSON>, FormBuilder} from '@angular/forms';
import {NwDialogService} from '@ng-nwui/components';
import {NwI18nService, NwValidatorResult, NwValidators} from '@ng-nwui/core';
import {HttpClient} from 'src/app/services/http.client';
import {DictTools} from '../../global/dict.tools';
import {DictCode} from '../../global/enums/dict-code.enum';
import {BusinessConstant} from '../../global/conts/business.constant';
import {FormService} from '../form.service';
import {ApiConstant, ConfigConstant} from '../../global/conts';
import {DomSanitizer, SafeHtml} from '@angular/platform-browser';

export class EmailFormService extends FormService {
  operationList!: any;

  emailTpl!: any;
  emailTplStyle = {border: '1px solid #e5e6e7'};

  emailTplMap = new Map<any, any>();

  constructor(
    protected fb: FormBuilder,
    protected nwDialogService: NwDialogService,
    protected apiService: HttpClient,
    protected nwI18nService: NwI18nService,
    protected dictTools: DictTools,
    protected sanitizer: DomSanitizer) {
    super(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  override initForm(): void {
    this.form = this.fb.group({
      factoryId: [null, [this.homeService.requiredValidator]],
      operation: [null, [this.homeService.requiredValidator]],
      subject: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(100)]],
      sendTo: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(100), this.homeService.emailValidator]],
      ccTo: [null, [this.homeService.maxLengthValidatorFn(100), this.homeService.emailListVerify]],
      sendBy: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(100), this.homeService.emailValidator]],
    });
  }

  override getDictList(): void {
    this.dictTools.getDictList(DictCode.BUSI_OPERATION).subscribe(resp => {
      this.operationList = resp.filter((v: any) => BusinessConstant.BUSI_OPERATION_FACTORY.some(k => k === v.id));
    });
  }

  override setForm(formValue: any): void {
    const processedFormValue = {...formValue, ccTo: formValue?.ccTo?.split(',')?.map((v: any) => ({id: v, text: v}))};
    super.setForm(processedFormValue);
    this.operationChange(processedFormValue?.operation);
  }

  override buildParams(): any {
    const result = Object.assign({}, this.form.value);
    result.ccTo = result.ccTo?.map((v: any) => v.id)?.join(',');
    return result;
  }

  operationChange(value: any): void {
    this.emailTpl = null;
    if (!value) {
      return;
    }
    this.getTpl(value);
  }

  getTpl(operation: any): void {
    if (this.emailTplMap.has(operation)) {
      this.emailTpl = this.emailTplMap.get(operation);
      return;
    }
    this.apiService.post(`${ApiConstant.EMAIL_TPL_DETAIL}/${operation}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.emailTpl = this.sanitizer.bypassSecurityTrustHtml(resp?.mailBody);
      this.emailTplMap.set(operation, this.emailTpl);
    });
  }

  ccToChange(value: any): void {
    console.log(value);
    // this.form?.get('ccTo')?.updateValueAndValidity();
  }
}
