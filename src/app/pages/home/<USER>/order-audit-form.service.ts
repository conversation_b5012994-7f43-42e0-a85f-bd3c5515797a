import {FormB<PERSON>er, FormGroup} from '@angular/forms';
import {NwDialogService, NwInputFileHook, NwModalService} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {HttpClient} from 'src/app/services/http.client';
import {DictTools} from '../../global/dict.tools';
import {DictCode} from '../../global/enums/dict-code.enum';
import {FormService} from '../form.service';
import {ConfigConstant} from '../../global/conts';
import {FunCodeGlobalConstant} from '../../global/conts/fun-code-global.constant';
import {DateUtils} from '../../global/utils';
import {SimCosTypeEnum} from '../../global/enums/sim.cos.type.enum';
import {SimPrintTypeEnum} from '../../global/enums/sim.print.type.enum';
import {OrderPolicyModalComponent} from './component/policy-modal/order-policy-modal.component';
import {NwModalRef} from '@ng-nwui/components/elements/dialogs/modal.service';
import {OrderSimModalComponent} from "./component/order-sim-modal/order-sim-modal.component";

export class OrderAuditFormService extends FormService {

  constructor(
    protected fb: FormBuilder,
    protected nwDialogService: NwDialogService,
    protected apiService: HttpClient,
    protected nwI18nService: NwI18nService,
    protected dictTools: DictTools,
    protected nwModalService: NwModalService) {
    super(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  operationList!: any;
  auditResultList!: any;

  orderSimNwModalRef!: any;



  dateConfig = {
    minDate: new Date(),
    autoApply: false,
    format: DateUtils.getDateFormatter(this.nwI18nService.getCurrentLocale() as string)
  };

  cardTypeStyle = 'min-width: 75px';
  emailTplStyle = {border: '1px solid #e56e7'};

  override initForm(): void {
    this.form = this.fb.group({
      operation: ['02', [this.homeService.requiredValidator]],
      result: ['00', [this.homeService.requiredValidator]],
      remark: [null, [this.homeService.maxLengthValidatorFn(512)]],
    });
  }

  override getDictList(): void {
    this.dictTools.getMulitDictList([
      DictCode.BUSI_OPERATION, DictCode.AUDIT_RESULT
    ]).subscribe(resp => {
      this.operationList = resp[DictCode.BUSI_OPERATION];
      this.auditResultList = resp[DictCode.AUDIT_RESULT];
    });
  }

  previewSimData(orderId: any, showCid = false): void {
    this.orderSimNwModalRef = this.nwModalService.open(OrderSimModalComponent, ConfigConstant.xlSizeModal({orderId, showCid}));
  }

  destroy(): void {
    if (!!this.orderSimNwModalRef) {
      this.orderSimNwModalRef.close();
    }
  }
}
