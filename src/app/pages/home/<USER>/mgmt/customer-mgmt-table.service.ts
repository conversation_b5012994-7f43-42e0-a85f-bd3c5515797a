import {TableSearchService} from '../../../global/table-search.service';
import {ApiConstant} from '../../../global/conts/api.constant';
import {NwI18nService} from '@ng-nwui/core';
import {NwTableConfig} from '@ng-nwui/components';
import {HttpClient} from '../../../../services/http.client';
import {DateUtils} from '../../../global/utils';

export class CustomerMgmtTableService extends TableSearchService {

  constructor(protected nwI18nService: NwI18nService, protected httpService: HttpClient) {
    super(httpService);
  }

  tableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'name',
        title: this.nwI18nService.getI18nValue('i18n_customer.form.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'shortName',
        title: this.nwI18nService.getI18nValue('i18n_customer.form.short.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'contactName',
        title: this.nwI18nService.getI18nValue('i18n_customer.form.contact.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'contactPhone',
        title: this.nwI18nService.getI18nValue('i18n_customer.form.contact.phone'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'status',
        title: this.nwI18nService.getI18nValue('i18n_public.status'),
        bodyType: 'template',
      },
      {
        id: 'createName',
        title: this.nwI18nService.getI18nValue('i18n_production.create.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'createTime',
        title: this.nwI18nService.getI18nValue('i18n_production.create.time'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
      {
        id: 'updateName',
        title: this.nwI18nService.getI18nValue('i18n_production.update.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'updateTime',
        title: this.nwI18nService.getI18nValue('i18n_production.update.time'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
    ]
  };

  getSearchUrl(): string {
    return ApiConstant.CUSTOMER_LIST;
  }
}
