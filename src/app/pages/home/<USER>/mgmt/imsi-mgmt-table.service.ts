import {TableSearchService} from '../../../global/table-search.service';
import {ApiConstant} from '../../../global/conts/api.constant';
import {NwI18nService} from '@ng-nwui/core';
import {NwTableConfig} from '@ng-nwui/components';
import {HttpClient} from '../../../../services/http.client';
import {DateUtils} from '../../../global/utils';

export class ImsiMgmtTableService extends TableSearchService {

  constructor(protected nwI18nService: NwI18nService, protected httpService: HttpClient) {
    super(httpService);
  }

  tableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'iccid',
        title: this.nwI18nService.getI18nValue('ICCID'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'imsi',
        title: this.nwI18nService.getI18nValue('IMSI'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'msisdn',
        title: this.nwI18nService.getI18nValue('MSISDN'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'vendorName',
        title: this.nwI18nService.getI18nValue('供应商'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'reuseCount',
        title: this.nwI18nService.getI18nValue('重用次数'),
        style: { headerCellHorizontalAlign: 'center', bodyCellHorizontalAlign: 'center'},
      },
      {
        id: 'serviceStartDate',
        title: this.nwI18nService.getI18nValue('服务开始日期'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
      {
        id: 'serviceEndDate',
        title: this.nwI18nService.getI18nValue('服务结束日期'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
      {
        id: 'status',
        title: this.nwI18nService.getI18nValue('状态'),
        bodyType: 'template',
      },
      // {
      //   id: 'createName',
      //   title: this.nwI18nService.getI18nValue('创建工号'),
      //   style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      // },
      // {
      //   id: 'createTime',
      //   title: this.nwI18nService.getI18nValue('创建时间'),
      //   style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      //   bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      // },
      {
        id: 'updateName',
        title: this.nwI18nService.getI18nValue('更新工号'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'updateTime',
        title: this.nwI18nService.getI18nValue('更新时间'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
    ]
  };

  getSearchUrl(): string {
    return ApiConstant.IMSI_LIST;
  }
}
