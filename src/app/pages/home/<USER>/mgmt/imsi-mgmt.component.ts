import {Component, OnInit, ViewChild} from '@angular/core';
import {
  NwDatetimeRangeConfig,
  NwDialogService, NwDropDownItem,
  NwModalService,
  NwScrollAnchorAuto,
  NwSelect,
  NwSelectConfig,
  NwSelectData,
  NwTable,
} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {FormBuilder, Validators} from '@angular/forms';
import {ApiConstant,SystemConstant,RegularConstant} from '../../../global/conts';
import {ImsiMgmtTableService} from './imsi-mgmt-table.service';
import {HttpClient} from '../../../../services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {FunCodeGlobalConstant} from 'src/app/pages/global/conts/fun-code-global.constant';
import {HomeService} from '../../home.service';
import {ColorConstant} from "../../../global/conts/color.constant";
import {DateUtils} from "../../../global/utils";
import {DictTools} from "../../../global/dict.tools";
import {DictCode} from "../../../global/enums/dict-code.enum";

@Component({
  selector: 'app-imsi-mgmt',
  templateUrl: './imsi-mgmt.component.html',
  styleUrls: ['./imsi-mgmt.component.scss']
})
export class ImsiMgmtComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,public dictTools: DictTools,
              private nwDialogService: NwDialogService, protected nwModalService: NwModalService,
  ) {
    this.tableService = new ImsiMgmtTableService(nwI18nService, apiService);
    this.homeService = new HomeService(apiService, nwI18nService);
  }

  SystemConstant = SystemConstant;
  ColorConstant = ColorConstant;
  @ViewChild('nwTable')
  nwTable?: NwTable;

  @ViewChild('detailScrollAnchor', {read: NwScrollAnchorAuto})
  detailScrollAnchor?: NwScrollAnchorAuto;
  @ViewChild('createScrollAnchor', {read: NwScrollAnchorAuto})
  createScrollAnchor?: NwScrollAnchorAuto;
  @ViewChild('editScrollAnchor', {read: NwScrollAnchorAuto})
  editScrollAnchor?: NwScrollAnchorAuto;
  @ViewChild('renewScrollAnchor', {read: NwScrollAnchorAuto})
  renewScrollAnchor?: NwScrollAnchorAuto;
  tableService!: ImsiMgmtTableService;
  lockItems: NwDropDownItem[] = [
    {id: 'batchLock', text: this.nwI18nService.getI18nValue('批量锁定')}];
  unLockItems: NwDropDownItem[] = [
    {id: 'batchUnlock', text: this.nwI18nService.getI18nValue('批量解锁')}];
  terminateItems: NwDropDownItem[] = [
    {id: 'batchTerminate', text: this.nwI18nService.getI18nValue('批量终止')}];
  // 时间配置
  locale: any = this.nwI18nService.getCurrentLocale() as string;
  dateConfig: NwDatetimeRangeConfig = {
    format: DateUtils.getDateFormatter(this.locale),
  };
  formBuilder = this.fb.group({
    startIccid: ['', [Validators.pattern(RegularConstant.ICCID)]],
    endIccid: ['', [Validators.pattern(RegularConstant.ICCID)]],
    vendorId: [''],
    status: [''],
    serviceEndDateArray:[]
  });
  formBuilderStatus = this.fb.group({
    startIccid: ['', [Validators.pattern(RegularConstant.ICCID), Validators.required]],
    endIccid: ['', [Validators.pattern(RegularConstant.ICCID), Validators.required]]
  });
  showDetail = false;
  showLock = false;
  lockTitle = '批量锁定';
  editType="";
  showRenew = false;
  showBatchImport = false;
  statusList !: NwSelectData[];
  vendorList !: NwSelectData[];
  currentSelectRow: any;

  selectConfig: NwSelectConfig = {clearable: true};
  FunCodeGlobalConstant = FunCodeGlobalConstant;
  homeService!: HomeService;

  ngOnInit(): void {
    this.doSearch();
    this.dictTools.getDictList(DictCode.IMSI_STATUS).subscribe((resp: any) => {
      this.statusList = resp;
    });
    this.dictTools.getDictList(DictCode.IMSI_VENDOR).subscribe((resp: any) => {
      this.vendorList = resp;
    });
  }

  onStartIccidChange(): void {
    const startIccid = this.formBuilderStatus.value.startIccid;
    if (startIccid && startIccid.length >= 15) {
      // 如果结束ICCID为空，自动设置为开始ICCID + 1
      if (!this.formBuilderStatus.value.endIccid) {
        const prefix = startIccid.substring(0, startIccid.length - 5);
        const suffix = startIccid.substring(startIccid.length - 5);
        const nextSuffix = (parseInt(suffix) + 1).toString().padStart(5, '0');
        this.formBuilderStatus.patchValue({
          endIccid: prefix + nextSuffix
        });
      }
    }
  }
  choiceLock(item:any){
    if(item.id == 'batchLock'){
      this.closePanel();
      this.lockTitle='批量锁定';
      this.showLock = true;
      this.editType='lock';
    }else{
     var params = {
       startIccid: this.currentSelectRow.iccid,
       endIccid:this.currentSelectRow.iccid,
      };
      this.lock(params);
    }
  }
  choiceTerminate(item:any){
    if(item.id == 'batchTerminate'){
      this.closePanel();
      this.lockTitle='批量终止';
      this.showLock = true;
      this.editType='terminate';
    }else{
      var params = {
        startIccid: this.currentSelectRow.iccid,
        endIccid:this.currentSelectRow.iccid,
      };
      this.delete(params);
    }
  }

  choiceUnlock(item:any){
    if(item.id == 'batchUnlock'){
      this.closePanel();
      this.lockTitle='批量解锁';
      this.showLock = true;
      this.editType='unlock';
    }else{
      var params = {
        startIccid: this.currentSelectRow.iccid,
        endIccid:this.currentSelectRow.iccid,
      };
      this.unlock(params);
    }
  }
  editImsi(){
    if (NwFormUtils.validate(this.formBuilderStatus)) {
      const formValue = this.formBuilderStatus.value;
      var params= {
        startIccid: formValue.startIccid,
        endIccid: formValue.endIccid,
      };
      if (this.editType=='lock'){
        this.lock(params);
      }else if (this.editType=='terminate'){
        this.delete(params);
      }else if (this.editType=='unlock'){
        this.unlock(params);
      }
    }
  }
  selectRow(row: any): void {
    this.tableService.onSelectRow(row);
    this.currentSelectRow = row.currentSelected;
    this.closePanel();
    if(this.currentSelectRow.status=='00'){
      this.lockItems = [
        {id: 'batchLock', text: this.nwI18nService.getI18nValue('批量锁定')},
        {id: 'lock', text: this.nwI18nService.getI18nValue('锁定')}
      ];
      this.unLockItems = [
        {id: 'batchUnlock', text: this.nwI18nService.getI18nValue('批量解锁')},
      ];
      this.terminateItems = [
        {id: 'batchTerminate', text: this.nwI18nService.getI18nValue('批量终止')},
        {id: 'terminate', text: this.nwI18nService.getI18nValue('终止')}
      ];
    }else if(this.currentSelectRow.status=='01'){
      this.lockItems = [
        {id: 'batchLock', text: this.nwI18nService.getI18nValue('批量锁定')}
      ];
      this.unLockItems = [
        {id: 'batchUnlock', text: this.nwI18nService.getI18nValue('批量解锁')},
        {id: 'unlock', text: this.nwI18nService.getI18nValue('解锁')}
      ];
      this.terminateItems = [
        {id: 'batchTerminate', text: this.nwI18nService.getI18nValue('批量终止')},
        {id: 'terminate', text: this.nwI18nService.getI18nValue('终止')}
      ];
    }else{
      this.lockItems = [
        {id: 'batchLock', text: this.nwI18nService.getI18nValue('批量锁定')},
      ];
      this.unLockItems = [
        {id: 'batchUnlock', text: this.nwI18nService.getI18nValue('批量解锁')},
      ];
      this.terminateItems = [
        {id: 'batchTerminate', text: this.nwI18nService.getI18nValue('批量终止')}
      ];
    }
  }

  onChangePage(pageNum: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePage(pageNum);
  }

  onChangePageSize(pageSize: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePageSize(pageSize);
  }

  doSearch(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      this.currentSelectRow = null;
      this.tableService.onSearch(this.buildParams());
      this.closePanel();
    }
    NwFormUtils.reset(this.formBuilderStatus);
  }

  buildParams(): any {
    const result = Object.assign({}, this.formBuilder.value);
    // 清除空值
    Object.keys(result).forEach(key => {
      if (result[key] === '' || result[key] === null || result[key] === undefined) {
        delete result[key];
      }
    });
    if(result.serviceEndDateArray){
      result.serviceEndDateBegin = DateUtils.formatDateToCompactThenAppendStartTimeOfDay(result.serviceEndDateArray[0], this.locale);
      result.serviceEndDateEnd = DateUtils.formatDateToCompactThenAppendEndTimeOfDay(result.serviceEndDateArray[1], this.locale);
    }
    return result;
  }

  reset(): void {
    NwFormUtils.reset(this.formBuilder);
    NwFormUtils.reset(this.formBuilderStatus);
    this.doSearch();
  }

  closePanel(): void {
    this.showDetail = false;
    this.showRenew = false;
    this.showBatchImport = false;
    this.showLock=false;
    NwFormUtils.reset(this.formBuilderStatus);
  }

  detail(): void {
    this.closePanel();
    this.showDetail = true;
  }

  renew(): void {
    this.closePanel();
    this.showRenew = true;
  }

  batchImport(): void {
    this.closePanel();
    this.showBatchImport = true;
  }

  lock(params:any): void {

    this.apiService.post(ApiConstant.IMSI_LOCK, params,
      ConfigConstant.noLoadingConfig()).subscribe((res: any) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
        this.doSearch();
      });
    }, (error) => {
      this.nwDialogService.error(error?.apiResponse?.message);
    });
  }

  unlock(params:any): void {

    this.apiService.post(ApiConstant.IMSI_UNLOCK, params,
      ConfigConstant.noLoadingConfig()).subscribe((res: any) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
        this.doSearch();
      });
    }, (error) => {
      this.nwDialogService.error(error?.apiResponse?.message);
    });
  }

  delete(params:any): void {
    this.nwDialogService.confirm(this.nwI18nService.getI18nValue('i18n_public.terminate.tips'), () => {
      this.apiService.post(ApiConstant.IMSI_TERMINATE, params,
        ConfigConstant.noLoadingConfig()).subscribe((res: any) => {
        this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
          this.doSearch();
        });
      }, (error) => {
        this.nwDialogService.error(error?.apiResponse?.message);
      });
    });
  }

  // 导出功能
  // export(): void {
  //   const params = this.buildParams();
  //   this.apiService.download(`${ApiConstant.IMSI_LIST}/export`, params).subscribe((res: any) => {
  //     const blob = new Blob([res], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
  //     const url = window.URL.createObjectURL(blob);
  //     const link = document.createElement('a');
  //     link.href = url;
  //     link.download = `IMSI_List_${new Date().getTime()}.xlsx`;
  //     link.click();
  //     window.URL.revokeObjectURL(url);
  //   });
  // }
}
