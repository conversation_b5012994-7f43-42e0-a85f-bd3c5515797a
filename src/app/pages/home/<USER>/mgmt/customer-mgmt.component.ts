import {Component, OnInit, ViewChild} from '@angular/core';
import {
  NwDialogService,
  NwModalService,
  NwScrollAnchorAuto,
  NwSelect,
  NwSelectConfig,
  NwTable,
} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {ApiConstant} from '../../../global/conts/api.constant';
import {SystemConstant} from '../../../global/conts/system.constant';
import {CustomerMgmtTableService} from './customer-mgmt-table.service';
import {HttpClient} from '../../../../services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {FunCodeGlobalConstant} from 'src/app/pages/global/conts/fun-code-global.constant';
import {ColorConstant} from '../../../global/conts/color.constant';
import { HomeService } from '../../home.service';

@Component({
  selector: 'app-profile-template',
  templateUrl: './customer-mgmt.component.html',
  styleUrls: ['./customer-mgmt.component.scss']
})
export class CustomerMgmtComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, protected nwModalService: NwModalService,
  ) {
    this.tableService = new CustomerMgmtTableService(nwI18nService, apiService);
    this.homeService = new HomeService(apiService, nwI18nService);
  }

  SystemConstant = SystemConstant;

  ColorConstant = ColorConstant;

  @ViewChild('nwTable')
  nwTable?: NwTable;

  @ViewChild('detailScrollAnchor', {read: NwScrollAnchorAuto})
  detailScrollAnchor?: NwScrollAnchorAuto;
  tableService!: CustomerMgmtTableService;

  formBuilder = this.fb.group({
    keyword: [''],
  });

  showDetail = false;
  showCreate = false;
  showEdit = false;

  currentSelectRow: any;

  selectConfig: NwSelectConfig = {clearable: true};

  FunCodeGlobalConstant = FunCodeGlobalConstant;
  homeService!: HomeService;

  ngOnInit(): void {
    this.doSearch();
  }

  selectRow(row: any): void {
    this.tableService.onSelectRow(row);
    this.currentSelectRow = row.currentSelected;
    this.closePanel();
  }

  onChangePage(pageNum: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePage(pageNum);
  }

  onChangePageSize(pageSize: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePageSize(pageSize);
  }

  doSearch(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      this.currentSelectRow = null;
      this.tableService.onSearch(this.buildParams());
      this.closePanel();
    }
  }

  buildParams(): any {
    const result = Object.assign({}, this.formBuilder.value);
    return result;
  }

  reset(): void {
    NwFormUtils.reset(this.formBuilder);
    this.doSearch();
  }

  closePanel(): void {
    this.showDetail = false;
    this.showCreate = false;
    this.showEdit = false;
  }

  detail(): void {
    this.closePanel();
    this.showDetail = true;
  }

  edit(): void {
    this.closePanel();
    this.showEdit = true;
  }

  create(): void {
    this.closePanel();
    this.showCreate = true;
  }

  delete(): void {
    if (!this.currentSelectRow.custId) {
      return;
    }

    this.nwDialogService.confirm(this.nwI18nService.getI18nValue('i18n_public.delete.tips'), () => {
      this.apiService.post(`${ApiConstant.CUSTOMER_DELETE}/${this.currentSelectRow?.custId}`, null,
        ConfigConstant.noLoadingConfig()).subscribe((res: any) => {
        this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
          this.doSearch();
        });
      }, (error) => {
        this.nwDialogService.error(error?.apiResponse?.message);
      });
    });
  }
}
