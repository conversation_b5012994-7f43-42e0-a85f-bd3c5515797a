import {Component, OnInit, ViewChild} from '@angular/core';
import {
  NwDialogService,
  NwModalService,
  NwScrollAnchorAuto,
  NwSelect,
  NwSelectConfig,
  NwTable,
} from '@ng-nwui/components';
import {NwFormUtils, NwHttpDownloadOptions, NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {ApiConstant} from '../../../global/conts/api.constant';
import {SystemConstant} from '../../../global/conts/system.constant';
import {KeyMgmtTableService} from './key-mgmt-table.service';
import {HttpClient} from '../../../../services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {FunCodeGlobalConstant} from 'src/app/pages/global/conts/fun-code-global.constant';
import {HomeService} from '../../home.service';
import {ColorConstant} from "../../../global/conts/color.constant";
import {DictTools} from "../../../global/dict.tools";
import {DictCode} from "../../../global/enums/dict-code.enum";
import {BusiOperationEnum} from "../../../global/enums/busi.operation.enum";
import {BusinessConstant} from "../../../global/conts/business.constant";
import {KeyTypeEnum} from "../../../global/enums/key.type.enum";
import {FileSuffixConstant} from "../../../global/conts/file-suffix.constant";

@Component({
  selector: 'app-profile-template',
  templateUrl: './key-mgmt.component.html',
  styleUrls: ['./key-mgmt.component.scss']
})
export class KeyMgmtComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder, public dictTools: DictTools,
              private nwDialogService: NwDialogService, protected nwModalService: NwModalService,
  ) {
    this.tableService = new KeyMgmtTableService(nwI18nService, apiService);
    this.homeService = new HomeService(apiService, nwI18nService);
  }


  SystemConstant = SystemConstant;
  ColorConstant = ColorConstant;
  KeyType = KeyTypeEnum;

  @ViewChild('nwTable')
  nwTable?: NwTable;

  @ViewChild('detailScrollAnchor', {read: NwScrollAnchorAuto})
  detailScrollAnchor?: NwScrollAnchorAuto;
  tableService!: KeyMgmtTableService;

  formBuilder = this.fb.group({
    factoryId: [''],
    operation: [''],
  });

  showDetail = false;
  showCreate = false;
  showEdit = false;

  currentSelectRow: any;

  selectConfig: NwSelectConfig = {clearable: true};

  FunCodeGlobalConstant = FunCodeGlobalConstant;
  homeService!: HomeService;

  operationList!: any;
  imsiOperationList!: any;
  factoryOperationList!: any;

  ngOnInit(): void {
    this.getDictList();
    this.doSearch();
  }

  getDictList(): void {
    this.dictTools.getDictList(DictCode.BUSI_OPERATION).subscribe(resp => {
      this.imsiOperationList = resp.filter((v: any) => BusinessConstant.BUSI_OPERATION_IMSI.some(k => k === v.id));
      this.factoryOperationList = resp.filter((v: any) => BusinessConstant.BUSI_OPERATION_FACTORY.some(k => k === v.id));
    });
  }

  selectRow(row: any): void {
    this.tableService.onSelectRow(row);
    this.currentSelectRow = row.currentSelected;
    this.closePanel();
  }

  onChangePage(pageNum: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePage(pageNum);
  }

  onChangePageSize(pageSize: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePageSize(pageSize);
  }

  factoryChange(value: any): void {
    if (!value) {
      return;
    }
    this.formBuilder.get('operation')?.reset();
    this.operationList = value === BusinessConstant.FACTORY_ID_IMSI ? this.imsiOperationList : this.factoryOperationList;
  }

  doSearch(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      this.currentSelectRow = null;
      this.tableService.onSearch(this.buildParams());
      this.closePanel();
    }
  }

  buildParams(): any {
    const result = Object.assign({}, this.formBuilder.value);
    return result;
  }

  reset(): void {
    NwFormUtils.reset(this.formBuilder);
    this.doSearch();
  }

  closePanel(): void {
    this.showDetail = false;
    this.showCreate = false;
    this.showEdit = false;
  }

  detail(): void {
    this.closePanel();
    this.showDetail = true;
  }

  edit(): void {
    this.closePanel();
    this.showEdit = true;
  }

  create(): void {
    this.closePanel();
    this.showCreate = true;
  }

  delete(): void {
    if (!this.currentSelectRow.keyId) {
      return;
    }

    this.nwDialogService.confirm(this.nwI18nService.getI18nValue('i18n_public.delete.tips'), () => {
      this.apiService.post(`${ApiConstant.KEY_DELETE}/${this.currentSelectRow?.keyId}`, null,
        ConfigConstant.noLoadingConfig()).subscribe((res: any) => {
        this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
          this.doSearch();
        });
      }, (error) => {
        this.nwDialogService.error(error?.apiResponse?.message);
      });
    });
  }

  export(): void {
    const currentSelectRow = this.currentSelectRow;
    if (!currentSelectRow) {
      return;
    }
    const fileNameSuffix = currentSelectRow?.keyAlg === 'PGP' ? FileSuffixConstant.asc : FileSuffixConstant.TXT;
    const fileName = `${currentSelectRow.factoryName}(${currentSelectRow.operationName})-${currentSelectRow.keyTypeName}(${currentSelectRow.keyAlg}).${fileNameSuffix}`;
    this.apiService.download(`${ApiConstant.KEY_EXPORT}/${this.currentSelectRow?.keyId}`, null, {fileName}).subscribe((res) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_public.success'));
    });
  }
}
