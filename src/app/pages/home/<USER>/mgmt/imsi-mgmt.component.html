<nw-panel>
  <nw-panel-header label="{{'查询条件'| nwi18n}}" [icons]="['collapse']">
  </nw-panel-header>
  <nw-panel-content>
    <div class="row" [formGroup]="formBuilder">
      <nw-form-field label="{{'供应商'| nwi18n}}" class="col-lg-3">
        <nw-select formControlName="vendorId"
                   [selectConfig]="selectConfig"
                   [selectData]="vendorList"
                   [placeholder]="nwI18nService.getI18nValue('请选择供应商')">
        </nw-select>

      </nw-form-field>
      <nw-form-field label="{{'ICCID Start'| nwi18n}}" class="col-lg-3">
        <nw-input formControlName="startIccid"
                  [maxlength]="20">
        </nw-input>
      </nw-form-field>
      <nw-form-field label="{{'ICCID End'| nwi18n}}" class="col-lg-3">
        <nw-input formControlName="endIccid"
                  [maxlength]="20">
        </nw-input>
      </nw-form-field>
      <nw-form-field label="{{'状态'| nwi18n}}" class="col-lg-3">
        <nw-select formControlName="status"
                   [selectConfig]="selectConfig"
                   [selectData]="statusList"
                   [placeholder]="nwI18nService.getI18nValue('请选择状态')">
        </nw-select>
      </nw-form-field>

      <nw-form-field label="{{'服务到期时间'| nwi18n}}" class="col-lg-3">
        <nw-datetime-range formControlName="serviceEndDateArray"
                           [config]="dateConfig">
        </nw-datetime-range>
      </nw-form-field>




    </div>
  </nw-panel-content>
  <nw-panel-footer>
    <button icon="fas fa-search" nw-button [color]="'primary'" [disabled]="!formBuilder.valid"
            (click)="doSearch()">{{'查询'|nwi18n}}</button>&nbsp;
    <button nw-button color="warning" icon="fa fa-undo" (click)="reset()">{{'重置'|nwi18n}}</button>&nbsp;
  </nw-panel-footer>
</nw-panel>

<nw-panel>
  <nw-panel-header
    label="{{'查询结果'| nwi18n}}">
  </nw-panel-header>
  <nw-panel-content>
    <nw-button-group>
      <button nw-button color="white" icon="fa-info-circle" [disabled]="!currentSelectRow"
              (click)="detail()">
        {{'详情'| nwi18n}}
      </button>
      <nw-dropdown  color="white"
                    icon="fa-lock"
                   direction="down"
                    *nwAuth="FunCodeGlobalConstant.IMSI_LOCK"
                   title="{{'锁定'|nwi18n}}"
                   align="left"
                   (itemClick)="choiceLock($event)"
                   [items]="lockItems">
      </nw-dropdown>

      <nw-dropdown  color="white"
                    icon="fa-lock-open"
                    direction="down"
                    *nwAuth="FunCodeGlobalConstant.IMSI_UNLOCK"
                    title="{{'解锁'|nwi18n}}"
                    align="left"
                    (itemClick)="choiceUnlock($event)"
                    [items]="unLockItems">
      </nw-dropdown>

      <nw-dropdown  color="white"
                    icon="fa-trash"
                    direction="down"
                    *nwAuth="FunCodeGlobalConstant.IMSI_TERMINATE"
                    title="{{'终止'|nwi18n}}"
                    align="left"
                    (itemClick)="choiceTerminate($event)"
                    [items]="terminateItems">
      </nw-dropdown>
      <button nw-button
              color="white"
              icon="fa-clock"
              (click)="renew()" *nwAuth="FunCodeGlobalConstant.IMSI_RENEW">{{'续期'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-upload"
              (click)="batchImport()" >{{'批量导入'| nwi18n}}</button>
<!--      *nwAuth="FunCodeGlobalConstant.IMSI_BATCH_IMPORT"-->
    </nw-button-group>

    <nw-table #nwTable
              [tableConfig]="tableService.tableConfig"
              [pageSizeOptions]="SystemConstant.DEFAULT_PAGE_SIZE_LIST"
              [tableData]="tableService.tableData"
              (changePage)="onChangePage($event)"
              (changePageSize)="onChangePageSize($event)"
              (selectRow)="selectRow($event)">
      <ng-template nw-table-column="status" let-row="row" let-cell="cell">
        <nw-label [color]='homeService.getColor(ColorConstant.IMSI_STATUS_COLOR, cell)'
                  *ngIf="cell">{{row.statusName}}</nw-label>
      </ng-template>
    </nw-table>
  </nw-panel-content>
</nw-panel>

<!-- 锁定解锁终止批量面板 -->
<nw-panel *ngIf="showLock" nw-scroll-anchor-auto>
  <nw-panel-header label="{{lockTitle}}" [icons]="['close']" (iconClick)="closePanel()">
  </nw-panel-header>
  <nw-panel-content>
    <div [formGroup]="formBuilderStatus">
      <nw-form-field label="{{'开始ICCID'| nwi18n}}" labelAlign="right"  layout="horizontal"
                     labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
        <nw-input formControlName="startIccid"
                  [placeholder]="nwI18nService.getI18nValue('请输入开始ICCID')"
                  [maxlength]="20"
                  (blur)="onStartIccidChange()">
        </nw-input>
        <div class="form-text text-muted">
          {{'格式：19-20位数字，如：89860000000000000001'| nwi18n}}
        </div>
      </nw-form-field>

      <nw-form-field label="{{'结束ICCID'| nwi18n}}" labelAlign="right" layout="horizontal"
                     labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
        <nw-input formControlName="endIccid"
                  [placeholder]="nwI18nService.getI18nValue('请输入结束ICCID')"
                  [maxlength]="20">
        </nw-input>
        <div class="form-text text-muted">
          {{'结束ICCID必须大于开始ICCID'| nwi18n}}
        </div>
      </nw-form-field>
    </div>
  </nw-panel-content>
  <nw-panel-footer>
    <div class="row">
      <div class="col-lg-3"></div>
      <div class="col-lg-9">
        <button nw-button color="primary" icon="fa-check" (click)="editImsi()" [disabled]="!formBuilderStatus.valid">
          {{'i18n_public.submit.button' | nwi18n}}
        </button>&nbsp;
        <button nw-button color="warning" icon="fa-times"
                (click)="closePanel()">{{'i18n_public.cancel.button'|nwi18n}}</button>
      </div>
    </div>
  </nw-panel-footer>
</nw-panel>

<!--Detail-->
<app-imsi-detail *ngIf="showDetail" [selectedRow]="currentSelectRow"  (closeReturn)="closePanel()"
                    #detailScrollAnchor nw-scroll-anchor-auto></app-imsi-detail>
<app-imsi-renew *ngIf="showRenew" (closeReturn)="$event ? doSearch() : closePanel()"
                    #renewScrollAnchor nw-scroll-anchor-auto></app-imsi-renew>
<app-imsi-batch-import *ngIf="showBatchImport" (closeReturn)="$event ? doSearch() : closePanel()"
                    #batchImportScrollAnchor nw-scroll-anchor-auto></app-imsi-batch-import>
