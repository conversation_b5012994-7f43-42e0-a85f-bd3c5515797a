<nw-panel>
  <nw-panel-header label="{{'i18n_public.query.result.condition'| nwi18n}}" [icons]="['collapse']">
  </nw-panel-header>
  <nw-panel-content>
    <div class="row" [formGroup]="formBuilder">
      <nw-form-field label="{{'合同编号'| nwi18n}}" class="col-lg-3">
        <nw-input formControlName="contractNo" [placeholder]="nwI18nService.getI18nValue('i18n_production.key.word')"></nw-input>
      </nw-form-field>
    </div>
  </nw-panel-content>
  <nw-panel-footer>
    <button icon="fas fa-search" nw-button [color]="'primary'" [disabled]="!formBuilder.valid"
            (click)="doSearch()">{{'i18n_public.search.button'|nwi18n}}</button>&nbsp;
    <button nw-button color="warning" icon="fa fa-undo" (click)="reset()">{{'i18n_public.reset.button'|nwi18n}}</button>
  </nw-panel-footer>
</nw-panel>
<nw-panel>

  <nw-panel-header
    label="{{'i18n_public.query.result.title'| nwi18n}}">
  </nw-panel-header>
  <nw-panel-content>
    <nw-button-group>
      <button nw-button color="white" icon="fa-info-circle" [disabled]="!currentSelectRow"
              (click)="detail()">
        {{'i18n_privilege.detail'| nwi18n}}
      </button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="edit()" *nwAuth="FunCodeGlobalConstant.ORDER_EDIT" [disabled]="disabledEdit()">{{'编辑'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="auditInternal(true)" *nwAuth="FunCodeGlobalConstant.ORDER_AUDIT_INTERNAL_OPERATOR" [disabled]="disabledAuditInternal()">{{'内部运营审核'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="auditInternal(false)" *nwAuth="FunCodeGlobalConstant.ORDER_AUDIT_INTERNAL_TECHNOLOGY" [disabled]="disabledAuditInternal()">{{'内部技术审核'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="auditExternal(true)" *nwAuth="FunCodeGlobalConstant.ORDER_AUDIT_EXTERNAL_OPERATOR" [disabled]="disabledAuditExternal()">{{'外部运营审核'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="auditExternal(false)" *nwAuth="FunCodeGlobalConstant.ORDER_AUDIT_EXTERNAL_TECHNOLOGY" [disabled]="disabledAuditExternal()">{{'外部技术审核'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="auditFirstCardRequest()" *nwAuth="FunCodeGlobalConstant.ORDER_FIRST_CARD_REQUEST" [disabled]="disabledFirstCardReviewRequest()">{{'首卡复核请求'| nwi18n}}</button>

      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="auditFirstCardReview()" *nwAuth="FunCodeGlobalConstant.ORDER_AUDIT_FIRST_CARD_REQUEST" [disabled]="disabledFirstCardReview()">{{'首卡复核确认'| nwi18n}}</button>

      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="delivery()" *nwAuth="FunCodeGlobalConstant.ORDER_DELIVERY" [disabled]="disabledDelivery()">{{'配送跟踪'| nwi18n}}</button>

      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="receipt()" *nwAuth="FunCodeGlobalConstant.ORDER_RECEIPT" [disabled]="disabledReceipt()">{{'配送签收'| nwi18n}}</button>
    </nw-button-group>

    <nw-table #nwTable
              [tableConfig]="tableService.tableConfig"
              [pageSizeOptions]="SystemConstant.DEFAULT_PAGE_SIZE_LIST"
              [tableData]="tableService.tableData"
              (changePage)="onChangePage($event)"
              (changePageSize)="onChangePageSize($event)"
              (selectRow)="selectRow($event)">
      <ng-template nw-table-column="status" let-row="row" let-cell="cell">
        <nw-label [color]='homeService.getColor(ColorConstant.ORDER_STATUS_COLOR, cell)'
                  *ngIf="cell">{{row.statusName}}</nw-label>
      </ng-template>
    </nw-table>
  </nw-panel-content>
</nw-panel>

<app-order-audit-internal-form *ngIf="showAuditInternal" [selectedRow]="currentSelectRow" [isOperator]="isOperator" (closeReturn)="$event ? doSearch() : closePanel()" nw-scroll-anchor-auto></app-order-audit-internal-form>
<app-order-audit-external-form *ngIf="showAuditExternal" [selectedRow]="currentSelectRow" [isOperator]="isOperator" (closeReturn)="$event ? doSearch() : closePanel()" nw-scroll-anchor-auto></app-order-audit-external-form>
<app-order-detail-form *ngIf="showDetail" [selectedRow]="currentSelectRow" (closeReturn)="$event ? doSearch() : closePanel()" nw-scroll-anchor-auto></app-order-detail-form>
<app-order-receipt *ngIf="showReceipt" [selectedRow]="currentSelectRow" (closeReturn)="$event ? doSearch() : closePanel()" nw-scroll-anchor-auto></app-order-receipt>
<app-order-delivery *ngIf="showDelivery" [selectedRow]="currentSelectRow" (closeReturn)="$event ? doSearch() : closePanel()" nw-scroll-anchor-auto></app-order-delivery>
<app-order-edit-form *ngIf="showEdit" [selectedRow]="currentSelectRow" (closeReturn)="$event ? doSearch() : closePanel()" nw-scroll-anchor-auto></app-order-edit-form>
