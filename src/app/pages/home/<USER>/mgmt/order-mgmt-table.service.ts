import {TableSearchService} from '../../../global/table-search.service';
import {ApiConstant} from '../../../global/conts/api.constant';
import {NwI18nService} from '@ng-nwui/core';
import {NwTableConfig} from '@ng-nwui/components';
import {HttpClient} from '../../../../services/http.client';
import {DateUtils} from '../../../global/utils';
import {OrderStatusEnum} from '../../../global/enums/order-status.enum';

export class OrderMgmtTableService extends TableSearchService {

  constructor(protected nwI18nService: NwI18nService, protected httpService: HttpClient) {
    super(httpService);
  }

  tableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'contractNo',
        title: this.nwI18nService.getI18nValue('合同号'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'custName',
        title: this.nwI18nService.getI18nValue('i18n_customer.form.name'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'factoryName',
        title: this.nwI18nService.getI18nValue('i18n_factory.form.name'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'simTypeName',
        title: this.nwI18nService.getI18nValue('卡类型'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'simQuantity',
        title: this.nwI18nService.getI18nValue('制卡总数量'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'reuseModeName',
        title: this.nwI18nService.getI18nValue('复用模式'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'reuseAmount',
        title: this.nwI18nService.getI18nValue('复用次数'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'status',
        title: this.nwI18nService.getI18nValue('订单状态'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyType: 'template',
      },
      {
        id: 'factoryDeliverDate',
        title: this.nwI18nService.getI18nValue('工厂交付日期'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
      {
        id: 'custDeliverDate',
        title: this.nwI18nService.getI18nValue('客户交付日期'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
      {
        id: 'createName',
        title: this.nwI18nService.getI18nValue('i18n_production.create.name'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'createTime',
        title: this.nwI18nService.getI18nValue('i18n_production.create.time'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
    ]
  };

  getSearchUrl(): string {
    return ApiConstant.ORDER_LIST;
  }
}
