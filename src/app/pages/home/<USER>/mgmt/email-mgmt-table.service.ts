import {TableSearchService} from '../../../global/table-search.service';
import {ApiConstant} from '../../../global/conts/api.constant';
import {NwI18nService} from '@ng-nwui/core';
import {NwTableConfig} from '@ng-nwui/components';
import {HttpClient} from '../../../../services/http.client';
import {DateUtils} from '../../../global/utils';

export class EmailMgmtTableService extends TableSearchService {

  constructor(protected nwI18nService: NwI18nService, protected httpService: HttpClient) {
    super(httpService);
  }

  tableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'factoryName',
        title: this.nwI18nService.getI18nValue('i18n_factory.form.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'operation',
        title: this.nwI18nService.getI18nValue('i18n_email.form.operation'),
        bodyType: 'template',
      },
      {
        id: 'subject',
        title: this.nwI18nService.getI18nValue('i18n_email.form.subject'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'sendTo',
        title: this.nwI18nService.getI18nValue('i18n_email.form.send.to'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'sendBy',
        title: this.nwI18nService.getI18nValue('i18n_email.form.send.by'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'createName',
        title: this.nwI18nService.getI18nValue('i18n_production.create.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'createTime',
        title: this.nwI18nService.getI18nValue('i18n_production.create.time'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
      {
        id: 'updateName',
        title: this.nwI18nService.getI18nValue('i18n_production.update.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'updateTime',
        title: this.nwI18nService.getI18nValue('i18n_production.update.time'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        bodyRender: (value: any) => value ? DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string) : '-'
      },
    ]
  };

  getSearchUrl(): string {
    return ApiConstant.EMAIL_LIST;
  }
}
