import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild} from '@angular/core';
import {NwDialogService, NwModalService, NwScrollAnchorAuto, NwSelectConfig, NwTable,} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {ApiConstant} from '../../../global/conts/api.constant';
import {SystemConstant} from '../../../global/conts/system.constant';
import {OrderMgmtTableService} from './order-mgmt-table.service';
import {HttpClient} from '../../../../services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {FunCodeGlobalConstant} from 'src/app/pages/global/conts/fun-code-global.constant';
import {HomeService} from '../../home.service';
import {ColorConstant} from '../../../global/conts/color.constant';
import {DictTools} from "../../../global/dict.tools";
import {DictCode} from "../../../global/enums/dict-code.enum";
import {BusinessConstant} from "../../../global/conts/business.constant";
import {KeyTypeEnum} from "../../../global/enums/key.type.enum";
import {OrderStatusEnum} from "../../../global/enums/order-status.enum";
import {
  OrderAuditFirstCardRequestModalComponent
} from "../audit-first-card-request/order-audit-first-card-request-modal.component";
import { OrderAuditFirstCardModalComponent } from '../audit-first-card/order-audit-first-card-modal.component';

@Component({
  selector: 'app-order-audit-internal',
  templateUrl: './order-mgmt.component.html',
  styleUrls: ['./order-mgmt.component.scss']
})
export class OrderMgmtComponent implements OnInit, OnDestroy {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder, public dictTools: DictTools,
              private nwDialogService: NwDialogService, protected nwModalService: NwModalService,
  ) {
    this.tableService = new OrderMgmtTableService(nwI18nService, apiService);
    this.homeService = new HomeService(apiService, nwI18nService);
  }


  SystemConstant = SystemConstant;
  ColorConstant = ColorConstant;
  KeyType = KeyTypeEnum;

  @ViewChild('nwTable')
  nwTable?: NwTable;

  @ViewChild('detailScrollAnchor', {read: NwScrollAnchorAuto})
  detailScrollAnchor?: NwScrollAnchorAuto;
  tableService!: OrderMgmtTableService;

  formBuilder = this.fb.group({
    contractNo: [''],
  });

  showDetail = false;
  showCreate = false;
  showAuditInternal = false;
  showAuditExternal = false;
  isOperator = false;
  showEdit = false;
  showFirstCardConfirm = false;
  showDelivery = false;
  showReceipt = false;

  currentSelectRow: any;

  auditFirstCardRequestNwModalRef!: any;
  auditFirstCardNwModalRef!: any;

  selectConfig: NwSelectConfig = {clearable: true};

  FunCodeGlobalConstant = FunCodeGlobalConstant;
  homeService!: HomeService;

  operationList!: any;
  imsiOperationList!: any;
  factoryOperationList!: any;

  ngOnInit(): void {
    this.getDictList();
    this.doSearch();
  }

  ngOnDestroy(): void {
    if (!!this.auditFirstCardRequestNwModalRef) {
      this.auditFirstCardRequestNwModalRef?.close();
    }
    if (!!this.auditFirstCardNwModalRef) {
      this.auditFirstCardNwModalRef?.close();
    }
  }



  getDictList(): void {
    this.dictTools.getDictList(DictCode.BUSI_OPERATION).subscribe(resp => {
      this.imsiOperationList = resp.filter((v: any) => BusinessConstant.BUSI_OPERATION_IMSI.some(k => k === v.id));
      this.factoryOperationList = resp.filter((v: any) => BusinessConstant.BUSI_OPERATION_FACTORY.some(k => k === v.id));
    });
  }

  selectRow(row: any): void {
    this.tableService.onSelectRow(row);
    this.currentSelectRow = row.currentSelected;
    this.closePanel();
  }

  onChangePage(pageNum: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePage(pageNum);
  }

  onChangePageSize(pageSize: any): void {
    this.closePanel();
    this.currentSelectRow = null;
    this.tableService.onChangePageSize(pageSize);
  }

  factoryChange(value: any): void {
    if (!value) {
      return;
    }
    this.formBuilder.get('operation')?.reset();
    this.operationList = value === BusinessConstant.FACTORY_ID_IMSI ? this.imsiOperationList : this.factoryOperationList;
  }

  doSearch(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      this.currentSelectRow = null;
      this.tableService.onSearch(this.buildParams());
      this.closePanel();
    }
  }

  buildParams(): any {
    const result = Object.assign({}, this.formBuilder.value);
    return result;
  }

  reset(): void {
    NwFormUtils.reset(this.formBuilder);
    this.doSearch();
  }

  closePanel(): void {
    this.showDetail = false;
    this.showAuditInternal = false;
    this.showAuditExternal = false;
    this.showEdit = false;
    this.showDelivery = false;
    this.showReceipt = false;
  }

  detail(): void {
    this.closePanel();
    this.showDetail = true;
  }

  edit(): void {
    this.closePanel();
    this.showEdit = true;
  }

  disabledEdit(): boolean {
    return !this.currentSelectRow || this.currentSelectRow.status !== OrderStatusEnum.DRAFT;
  }

  auditInternal(isOperator: boolean): void {
    this.closePanel();
    setTimeout(() => {
      this.showAuditInternal = true;
      this.isOperator = isOperator;
    });
  }

  disabledAuditInternal(): boolean {
    return !this.currentSelectRow ||
      (this.currentSelectRow.status !== OrderStatusEnum.DRAFT && this.currentSelectRow.status !== OrderStatusEnum.REVIEWING);
  }

  auditExternal(isOperator: boolean): void {
    this.closePanel();
    setTimeout(() => {
      this.showAuditExternal = true;
      this.isOperator = isOperator;
    });
  }

  disabledAuditExternal(): boolean {
    return !this.currentSelectRow || (this.currentSelectRow.status !== OrderStatusEnum.DATA_AUDIT && this.currentSelectRow.status !== OrderStatusEnum.DATA_AUDIT_CONFIRM);
  }

  disabledFirstCardReviewRequest(): boolean {
    return !this.currentSelectRow || this.currentSelectRow.status !== OrderStatusEnum.WAITING_FOR_FIRST_CARD_LOG;
  }


  auditFirstCardRequest(): void {
    this.auditFirstCardRequestNwModalRef = this.nwModalService.open(OrderAuditFirstCardRequestModalComponent, ConfigConstant.mdSizeModal({orderId: this.currentSelectRow?.orderId}));
    this.auditFirstCardRequestNwModalRef.onClose().subscribe((resp: any) => {
      if (!!resp) {
        this.doSearch();
      }
    });
  }

  disabledFirstCardReview(): boolean {
    return !this.currentSelectRow || this.currentSelectRow.status !== OrderStatusEnum.FIRST_CARD_LOG_REVIEW;
  }

  auditFirstCardReview(): void{
    this.auditFirstCardNwModalRef = this.nwModalService.open(OrderAuditFirstCardModalComponent, ConfigConstant.mdSizeModal({orderId: this.currentSelectRow?.orderId}));
    this.auditFirstCardNwModalRef.onClose().subscribe((resp: any) => {
      if (!!resp) {
        this.doSearch();
      }
    });
  }


  disabledDelivery(): boolean {
    return !this.currentSelectRow || this.currentSelectRow.status !== OrderStatusEnum.ORDER_TRACKING;
  }

  delivery(): void {
    this.closePanel();
    this.showDelivery = true;
  }

  disabledReceipt(): boolean {
    return !this.currentSelectRow || this.currentSelectRow.status !== OrderStatusEnum.ORDER_TRACKING;
  }

  receipt(): void {
    this.closePanel();
    this.showReceipt = true;
  }
}
