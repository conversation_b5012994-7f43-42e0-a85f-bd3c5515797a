<nw-panel>
  <nw-panel-header label="{{'i18n_public.query.result.condition'| nwi18n}}" [icons]="['collapse']">
  </nw-panel-header>
  <nw-panel-content>
    <div class="row" [formGroup]="formBuilder">
      <nw-form-field label="{{'i18n_production.key.word'| nwi18n}}" class="col-lg-3">
        <nw-input formControlName="keyword" [placeholder]="nwI18nService.getI18nValue('i18n_production.key.word')"></nw-input>
      </nw-form-field>

      <nw-form-field label="{{'i18n_factory.form.name'| nwi18n}}" class="col-lg-3">
        <nw-select formControlName="factoryId" [selectConfig]="homeService.buildFactorySelectConfig()"></nw-select>
      </nw-form-field>

      <nw-form-field label="{{'i18n_email.form.operation'| nwi18n}}" class="col-lg-3">
        <nw-select formControlName="operation" [selectConfig]="dictTools.defaultSelectConfig" [selectData]="operationList"></nw-select>
      </nw-form-field>
    </div>
  </nw-panel-content>
  <nw-panel-footer>
    <button icon="fas fa-search" nw-button [color]="'primary'" [disabled]="!formBuilder.valid"
            (click)="doSearch()">{{'i18n_public.search.button'|nwi18n}}</button>&nbsp;
    <button nw-button color="warning" icon="fa fa-undo" (click)="reset()">{{'i18n_public.reset.button'|nwi18n}}</button>
  </nw-panel-footer>
</nw-panel>

<nw-panel>

  <nw-panel-header
    label="{{'i18n_public.query.result.title'| nwi18n}}">
  </nw-panel-header>
  <nw-panel-content>
    <nw-button-group>
      <button nw-button color="white" icon="fa-info-circle" [disabled]="!currentSelectRow"
              (click)="detail()">
        {{'i18n_privilege.detail'| nwi18n}}
      </button>

      <button nw-button
              color="white"
              icon="fa-plus"
              (click)="create()" *nwAuth="FunCodeGlobalConstant.EMAIL_CREATE">{{'i18n_public.create.button'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-edit"
              (click)="edit()" *nwAuth="FunCodeGlobalConstant.EMAIL_MODIFY" [disabled]="!currentSelectRow">{{'i18n_public.edit.button'| nwi18n}}</button>
      <button nw-button
              color="white"
              icon="fa-trash" *nwAuth="FunCodeGlobalConstant.EMAIL_DELETE" [disabled]="!currentSelectRow"
              (click)="delete()">{{'i18n_public.delete.button'| nwi18n}}</button>

    </nw-button-group>

    <nw-table #nwTable
              [tableConfig]="tableService.tableConfig"
              [pageSizeOptions]="SystemConstant.DEFAULT_PAGE_SIZE_LIST"
              [tableData]="tableService.tableData"
              (changePage)="onChangePage($event)"
              (changePageSize)="onChangePageSize($event)"
              (selectRow)="selectRow($event)">
      <ng-template nw-table-column="operation" let-row="row" let-cell="cell">
        <nw-label [color]='homeService.getColor(ColorConstant.BUSI_OPERATION_COLOR, cell)'
                  *ngIf="cell">{{row.operationName}}</nw-label>
      </ng-template>
    </nw-table>
  </nw-panel-content>
</nw-panel>

<!--Detail-->
<app-email-detail *ngIf="showDetail" [selectedRow]="currentSelectRow"  (closeReturn)="closePanel()"
                        #detailScrollAnchor nw-scroll-anchor-auto></app-email-detail>
<app-email-create *ngIf="showCreate" (closeReturn)="$event ? doSearch() : closePanel()"
                        #detailScrollAnchor nw-scroll-anchor-auto></app-email-create>
<app-email-edit *ngIf="showEdit" [selectedRow]="currentSelectRow"  (closeReturn)="$event ? doSearch() : closePanel()"
                        #detailScrollAnchor nw-scroll-anchor-auto></app-email-edit>
