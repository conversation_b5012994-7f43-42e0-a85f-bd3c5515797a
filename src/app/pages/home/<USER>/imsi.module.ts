import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DictTools} from '../../global/dict.tools';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule,
  NwDatetimeModule,
  NwDatetimeRangeModule,
  NwDialogModule, NwDropdownModule,
  NwFormFieldModule,
  NwInputFileModule,
  NwInputGroupModule,
  NwInputModule, NwInputMultipleModule,
  NwLabelModule,
  NwModalService,
  NwPanelModule,
  NwRadioModule,
  NwScrollAnchorModule,
  NwSelectModule,
  NwSelectTreeModule,
  NwStepperModule,
  NwSwitchModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwTreeModule,
  NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {ImsiRoutingModule} from './imsi-routing.module';
import {ImsiMgmtComponent} from './mgmt/imsi-mgmt.component';
import {ImsiDetailComponent} from './detail/imsi-detail.component';
import {ImsiRenewComponent} from './renew/imsi-renew.component';
import {ImsiBatchImportComponent} from './batch-import/imsi-batch-import.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NwAuthModule} from "@ng-nwui/integration";
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {SharedModule} from "../../../shared/shared.module";

@NgModule({
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    ImsiRoutingModule,
    NwInputGroupModule,
    PipeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwStepperModule,
    NwDatetimeModule,
    NwInputFileModule,
    SharedModule,
    NwDropdownModule,
    NwInputMultipleModule
  ],
  declarations: [
    ImsiMgmtComponent,
    ImsiDetailComponent,
    ImsiRenewComponent,
    ImsiBatchImportComponent
  ],
  providers: [DictTools, NwModalService]
})
export class ImsiModule {
}
