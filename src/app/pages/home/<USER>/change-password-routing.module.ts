import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NwRouteSettings} from '@ng-nwui/core';
import {ChangePasswordComponent} from "./change-password.component";

const routes: Routes = [
  {
    path: 'changePassword', component: ChangePasswordComponent,
    ...NwRouteSettings.data({reuse: true, routeIcon: 'fas fa-cog', routeAlias: 'i18n_privilege.change_password'})
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ChangePasswordRoutingModule {
}
