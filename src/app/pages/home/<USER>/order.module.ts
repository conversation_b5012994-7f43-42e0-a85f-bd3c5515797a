import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DictTools} from '../../global/dict.tools';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule,
  NwDatetimeModule,
  NwDatetimeRangeModule,
  NwDialogModule,
  NwFormFieldModule,
  NwInputFileModule,
  NwInputGroupModule,
  NwInputModule,
  NwLabelModule,
  NwModalService, NwPaginationModule,
  NwPanelModule,
  NwRadioModule,
  NwScrollAnchorModule,
  NwSelectModule,
  NwSelectTreeModule,
  NwStepperModule,
  NwSwitchModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwTreeModule,
  NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {OrderRoutingModule} from './order-routing.module';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NwAuthModule} from '@ng-nwui/integration';
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {OrderCreateComponent} from "./create/order-create.component";
import {OrderPolicyModalComponent} from "./component/policy-modal/order-policy-modal.component";
import {RangePolicyComponent} from "./component/range-policy/range-policy.component";
import {OrderMgmtComponent} from "./mgmt/order-mgmt.component";
import {OrderAuditInternalComponent} from "./audit-internal/order-audit-internal.component";
import {OrderSimModalComponent} from "./component/order-sim-modal/order-sim-modal.component";
import {OrderAuditExternalComponent} from "./audit-external/order-audit-external.component";
import {OrderEditComponent} from "./edit/order-edit.component";
import {
  OrderAuditFirstCardRequestModalComponent
} from "./audit-first-card-request/order-audit-first-card-request-modal.component";
import {OrderAuditFirstCardModalComponent} from "./audit-first-card/order-audit-first-card-modal.component";
import {OrderDeliveryComponent} from "./delivery/order-delivery.component";
import {OrderReceiptComponent} from "./receipt/order-receipt.component";
import {BlockComponent} from "./component/block-policy/block.component";
import {OrderDetailComponent} from "./detail/order-detail.component";

@NgModule({
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    OrderRoutingModule,
    NwInputGroupModule,
    PipeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwStepperModule,
    NwDatetimeModule,
    NwInputFileModule,
    NwPaginationModule
  ],
  declarations: [
    OrderCreateComponent,
    OrderPolicyModalComponent,
    RangePolicyComponent,
    OrderMgmtComponent,
    OrderAuditInternalComponent,
    OrderAuditExternalComponent,
    OrderEditComponent,
    OrderSimModalComponent,
    OrderAuditFirstCardRequestModalComponent,
    OrderAuditFirstCardModalComponent,
    OrderDeliveryComponent,
    OrderReceiptComponent,
    BlockComponent,
    OrderDetailComponent
  ],
  providers: [DictTools, NwModalService]
})
export class OrderModule {
}
