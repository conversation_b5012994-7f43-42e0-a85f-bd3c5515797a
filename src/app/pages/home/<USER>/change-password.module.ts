import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule,
  NwFormFieldModule,
  NwInputModule,
  NwLabelModule,
  NwPanelModule,
  NwRadioModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwSelectTreeModule,
  NwTreeModule,
  NwSelectModule,
  NwTreeTableModule,
  NwSwitchModule,
  NwScrollAnchorModule, NwDialogModule, NwInputFileModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {NwAuthModule} from '@ng-nwui/integration';
import {ChangePasswordRoutingModule} from './change-password-routing.module';
import {ChangePasswordComponent} from "./change-password.component";

@NgModule({
  declarations: [
    ChangePasswordComponent,
  ],
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    ChangePasswordRoutingModule,
    NwDialogModule,
    NwInputFileModule,
  ],
})
export class ChangePasswordModule {
}
