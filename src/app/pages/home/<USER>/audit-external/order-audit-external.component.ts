import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService, NwModalService} from '@ng-nwui/components';
import {ApiConstant, ConfigConstant} from 'src/app/pages/global/conts';
import {PolicyEnum} from 'src/app/pages/global/enums/policy.enum';
import {DictTools} from 'src/app/pages/global/dict.tools';
import {OrderAuditFormService} from '../order-audit-form.service';
import {DateUtils} from '../../../global/utils';
import { SimPrintTypeEnum } from 'src/app/pages/global/enums/sim.print.type.enum';
import { SimCosTypeEnum } from 'src/app/pages/global/enums/sim.cos.type.enum';
import {DomSanitizer} from '@angular/platform-browser';

@Component({
  selector: 'app-order-audit-external-form',
  templateUrl: './order-audit-external.component.html',
  styleUrls: ['./order-audit-external.component.scss']
})
export class OrderAuditExternalComponent implements OnInit, OnDestroy {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder, protected sanitizer: DomSanitizer,
              private nwDialogService: NwDialogService, public dictTools: DictTools, private nwModalService: NwModalService) {
    this.formService = new OrderAuditFormService(fb, nwDialogService, apiService, nwI18nService, dictTools, nwModalService);
  }

  @Input() selectedRow: any;

  @Input() isOperator: any;

  @Output()
  closeReturn = new EventEmitter<any>();

  formService: OrderAuditFormService;

  PolicyEnum = PolicyEnum;
  SimPrintTypeEnum = SimPrintTypeEnum;
  SimCosTypeEnum =  SimCosTypeEnum;

  DateUtils = DateUtils;

  data!: any;

  emailData !: any;

  locale = this.nwI18nService.getCurrentLocale() as string;

  ngOnInit(): void {
    this.getDetail();
  }

  ngOnDestroy(): void {
    this.formService?.destroy();
  }



  getDetail(): any {
    this.apiService.post(`${ApiConstant.ORDER_DETAIL}/${this.selectedRow?.orderId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.data = resp;
    });

    this.apiService.post(`${ApiConstant.ORDER_EMAIL_DETAIL}/${this.selectedRow?.orderId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      if (!!resp) {
        resp.content = this.sanitizer.bypassSecurityTrustHtml(resp?.content);
        this.emailData = resp;
      }
    });
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  download(fileId: any): void {
    this.apiService.download(ApiConstant.FILE_DOWNLOAD_V2, {fileId}).subscribe((res) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_public.success'));
    });
  }

  submit(): any {
    const url = this.isOperator ? ApiConstant.ORDER_AUDIT_EXTERNAL_OPERATOR : ApiConstant.ORDER_AUDIT_EXTERNAL_TECHNOLOGY;
    this.formService.submit(url, this.closeReturn, {orderId: this.selectedRow?.orderId});  }
}
