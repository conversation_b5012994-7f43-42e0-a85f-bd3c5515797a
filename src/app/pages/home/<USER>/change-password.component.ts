import {Component, OnInit} from '@angular/core';
import {
  NwFormUtils,
  NwI18nService,
  NwPermissionService,
  NwValidatorCrossExecutor,
  NwValidatorResult,
  NwValidators
} from '@ng-nwui/core';
import {AbstractControl, FormBuilder, FormGroup} from '@angular/forms';
import {NwDialogService} from '@ng-nwui/components';
import {HttpClient} from "../../../services/http.client";
import {RegularConstant} from "../../global/conts/regular.constant";
import {CryptoUtils} from "../../global/utils/CryptoUtils";
import {CacheManagerTools} from "../../global/cache-manager.tools";
import {ApiConstant} from "../../global/conts/api.constant";
import {NwFrameCoreService} from "@ng-nwui-frame/core";

@Component({
  selector: 'personal-setting-change-password',
  templateUrl: './change-password.component.html'
})
export class ChangePasswordComponent implements OnInit {
  constructor(
    protected formBuilder: FormBuilder,
    private permission: NwPermissionService,
    protected httpService: HttpClient,
    protected i18nService: NwI18nService,
    protected nwDialogService: NwDialogService,
    private frameCoreService: NwFrameCoreService,
  ) {
  }

  passwordPattern = NwValidators.pattern(RegularConstant.STRONG_PASSWORD, 'i18n_privilege.password.format');

  changePasswordForm!: FormGroup;

  ngOnInit(): void {
    this.changePasswordForm = this.formBuilder.group({
      email: [this.permission.username],
      oldPassword: ['', [this.inputRequiredVerify, this.passwordPattern]],
      newPassword: ['', [this.inputRequiredVerify, this.passwordPattern]],
      confirmPassword: ['', [this.inputRequiredVerify, this.passwordPattern]]
    }, {
      validators: [NwValidators.crossValidator(
        new PasswordValidatorCrossExecutor('newPassword', 'confirmPassword')
      )]
    });
  }
  inputRequiredVerify = NwValidators.syncValidator((control) => {
    const nwValidatorResult = new NwValidatorResult();
    nwValidatorResult.result = (control.value && control.value.trim().length > 0);
    return nwValidatorResult;
  }, 'i18n_public.field.input.required');
  changePasswordFormSubmit(): void {
    if (NwFormUtils.validate(this.changePasswordForm)) {
      this.nwDialogService.confirm(this.i18nService.getI18nValue('i18n_public.submit.tips'), () => {
        this.changePasswordForm.value.oldPassword = CryptoUtils.encryptRSA(
          this.changePasswordForm.value.oldPassword,
          CacheManagerTools.getRsaPublicKey()
        ).data;
        this.changePasswordForm.value.newPassword = CryptoUtils.encryptRSA(
          this.changePasswordForm.value.newPassword,
          CacheManagerTools.getRsaPublicKey()
        ).data;
        this.changePasswordForm.value.confirmPassword = '';
        this.httpService.post(ApiConstant.STAFF_PASSWORD_CHANGE, this.changePasswordForm.value).subscribe(() => {
          this.frameCoreService.logout().subscribe(() => {
          });
        }, error => {
          this.nwDialogService.error(error.apiResponse.message);
        });
      });
    }
  }

  changePasswordFormReset(): void {
    NwFormUtils.reset(this.changePasswordForm);
    this.changePasswordForm.patchValue({email: this.permission.username});
  }

}

export class PasswordValidatorCrossExecutor implements NwValidatorCrossExecutor {
  constructor(
    protected newPassword: string,
    protected confirmPassword: string
  ) {
  }

  crossControls(control: AbstractControl): AbstractControl[] {
    // @ts-ignore
    return [control.get(this.newPassword), control.get(this.confirmPassword)];
  }

  messages(crossControls: AbstractControl[]): Map<AbstractControl, string> | null | undefined {
    const message = new Map<AbstractControl, string>();
    message.set(crossControls[0], 'i18n_privilege.password.unequal');
    message.set(crossControls[1], 'i18n_privilege.password.unequal');
    return message;
  }

  validate(control: AbstractControl, crossControls: AbstractControl[]): boolean {
    const newPassword = crossControls[0];
    const confirmPassword = crossControls[1];
    if (newPassword && confirmPassword && newPassword.value && confirmPassword.value) {
      return newPassword.value === confirmPassword.value;
    }
    return true;
  }

  validatorId(): string {
    return 'PasswordValidatorCrossExecutor';
  }
}
