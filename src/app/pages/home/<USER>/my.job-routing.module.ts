import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {MyJobComponent} from "./my.job.component";
import {NwI18nService, NwRouteSettings} from "@ng-nwui/core";




const routes: Routes = [
  {path: 'jobList', component: MyJobComponent,
    ...NwRouteSettings.data({reuse: true, routeIcon: 'fas fa-thumbtack', routeAlias:  'i18n_privilege.job'})
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MyJobRoutingModule {

}
