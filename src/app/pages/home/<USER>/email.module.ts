import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DictTools} from '../../global/dict.tools';
import {
    NwBadgeModule,
    NwButtonGroupModule,
    NwButtonModule,
    NwCheckboxModule,
    NwDatetimeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwFormFieldModule,
    NwInputGroupModule,
    NwInputModule, NwInputMultipleModule,
    NwLabelModule,
    NwModalService,
    NwPanelModule,
    NwRadioModule,
    NwScrollAnchorModule,
    NwSelectModule,
    NwSelectTreeModule,
    NwStepperModule,
    NwSwitchModule,
    NwTableModule,
    NwTabModule,
    NwTextareaModule,
    NwTreeModule,
    NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {EmailRoutingModule} from './email-routing.module';
import {EmailMgmtComponent} from './mgmt/email-mgmt.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NwAuthModule} from "@ng-nwui/integration";
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {EmailDetailComponent} from "./detail/email-detail.component";
import {EmailCreateComponent} from "./create/email-create.component";
import {EmailEditComponent} from "./edit/email-edit.component";

@NgModule({
    imports: [
        NwPanelModule,
        NwCoreModule,
        NwButtonModule,
        NwButtonGroupModule,
        NwInputModule,
        NwRadioModule,
        NwCheckboxModule,
        NwLabelModule,
        ReactiveFormsModule,
        NwSelectModule,
        NwTableModule,
        NwSelectTreeModule,
        NwTabModule,
        NwBadgeModule,
        NwFormFieldModule,
        NwTextareaModule,
        FormsModule,
        NwTreeModule,
        CommonModule,
        NwTreeTableModule,
        NwSwitchModule,
        NwScrollAnchorModule,
        NwAuthModule,
        EmailRoutingModule,
        NwInputGroupModule,
        PipeModule,
        NwDatetimeRangeModule,
        NwDialogModule,
        NwStepperModule,
        NwDatetimeModule,
        NwInputMultipleModule
    ],
  declarations: [
    EmailMgmtComponent,
    EmailDetailComponent,
    EmailCreateComponent,
    EmailEditComponent
  ],
  providers: [DictTools, NwModalService]
})
export class EmailModule {
}
