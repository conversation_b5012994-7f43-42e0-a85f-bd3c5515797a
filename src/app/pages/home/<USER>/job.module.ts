import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule, NwDatetimeModule,
  NwDatetimeRangeModule,
  NwDialogModule,
  NwFormFieldModule,
  NwInputGroupModule,
  NwInputModule,
  NwLabelModule,
  NwPanelModule,
  NwRadioModule,
  NwScrollAnchorModule,
  NwSelectModule,
  NwSelectTreeModule,
  NwStepperModule,
  NwSwitchModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwTreeModule,
  NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {NwAuthModule} from '@ng-nwui/integration';
import {JobRoutingModule} from "./job-routing.module";
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {JobComponent} from "./job.component";
import {JobEditTaskTimeComponent} from "./edit/job.editTaskTime.component";


@NgModule({
  declarations: [
    JobComponent,
    JobEditTaskTimeComponent,
  ],
  exports: [
    JobComponent
  ],
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    JobRoutingModule,
    NwInputGroupModule,
    PipeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwStepperModule,
    NwDatetimeModule
  ]
})
export class JobModule {
}
