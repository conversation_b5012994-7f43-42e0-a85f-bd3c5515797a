import {FormBuilder} from '@angular/forms';
import {NwDialogService, NwInputFileConfig} from '@ng-nwui/components';
import {NwI18nService} from '@ng-nwui/core';
import {HttpClient} from 'src/app/services/http.client';
import {DictTools} from '../../global/dict.tools';
import {DictCode} from '../../global/enums/dict-code.enum';
import {BusinessConstant} from '../../global/conts/business.constant';
import {FormService} from '../form.service';
import {FileSuffixConstant} from "../../global/conts/file-suffix.constant";

export class KeyFormService extends FormService {

  fileData: any;

  operationList!: any;
  imsiOperationList!: any;
  factoryOperationList!: any;

  keyTypeList!: any;

  keyAlgList!: any;
  symmetricKeyAlgList = [{id: 'AES_256', text: 'AES(256)'}];
  asymmetricKeyAlgList = [{id: 'RSA', text: 'RSA'}, {id: 'PGP', text: 'PGP'}];

  fileConfig = new NwInputFileConfig({
    fileType: ['*'],
    chunk: false, chunkSize: 0, dataAdapter: undefined, maxSize: 10 * 1024, url: ''
  });

  txtFileConfig = new NwInputFileConfig({
    fileType: [FileSuffixConstant.TXT],
    chunk: false, chunkSize: 0, dataAdapter: undefined, maxSize: 10 * 1024, url: ''
  });

  pgpFileConfig = new NwInputFileConfig({
    fileType: [FileSuffixConstant.asc],
    chunk: false, chunkSize: 0, dataAdapter: undefined, maxSize: 10 * 1024, url: ''
  });

  constructor(
    protected fb: FormBuilder,
    protected nwDialogService: NwDialogService,
    protected apiService: HttpClient,
    protected nwI18nService: NwI18nService,
    protected dictTools: DictTools) {
    super(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  override initForm(): void {
    this.form = this.fb.group({
      factoryId: [null, [this.homeService.requiredValidator]],
      operation: [null, [this.homeService.requiredValidator]],
      keyType: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(100)]],
      keyAlg: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(512)]],
    });
  }

  override getDictList(): void {
    this.dictTools.getMulitDictList([DictCode.BUSI_OPERATION, DictCode.KEY_TYPE]).subscribe(resp => {
      const operationList = resp[DictCode.BUSI_OPERATION];
      this.imsiOperationList = operationList.filter((v: any) => BusinessConstant.BUSI_OPERATION_IMSI.some(k => k === v.id));
      this.factoryOperationList = operationList.filter((v: any) => BusinessConstant.BUSI_OPERATION_FACTORY.some(k => k === v.id));

      this.keyTypeList = resp[DictCode.KEY_TYPE];
    });
  }

  override setForm(formValue: any): void {
    super.setForm(formValue);

    // 手动刷新字典
    this.factoryChange(formValue?.factoryId, false);
    this.keyTypeChange(formValue?.keyType, false);
  }

  factoryChange(value: any, resetValue: boolean = true): void {
    if (resetValue) {
      this.form.get('operation')?.reset();
    }
    if (!value) {
      this.operationList = [];
    } else {
      this.operationList = value === BusinessConstant.FACTORY_ID_IMSI ? this.imsiOperationList :
        this.factoryOperationList;
    }
  }

  keyTypeChange(value: any, resetValue: boolean = true): void {
    if (resetValue) {
      this.form.get('keyAlg')?.reset();
    }
    if (!value) {
      this.keyAlgList = [];
    } else {
      this.keyAlgList = value === BusinessConstant.KEY_TYPE_SYMMETRIC_KEY ? this.symmetricKeyAlgList : this.asymmetricKeyAlgList;
    }
  }

  keyAlgChange(value: any, resetValue: boolean = true): void {
    if (resetValue) {
      this.fileData = null;
    }
    this.fileConfig = value === 'PGP' ? this.pgpFileConfig : this.txtFileConfig;
  }

  disableOperation(): boolean {
    return !this.form.value?.factoryId;
  }

  disableKeyAlg(): boolean {
    return !this.form.value?.keyType;
  }

  onSelectFile($event: any): void {
    this.fileData = $event?.file;
  }

  deleteFile(): void {
    this.fileData = null;
  }

  override checkParams(): boolean {
    if (!this.form.valid) {
      return false;
    }
    return !!this.fileData;
  }

  override buildParams(): any {
    const result = new FormData();
    result.append('file', this.fileData);

    const formValue = this.form.value;
    Object.keys(formValue).filter(key => formValue[key]).forEach(key => result.append(key, formValue[key]));
    return result;
  }

  override addExtendParam(params: any, extendParams: any): void {
    if (!!extendParams) {
      Object.keys(extendParams).filter(key => extendParams[key]).forEach(key => params.append(key, extendParams[key]));
    }
  }
}
