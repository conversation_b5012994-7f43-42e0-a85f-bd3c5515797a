import {Component, Inject, OnInit, Optional} from '@angular/core';
import {NW_MODAL_DATA, NwDialogService, NwModalRef} from '@ng-nwui/components';
import {FormBuilder} from '@angular/forms';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {DictTools} from '../../../../global/dict.tools';
import {HttpClient} from '../../../../../services/http.client';
import {OrderMgmtTableService} from "../../mgmt/order-mgmt-table.service";
import {OrderSimTableService} from "./order-sim-table.service";
import { SystemConstant } from 'src/app/pages/global/conts';

@Component({
  selector: 'app-order-policy-modal',
  templateUrl: 'order-sim-modal.component.html',
  styleUrls: ['./order-sim-modal.component.scss']
})
export class OrderSimModalComponent implements OnInit {

  constructor(@Optional() @Inject(NW_MODAL_DATA) private _data: any,
              protected fb: FormBuilder,
              protected nwDialogService: NwDialogService,
              public i18nService: NwI18nService,
              protected apiService: HttpClient,
              private modalRef: NwModalRef,
              public dictTools: DictTools,
  ) {
    this.tableService = new OrderSimTableService(i18nService, apiService, this._data?.showCid);
    console.log(this._data);
  }

  tableService!: OrderSimTableService;

  SystemConstant = SystemConstant;

  formBuilder = this.fb.group({
    iccid: [''],
    orderId: [''],
  });

  ngOnInit(): void {
    this.reset();
  }


  doSearch(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      this.tableService.onSearch(this.buildParams());
    }
  }

  buildParams(): any {
    const result = Object.assign({}, this.formBuilder.value);
    return result;
  }

  reset(): void {
    NwFormUtils.reset(this.formBuilder);
    this.formBuilder.patchValue({orderId: this._data?.orderId});
    this.doSearch();
  }


}
