import {TableSearchService} from '../../../../global/table-search.service';
import {ApiConstant} from '../../../../global/conts/api.constant';
import {NwI18nService} from '@ng-nwui/core';
import {NwTableConfig} from '@ng-nwui/components';
import {HttpClient} from '../../../../../services/http.client';
import {DateUtils} from '../../../../global/utils';
import {OrderStatusEnum} from '../../../../global/enums/order-status.enum';

export class OrderSimTableService extends TableSearchService {

  tableConfig: NwTableConfig;

  constructor(protected nwI18nService: NwI18nService, protected httpService: HttpClient, showCid: boolean) {
    super(httpService);
    this.tableConfig = !!showCid ? this.productionTableConfig : this.previewTableConfig;
  }

  previewTableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'iccid',
        title: this.nwI18nService.getI18nValue('ICCID'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'imsi',
        title: this.nwI18nService.getI18nValue('IMSI'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'msisdn',
        title: this.nwI18nService.getI18nValue('MSISDN'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
    ]
  };


  productionTableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'cid',
        title: this.nwI18nService.getI18nValue('CID'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'iccid',
        title: this.nwI18nService.getI18nValue('ICCID'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'imsi',
        title: this.nwI18nService.getI18nValue('IMSI'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'msisdn',
        title: this.nwI18nService.getI18nValue('MSISDN'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'pin1',
        title: this.nwI18nService.getI18nValue('PIN1'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'puk1',
        title: this.nwI18nService.getI18nValue('PUK1'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'pin2',
        title: this.nwI18nService.getI18nValue('PIN2'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
      {
        id: 'puk2',
        title: this.nwI18nService.getI18nValue('PUK2'),
        style: {headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
      },
    ]
  };

  getSearchUrl(): string {
    return ApiConstant.ORDER_LIST_SIM;
  }

}
