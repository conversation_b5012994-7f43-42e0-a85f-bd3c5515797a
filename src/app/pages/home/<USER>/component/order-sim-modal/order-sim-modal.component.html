<div class="modal-change">
  <nw-modal>
    <nw-modal-header label="{{'预览生产数据'| nwi18n}}"></nw-modal-header>
    <nw-modal-content>
<!--      <div class="row" [formGroup]="formBuilder">-->
<!--        <nw-form-field label="{{'ICCID'| nwi18n}}"  layout="horizontal" labelAlign="right" labelClass="col-lg-3" fieldClass="col-lg-6" >-->
<!--          <nw-input formControlName="iccid" [placeholder]="i18nService.getI18nValue('ICCID')"></nw-input>-->
<!--        </nw-form-field>-->
<!--      </div>-->
<!--      <div class="row">-->
<!--        <button icon="fas fa-search" nw-button outline [color]="'primary'" [disabled]="!formBuilder.valid"-->
<!--                (click)="doSearch()">{{'i18n_public.search.button'|nwi18n}}</button>&nbsp;-->
<!--        <button nw-button outline color="warning" icon="fa fa-undo"-->
<!--                (click)="reset()">{{'i18n_public.reset.button'|nwi18n}}</button>-->
<!--      </div>-->
      <div class="row">
        <div class="col-lg-12">
          <nw-table #nwTable
                    [tableConfig]="tableService.tableConfig"
                    [pageSizeOptions]="SystemConstant.DEFAULT_PAGE_SIZE_LIST"
                    [tableData]="tableService.tableData"
                    (changePage)="tableService.onChangePage($event)"
                    (changePageSize)="tableService.onChangePageSize($event)">
          </nw-table>
        </div>
      </div>
    </nw-modal-content>
    <nw-modal-footer>
    </nw-modal-footer>
  </nw-modal>
</div>
