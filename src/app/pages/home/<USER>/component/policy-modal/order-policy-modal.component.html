<div class="modal-change">
  <nw-modal>
    <nw-modal-header label="{{'智能选号策略'| nwi18n}}"></nw-modal-header>
    <nw-modal-content>
      <div class="row" [formGroup]="form">
        <nw-form-field label="{{'制卡数量'| nwi18n}}" class="col-lg-3" required>
          <nw-input formControlName="simQuantity" [disabled]="true"></nw-input>
        </nw-form-field>
        <nw-form-field label="{{'复用模式'| nwi18n}}" class="col-lg-3" required>
          <nw-select [selectData]="reuseModeList" [selectConfig]="dictTools.simpleSelectConfig"
                     formControlName="reuseMode" [disabled]="true"></nw-select>
        </nw-form-field>
        <nw-form-field label="{{'复用次数'| nwi18n}}" class="col-lg-3" required>
          <nw-input formControlName="reuseAmount" [disabled]="true"></nw-input>
        </nw-form-field>
        <nw-form-field label="{{'供应商'| nwi18n}}" class="col-lg-3" required>
          <nw-select [selectData]="vendorList" [selectConfig]="dictTools.simpleSelectConfig"
                     formControlName="vendorId" (change)="vendorIdChange($event)"></nw-select>
        </nw-form-field>
        <nw-form-field label="{{'智能选号策略'| nwi18n}}" class="col-lg-3" required>
          <nw-select [selectData]="policyIdList" [selectConfig]="dictTools.simpleSelectConfig"
                     formControlName="policyId" (change)="policyIdChange($event)"></nw-select>
        </nw-form-field>

        <ng-container *ngIf="policyId === PolicyEnum.BLOCK" [formGroup]="policyForm">
          <nw-form-field label="{{'块大小' | nwi18n}}"
                         class="col-lg-3">
            <nw-select [selectData]="cardBlockSizeList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="blockSize" (change)="blockSizeChange($event)"></nw-select>
          </nw-form-field>
          <div class="col-lg-6"></div>

          <app-block #blockComponent class="col-lg-12" [searchParam]="blockSearchParam" [searchUrl]="blockSearchUrl" [selectedBlockDataList]="selectedBlockDataList"
                     (changeSelectedBlockDataList)="changeSelectedBlockDataList($event)"></app-block>

<!--          <ng-container *ngIf="!!cardBlockList?.length">-->
<!--            <div class="card-block-container">-->
<!--              <div *ngFor="let block of cardBlockList" class="card-block" [style.background-color]="block.color"-->
<!--                   (click)="blockClick(block)">-->
<!--                <div *ngIf="showCheckMark(block)" class="check-mark"><i class="fas fa-check"></i></div>-->
<!--                <div class="block-info">-->
<!--                  <p>块号：{{ block.blockNo }}</p>-->
<!--                  <p>块大小：{{ block.blockSize }}</p>-->
<!--                  <p>复用率：{{ block.reuseCount }} 次 - {{ block.reusePercent}}%</p>-->
<!--                  <span class="block-tooltip-text">{{ block.beginIccid }} <br/>-<br/> {{ block.endIccid }}</span>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
        </ng-container>
      </div>

      <ng-container *ngIf="policyId === PolicyEnum.SPECIFIED_RANGE">
        <div class="row" [formGroup]="policyForm">
          <nw-form-field label="{{'首ICCID' | nwi18n}}"
                         class="col-lg-6">
            <nw-input-group>
              <nw-select nw-input-group-prepend formControlName="cardType"
                         [selectConfig]="dictTools.simpleSelectConfig"
                         [selectData]="cardTypeList" [disabled]="true"
                         [style]="cardTypeStyle">
              </nw-select>
              <nw-input formControlName="rangeBeginIccid">
              </nw-input>
            </nw-input-group>
          </nw-form-field>
          <nw-form-field label="{{'尾ICCID' | nwi18n}}"
                         class="col-lg-6">
            <nw-input-group>
            <nw-select nw-input-group-prepend formControlName="cardType"
                       [selectConfig]="dictTools.simpleSelectConfig"
                       [selectData]="cardTypeList" [disabled]="true"
                       [style]="cardTypeStyle">
            </nw-select>
            <nw-input formControlName="rangeEndIccid">
            </nw-input>
          </nw-input-group>
          </nw-form-field>
        </div>
<!--        <app-policy-range [form]="policyForm"></app-policy-range>-->
      </ng-container>


<!--      <div class="row" [formGroup]="policyForm" *ngIf="!!policyForm && policyId === PolicyEnum.BLOCK">-->
<!--        <ng-container *ngIf="policyId === PolicyEnum.BLOCK">-->
<!--          <nw-form-field label="{{'块大小' | nwi18n}}"-->
<!--                         class="col-lg-3">-->
<!--            <nw-select [selectData]="cardBlockSizeList" [selectConfig]="dictTools.simpleSelectConfig"-->
<!--                       formControlName="blockSize" (change)="blockSizeChange($event)"></nw-select>-->
<!--          </nw-form-field>-->
<!--          <div class="col-lg-9"></div>-->
<!--          <ng-container *ngIf="!!cardBlockList?.length">-->
<!--            <div class="card-block-container">-->
<!--              <div *ngFor="let block of cardBlockList" class="card-block" [style.background-color]="block.color"-->
<!--                   (click)="blockClick(block)">-->
<!--                <div *ngIf="showCheckMark(block)" class="check-mark"><i class="fas fa-check"></i></div>-->
<!--                <div class="block-info">-->
<!--                  <p>块号：{{ block.blockNo }}</p>,-->
<!--                  <p>块大小：{{ block.blockSize }}</p>-->
<!--                  <p>复用率：{{ block.reuseCount }} 次 - {{ block.reusePercent}}%</p>-->
<!--                  <span class="block-tooltip-text">{{ block.beginIccid }} <br/>-<br/> {{ block.endIccid }}</span>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </ng-container>-->
<!--        </ng-container>-->
<!--      </div>-->
    </nw-modal-content>
    <nw-modal-footer>
      <div class="col-lg-9" style="text-align: left">
        <button nw-button color="primary" icon="fa-check" [disabled]="!checkParams()"
                (click)="doSubmit()">{{'i18n_public.submit.button' | nwi18n}}</button>&nbsp;
        <button nw-button color="warning" icon="fa-times"
                (click)="closeModal()">{{'i18n_public.cancel.button' | nwi18n}}</button>
      </div>
    </nw-modal-footer>
  </nw-modal>
</div>
