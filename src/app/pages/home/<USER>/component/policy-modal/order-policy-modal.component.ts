import {Component, Inject, OnInit, Optional, ViewChild} from '@angular/core';
import {NW_MODAL_DATA, NwDialogService, NwModalRef} from '@ng-nwui/components';
import {FormBuilder} from '@angular/forms';
import {NwI18nService} from '@ng-nwui/core';
import {DictTools} from '../../../../global/dict.tools';
import {PolicyEnum} from '../../../../global/enums/policy.enum';
import {ApiConstant, ConfigConstant} from '../../../../global/conts';
import {FormService} from '../../../form.service';
import {HttpClient} from '../../../../../services/http.client';
import {DictCode} from '../../../../global/enums/dict-code.enum';
import {BlockComponent} from "../block-policy/block.component";

@Component({
  selector: 'app-order-policy-modal',
  templateUrl: 'order-policy-modal.component.html',
  styleUrls: ['./order-policy-modal.component.scss']
})
export class OrderPolicyModalComponent extends FormService implements OnInit {

  policyForm!: any;
  policyId!: any;

  rangePolicyForm!: any;
  blockPolicyForm!: any;

  PolicyEnum = PolicyEnum;

  cardTypeList!: any;
  cardTypeStyle = 'min-width: 75px';

  cardBlockSizeList!: any;

  policyIdList!: any;
  reuseModeList!: any;
  vendorList!: any;

  blockSearchParam!: any;
  blockSearchUrl = ApiConstant.ORDER_CREAT_LIST_BLOCK;
  @ViewChild('blockComponent')
  blockComponent!: BlockComponent;
  selectedBlockDataList = [];

  constructor(@Optional() @Inject(NW_MODAL_DATA) private _data: any,
              protected fb: FormBuilder,
              protected nwDialogService: NwDialogService,
              protected i18nService: NwI18nService,
              protected apiService: HttpClient,
              private modalRef: NwModalRef,
              public dictTools: DictTools,
  ) {
    super(fb, nwDialogService, apiService, i18nService, dictTools);
    this.setForm(_data);
  }

  ngOnInit(): void {

  }

  override initForm(): void {
    this.form = this.fb.group({
      simQuantity: [null, [this.homeService.requiredValidator]],
      reuseMode: [null, [this.homeService.requiredValidator]],
      reuseAmount: [null, [this.homeService.requiredValidator]],
      vendorId: [null, [this.homeService.requiredValidator]],
      policyId: [null, [this.homeService.requiredValidator]],
    });

    this.rangePolicyForm = this.fb.group({
      cardType: ['ICCID', [this.homeService.requiredValidator]],
      rangeBeginIccid: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(20)]],
      rangeEndIccid: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(20)]],
    });

    this.blockPolicyForm = this.fb.group({
      blockSize: [null, [this.homeService.requiredValidator]],
      blockDataList: [[], [this.homeService.requiredValidator]],
    });
  }


  setForm(formValue: any): void {
    this.form.patchValue(formValue, {emitEvent: false});
    if (!!formValue?.policyId) {
      this.policyIdChange(formValue?.policyId, false);
      setTimeout(() => {
        this.policyForm?.patchValue(formValue, {emitEvent: false});
        setTimeout(() => {
          this.selectedBlockDataList = formValue?.blockDataList;
        });
      });
    }
  }

  override getDictList(): void {
    this.cardTypeList = [{id: 'ICCID', text: 'ICCID'}];

    const blockSizes = ['5', '10', '20', '50', '100', '200', '500', '1000', '2000', '5000'];
    this.cardBlockSizeList = blockSizes.map((v: any) => ({id: v, text: v}));

    this.dictTools.getMulitDictList([
      DictCode.POLICY_ID, DictCode.REUSE_MODE, DictCode.IMSI_VENDOR
    ]).subscribe(resp => {
      this.policyIdList = resp[DictCode.POLICY_ID];
      this.reuseModeList = resp[DictCode.REUSE_MODE];
      this.vendorList = resp[DictCode.IMSI_VENDOR];
    });
  }

  closeModal(value: any = null): void {
    this.modalRef?.close(value);
  }

  policyIdChange(value: any, resetValue: boolean = true): void {
    this.policyId = value;
    // 重置相关策略数据
    if (!value) {
      return;
    }

    let policyForm;
    switch (value) {
      case PolicyEnum.BLOCK: {
        policyForm = this.blockPolicyForm;
        if (resetValue) {
          this.blockPolicyForm.patchValue({blockDataList: []});
        }
      } break;
      case PolicyEnum.SPECIFIED_RANGE: {
        policyForm = this.rangePolicyForm;
        this.rangePolicyForm.patchValue({cardType: 'ICCID'});
      } break;
    }

    this.policyForm = policyForm;
  }

  vendorIdChange(value: any): void {
    if (!!value) {
      this.queryImsiBlockIfNeed();
    }
  }

  blockSizeChange(value: any): void {
    if (!!value) {
      this.queryImsiBlockIfNeed();
    }
  }

  queryImsiBlockIfNeed(): void {
    if (this.form.value?.policyId !== PolicyEnum.BLOCK || this.policyForm !== this.blockPolicyForm) {
      return;
    }

    const vendorId = this.form?.value?.vendorId;
    const blockSize = this.policyForm?.value?.blockSize;
    if (!vendorId || !blockSize) {
      return;
    }

    this.blockSearchParam = {vendorId, blockSize};
    this.blockComponent.onSearch(this.blockSearchParam);
  }

  changeSelectedBlockDataList(value: any): void {
    this.blockPolicyForm.patchValue({blockDataList: value});
  }

  doSubmit(): void {
    if (!this.checkParams()) {
      return;
    }
    // 检查选中数量是否满足条件,仅前端逻辑,不调用后台
    const expectCount = this.form?.value?.simQuantity / this.form?.value?.reuseAmount;
    if (this.form?.value?.simQuantity >= 0) {
      if (this.policyId === PolicyEnum.SPECIFIED_RANGE) {
        const count = BigInt(this.rangePolicyForm?.value.rangeEndIccid) - BigInt(this.rangePolicyForm?.value.rangeBeginIccid) + BigInt(1);
        if (count !== BigInt(expectCount)) {
          this.nwDialogService.error('当前填入范围与制卡数量不匹配');
          return;
        }
      } else if (this.policyId === PolicyEnum.BLOCK) {
        const count = this.blockPolicyForm?.value?.blockDataList?.reduce((sum: number, item: any) => sum + (item.blockSize || 0), 0);
        if (count !== expectCount) {
          this.nwDialogService.error('当前选中块与制卡数量不匹配');
          return;
        }
      }
    }

    this.closeModal({...this.form.value, ...this.policyForm?.value});
  }

  checkParams(): boolean {
    return super.checkParams() && this.policyForm?.valid;
  }
}
