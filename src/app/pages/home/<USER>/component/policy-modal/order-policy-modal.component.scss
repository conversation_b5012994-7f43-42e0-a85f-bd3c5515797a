.card-block-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  padding: 15px;
}

.card-block {
  padding: 20px;
  width: 200px;
  height: 110px;
  border-radius: 4px;

  color: inherit;
  display: flex;
  align-items: center;
  justify-content: space-between;

  position: relative;
  cursor: pointer;
}

.block-info {
  text-align: left;

  display: inline-block;
  cursor: pointer;
}


/* 提示内容 - 默认隐藏 */
.block-info .block-tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #fff;
  color: inherit;
  text-align: center;
  border: 1px solid #000;
  border-radius: 4px;
  padding: 8px;
  position: absolute;
  z-index: 1000;
  bottom: 100%; /* 显示在元素上方 */
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

/* 鼠标悬浮时显示 */
.block-info:hover .block-tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* 勾选样式*/
.mark-check {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px;
  border: 1px solid #fff;
}


.inline-range {
  display: inline-block;
  vertical-align: top;
  width: auto;
}

/* 如果需要和后面的内容同排 */
.inline-range + * {
  display: inline-block;
  margin-left: 15px; /* 适当间距 */
}

