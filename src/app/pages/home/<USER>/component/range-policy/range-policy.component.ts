import {Component, Input} from '@angular/core';
import {FormGroup} from '@angular/forms';
import {DictTools} from 'src/app/pages/global/dict.tools';

@Component({
  selector: 'app-policy-range',
  templateUrl: './range-policy.component.html',
  styles: [`:host {display: inline-block}`]
})
export class RangePolicyComponent {
  @Input() form!: FormGroup;

  @Input() disabled = false;

  @Input() fieldClass = 'col-lg-6';

  @Input() fieldLabelClass = 'col-lg-3';
  @Input() fieldContentClass = 'col-lg-6';

  cardTypeList = [{id: 'ICCID', text: 'ICCID'}];
  cardTypeStyle = 'min-width: 75px';

  constructor(public dictTools: DictTools) {
    if (this.disabled) {
      this.fieldClass = '';
    } else {
      this.fieldLabelClass = this.fieldContentClass = '';
    }
  }
}
