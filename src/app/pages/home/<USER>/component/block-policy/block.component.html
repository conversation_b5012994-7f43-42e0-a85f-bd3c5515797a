<div>
  <div class="block-container">
    <div *ngFor="let block of _blockTableData?.data" class="card-block" [style.background-color]="block.color"
         (click)="onClickBlock(block)">
      <div class="block-mark"><i class="fas fa-check" [style]="showCheckMark(block) ? {color: '#000'} : {color: block.color}"></i></div>
      <div class="block-info">
        <p>#{{ block.blockNo }}&nbsp;-&nbsp;{{ block.blockSize }}</p>
        <p>{{ block.reuseCount }} 次 - {{ block.reusePercent}}%</p>
        <span class="block-tooltip-text">{{ block.beginIccid }} <br/>-<br/> {{ block.endIccid }}</span>
      </div>
    </div>
  </div>

  <ng-container *ngIf="_pagination && _blockTableData?.data?.length">
    <nw-pagination [paginationData]="_pagination">
      <nw-pagination-pagesize [sizeOptions]="pageSizeOptions"
                              (change)="_onChangePageSize($event)"></nw-pagination-pagesize>
      <nw-pagination-navigation (change)="_onChangePage($event)"></nw-pagination-navigation>
    </nw-pagination>
  </ng-container>
</div>
