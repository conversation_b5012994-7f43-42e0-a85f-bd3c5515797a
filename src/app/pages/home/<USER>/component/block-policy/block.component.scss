.block-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;

  //background-color: rgba(0,0,0,.075);
  border: 1px solid #EBEBEB!important;
  margin-bottom: 10px;
  padding: 5px;
}

.block-container div {
  flex-basis: 9%;
}

.card-block {
  flex: 1;
  min-width: calc(12.5% - 30px * 7 / 8); // 考虑gap的精确计算
  padding: 5px;
  height: 5em;
  border: 1px solid #000;
  color: inherit;
  position: relative;
  cursor: pointer;
}

.block-info {
  text-align: left;
  display: inline-block;
  cursor: pointer;
}


/* 提示内容 - 默认隐藏 */
.block-info .block-tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #fff;
  color: inherit;
  text-align: center;
  border: 1px solid #000;
  border-radius: 4px;
  padding: 8px;
  position: absolute;
  z-index: 1000;
  bottom: 100%; /* 显示在元素上方 */
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

/* 鼠标悬浮时显示 */
.block-info:hover .block-tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* 勾选样式*/
.block-mark {
  position: absolute;
  top: 0;
  right: 0;
  padding: 1px;
  border: 1px solid #000000;
}

.inline-range {
  display: inline-block;
  vertical-align: top;
  width: auto;
}

/* 如果需要和后面的内容同排 */
.inline-range + * {
  display: inline-block;
  margin-left: 15px; /* 适当间距 */
}

