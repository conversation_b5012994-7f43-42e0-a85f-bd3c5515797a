import {Component, EventEmitter, Input, Output} from '@angular/core';
import {NwPaginationData} from '@ng-nwui/components';
import {ConfigConstant, SystemConstant} from 'src/app/pages/global/conts';
import {HttpClient} from 'src/app/services/http.client';

@Component({
  selector: 'app-block',
  templateUrl: './block.component.html',
  styleUrls: ['./block.component.scss']
})
export class BlockComponent {

  @Input() searchParam!: any;
  @Input() searchUrl!: any;
  @Input() pageSizeOptions = [24, 48, 96];
  @Input() selectedBlockDataList = [];

  @Output() clickBlock = new EventEmitter<any>();
  @Output() changeSelectedBlockDataList = new EventEmitter<any>();


  defaultPageSize = 24;
  _blockTableData!: any;
  _pagination?: NwPaginationData = new NwPaginationData();

  private pageOptions = {
    pageNum: SystemConstant.DEFAULT_PAGE_NUM,
    pageSize: this.defaultPageSize,
  };

  set blockTableData(value: any) {
    this._blockTableData = value;
    if (!value) {
      this._pagination = undefined;
    } else {
      this._pagination = Object.assign({}, value.pagination);
    }
  }

  get blockTableData(): any {
    return this._blockTableData;
  }

  constructor(protected httpService: HttpClient) {

  }

  onClickBlock(block: any): void {
    if (!block) {
      return;
    }

    const blockDataList = this.selectedBlockDataList;
    const index = blockDataList.findIndex((v: any) => v.blockNo == block.blockNo);

    // 若存在则删除,不存在则添加
    if (index !== -1) {
      blockDataList.splice(index, 1);
    } else {
      // @ts-ignore
      blockDataList.push(block);
    }
    this.selectedBlockDataList = [...blockDataList];
    this.clickBlock?.emit(block);

    this.changeSelectedBlockDataList?.emit(this.selectedBlockDataList);
  }

  showCheckMark(block: any): boolean {
    return !!this.selectedBlockDataList?.find((v: any) => v.blockNo == block.blockNo);
  }

  _onChangePageSize(pageSize: number): void {
    this.pageOptions.pageSize = pageSize;
    this.pageOptions.pageNum = 1;
    this.searchData();
  }

  _onChangePage(pageNum: number): void {
    this.pageOptions.pageNum = pageNum;
    this.searchData();
  }

  onSearch(params: any): void {
    this.searchParam = params;
    this.resetBlock();
    this.searchData();
  }

  private searchData(): void {
    this.searchParam.offset = (this.pageOptions.pageNum - 1) * this.pageOptions.pageSize;
    this.searchParam.limit = this.pageOptions.pageSize;
    this.httpService.post(this.searchUrl, this.searchParam, ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      const pagination = {
        currentPage: resp.pageNum,
        pageSize: resp.pageSize,
        totalRows: resp.total
      };

      if (!!this.selectedBlockDataList) {
        this.selectedBlockDataList.forEach((key: any) => {
          const target = resp.list.find((item: any) => item?.blockNo === key);
          if (!!target) {
            this.onClickBlock(target);
          }
        });
      }

      this.blockTableData = {data: resp.list, pagination};
    });
  }

  private resetBlock(): void {
    this.pageOptions = {
      pageNum: SystemConstant.DEFAULT_PAGE_NUM,
      pageSize: this.defaultPageSize,
    };
    this._blockTableData = {
      data: [],
      pagination: {
        currentPage: this.pageOptions.pageNum,
        pageSize: this.pageOptions.pageSize,
        totalRows: 0
      }
    };
    this.selectedBlockDataList = [];
    this.changeSelectedBlockDataList?.emit(this.selectedBlockDataList);
  }
}
