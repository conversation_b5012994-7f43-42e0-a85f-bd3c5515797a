import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DictTools} from '../../global/dict.tools';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule,
  NwDatetimeModule,
  NwDatetimeRangeModule,
  NwDialogModule,
  NwFormFieldModule,
  NwInputGroupModule,
  NwInputModule,
  NwLabelModule,
  NwModalService,
  NwPanelModule,
  NwRadioModule,
  NwScrollAnchorModule,
  NwSelectModule,
  NwSelectTreeModule,
  NwStepperModule,
  NwSwitchModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwTreeModule,
  NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {FactoryRoutingModule} from './factory-routing.module';
import {FactoryMgmtComponent} from './mgmt/factory-mgmt.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NwAuthModule} from "@ng-nwui/integration";
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {FactoryDetailComponent} from "./detail/factory-detail.component";
import {FactoryCreateComponent} from "./create/factory-create.component";
import {FactoryEditComponent} from "./edit/factory-edit.component";

@NgModule({
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    FactoryRoutingModule,
    NwInputGroupModule,
    PipeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwStepperModule,
    NwDatetimeModule
  ],
  declarations: [
    FactoryMgmtComponent,
    FactoryDetailComponent,
    FactoryCreateComponent,
    FactoryEditComponent
  ],
  providers: [DictTools, NwModalService]
})
export class FactoryModule {
}
