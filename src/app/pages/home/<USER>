<nw-frame>
  <nw-frame-header>
    <div class="header-left">
    </div>
    <div class="header-right">
      <span class="header-right-welcome">Welcome to NEWare Admin Portal.</span>
      <div class="header-right-info-email">
        <i class="fas fa-envelope"></i> <span class="label label-warning">16</span>
      </div>
      <div class="header-right-info-alert" style="position: relative">
        <i class="fas fa-bell"></i> <span class="label label-primary">8</span>
      </div>
      <a class="header-right-logout" (click)="onLogout()">
        <i class="fas fa-sign-out-alt"></i> <span>Log out</span>
      </a>
    </div>
  </nw-frame-header>
  <!-- 导航栏 -->
  <nw-navigation supportI18n [navs]="navs"></nw-navigation>
  <!-- 左侧菜单区域 -->
  <nw-frame-menu>
    <nw-frame-menu-top>
      <img src="./assets/img/profile_photo.png" alt="img" class="rounded-circle profile-photo"/>
      <nw-dropdown class="login-name" color="transparent" [title]="username"
                   (itemClick)="onClickItem($event)"
                   [items]="profileItem"></nw-dropdown>
    </nw-frame-menu-top>
    <nw-menu [menus]="menus"></nw-menu>
  </nw-frame-menu>
  <!-- 中间内容区域 -->
  <nw-frame-content></nw-frame-content>
  <nw-frame-footer>
    Copyright © 2015-2024 NEWare All rights reserved
  </nw-frame-footer>

  <!-- 右侧快捷访问区域 -->
  <nw-quick-sidebar
    [config]="['history']"
    [visitHistories]="visitHistories"
    (deleteVisitHistory)="onDeleteHistory($event)">
  </nw-quick-sidebar>
</nw-frame>
