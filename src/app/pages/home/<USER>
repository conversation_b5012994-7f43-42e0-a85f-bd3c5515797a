import {FormBuilder, FormGroup} from '@angular/forms';
import {NwDialogService} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {HttpClient} from 'src/app/services/http.client';
import { DictTools } from '../global/dict.tools';
import { HomeService } from './home.service';
import {BusiCommonVerifyService} from "../../services/busi-common-verify.service";

export abstract class FormService {

  homeService!: HomeService;
  form!: FormGroup;

  constructor(
    protected fb: FormBuilder,
    protected nwDialogService: NwDialogService,
    protected apiService: HttpClient,
    protected nwI18nService: NwI18nService,
    protected dictTools: DictTools) {
    this.homeService = new HomeService(apiService, nwI18nService);
    this.initForm();
    this.getDictList();
  }

  protected abstract initForm(): void;
  protected abstract getDictList(): void;
  setForm(formValue: any): void {
    this.form.patchValue(formValue, {emitEvent: false});
  }

  resetForm(): void {
    NwFormUtils.reset(this.form);
  }

  checkParams(): boolean {
    return this.form.valid;
  }

  buildParams(): any {
    return Object.assign({}, this.form.value);
  }

  addExtendParam(params: any, extendParams: any): void {
    if (!!extendParams) {
      Object.assign(params, extendParams);
    }
  }

  submit(url: any, messageNotify: any, extendParam?: any): void {
    if (!this.checkParams()) {
      return;
    }
    this.nwDialogService.confirm(this.nwI18nService.getI18nValue('i18n_public.submit.tips'),
      () => {
        const params = this.buildParams();
        this.addExtendParam(params, extendParam);
        this.apiService.post(url, params).subscribe((res) => {
          this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
            this.resetForm();
            messageNotify?.emit(true);
          });
        }, (res: any) => {
          this.nwDialogService.error(res?.apiResponse?.message);
        });
      }, () => {
      });
  }
}
