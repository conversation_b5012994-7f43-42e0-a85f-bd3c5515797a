@use "sass:math";

$mini-menu-top-height: 60px;
$mini-photo-size: 25px;
$mini-photo-margin-top: math.div($mini-menu-top-height, 2);

// header 自定义配置
nw-frame-header {
  .header-left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .header-left-search-input {
      margin-left: 2px;
      border: none;
    }
  }

  .header-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;

    .header-right-welcome {
      color: #888;
    }

    .header-right-info-email, .header-right-info-alert {
      font-size: 14px;
      height: 100%;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      color: #999c9e;

      > span.label {
        font-size: 10px;
        line-height: 12px;
        padding: 2px 5px;
        position: absolute;
        left: calc(50%);
        top: calc(50% - 18px);
      }
    }

    .header-right-logout {
      font-size: 14px;
      color: #999c9e;
      text-decoration: none;
      padding: 0 10px;
      font-weight: 600;

      &:hover {
        color: #676A6C;
      }

      > i {
        margin-right: 6px;
      }
    }

  }
}

// 菜单mini状态下固定的class=nw-menu-mini
.nw-menu-mini {
  nw-frame-menu {
    nw-frame-menu-top {
      height: $mini-menu-top-height;

      .profile-photo {
        height: $mini-photo-size;
        width: $mini-photo-size;
        margin-top: $mini-photo-margin-top;
      }

      .login-name {
        opacity: 0;
        visibility: hidden;
      }
    }
  }
}

nw-frame-menu {
  nw-frame-menu-top {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 150px;
    width: 100%;

    transition: all .3s;

    .profile-photo {
      height: 50px;
      width: 50px;
      transition: height .3s, width .3s, margin-top .3s;
    }

    .login-name {
      color: #DFE4ED;
      opacity: 1;
      visibility: visible;
      transition: all .3s;
    }
  }
}

@media (max-width: 768px) {
  nw-frame-header {
    .header-right {
      .header-right-welcome {
        display: none;
      }
    }
  }
}

@media (max-width: 480px) {
  nw-frame-header {
    .header-right {
      .header-right-logout > span {
        display: none;
      }
    }
  }
}
