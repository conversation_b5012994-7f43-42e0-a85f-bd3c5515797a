import {AfterContentInit, Component} from '@angular/core';
import {NwDialogService, NwDropDownItem, NwMenuData} from '@ng-nwui/components';
import {
  NW_INTEGRATION_I18NS,
  NwHttpErrorResponse,
  NwI18nService,
  NwPermissionService,
  NwRouteReuseService
} from '@ng-nwui/core';
import {Router} from '@angular/router';
import {NwAuthService, NwIntegrationAbstractComponent, NwIntegrationService} from '@ng-nwui/integration';
import {Observable} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {NwFrameCoreService} from '@ng-nwui-frame/core';
import {ApiConstant} from "../global/conts/api.constant";
import {ConfigConstant} from "../global/conts/config.constant";
import {HttpClient} from "../../services/http.client";

@Component({
  selector: 'app-root',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent extends NwIntegrationAbstractComponent implements AfterContentInit {
  private _i18ns!: any;
  username?: string;
  profileItem = [
    {id: 'logout', text: this.i18nService.getI18nValue('i18n_header.logout'), icon: 'fas fa-sign-out-alt'},
    {id: 'changePassword', text: this.i18nService.getI18nValue('i18n_header.changePassword'), icon: 'fas fa-key'},
    {id: 'myJob', text: this.i18nService.getI18nValue('i18n_privilege.job'), icon: 'fas current-icon fa-thumbtack'},
    {id: 'publicKey', text: this.i18nService.getI18nValue('i18n_key_my'), icon: 'fas fa-key'}
  ];
  // 是否强制刷新页面
  private _forceReload = false;

  constructor(protected router: Router,protected apiService: HttpClient,
              protected i18nService: NwI18nService,
              protected routeReuseService: NwRouteReuseService,
              protected integrationService: NwIntegrationService,
              protected permissionService: NwPermissionService,
              private dialogService: NwDialogService,
              private authService: NwAuthService, private frameCoreService: NwFrameCoreService) {
    super(router, i18nService, routeReuseService, integrationService, permissionService);
    this._i18ns = this.i18nService.getComponentI18n(NW_INTEGRATION_I18NS.ReLoginDialog);
    this.i18nService.localeChange.subscribe(() => {
      this._i18ns = this.i18nService.getComponentI18n(NW_INTEGRATION_I18NS.ReLoginDialog);
    });
  }

  ngAfterContentInit(): void {
    this.username = this.permissionService.username;
  }

  nwOnDestroy(): void {
  }

  nwOnInit(): void {
  }

  getMenus(): Observable<NwMenuData[]> {
    return this.frameCoreService.menuList.pipe(
      catchError((err, caught) => {
        // 如果在获取菜单时异常，则在登陆成功后，需要强制刷新页面，来重新获取菜单
        this._forceReload = true;
        throw err;
      })
    );
  }

  onTokenExpire(): void {
    this.integrationService.showReLoginDialog(
      password => {
        return new Promise<{ success: boolean; message?: string }>((resolve) => {
            this.frameCoreService.login({
              username: this.permissionService.username,
              password,
              language: this.i18nService.getCurrentLocale() || 'us'
            }).subscribe(
              () => {
                if (this._forceReload) {
                  window.location.reload();
                } else {
                  resolve({success: true, message: 'success'});
                }
              },
              (error: NwHttpErrorResponse) => {
                resolve({success: false, message: error.message});
              });
          }
        );
      },
      () => this.router.navigateByUrl('/login').then(_ => {
        return {success: true};
      }).catch(_ => {
        return {success: false};
      })
    )
    ;
  }

  onClickItem(item: NwDropDownItem): void {
    switch (item.id) {
      case 'logout':
        this.onLogout();
        break;
      case 'changePassword':
        this.onChangePwd();
        break;
      case 'publicKey':
        this.onPublicKey();
        break;
      case 'myJob':
        this.onMyJob();
        break;
    }
  }

  onChangePwd(): void {
    this.router.navigate(['/personal/changePassword']);
  }

  onLogout(): void {
    this.frameCoreService.logout().subscribe(() => {
      console.log(`logout success`);
    });
  }

  onPublicKey(): void {
    this.router.navigate(['/system/key/public_key']);
  }

  onMyJob(): void {
    this.router.navigate(['/myjob/jobList']);
  }

  showRenewalWarningDialog(count:any): void {
    // 文本设置
    const title = this.i18nService.getI18nValue('i18n_batch_profile_warning.dialog.title');
    const denyButtonText = this.i18nService.getI18nValue('i18n_batch_profile_warning.dialog.denyButtonText');
    const content= this.i18nService.getI18nValue('i18n_batch_profile_warning.dialog.content',undefined,[count]);
    const confirmButtonText = denyButtonText;
    const cancelButtonText = this.i18nService.getI18nValue('i18n_public.cancel.button');
    // 按钮展示设置
    const showDenyButton = true;
    const showConfirmButton = false;
    const showCancelButton = false;
    this.dialogService.custom({
      icon: 'warning',
      title,
      html: `<div class="alert alert-info">`+content+`</div>`,
      denyButtonText,
      confirmButtonText,
      cancelButtonText,
      allowEscapeKey: false,
      allowOutsideClick: false,
      showDenyButton,
      showConfirmButton,
      showCancelButton,
    }).then((result) => {
      if (result.isDismissed) {
        return;
      }
     // this.router.navigateByUrl('/personal/changePassword');
    });
  }
}
