import {NwSelectData} from "@ng-nwui/components";
import {ApiConstant, RegularConstant} from '../global/conts';
import {MerchantStatus} from '../global/enums/merchant-status.enum';
import {ConfigConstant} from '../global/conts';
import {Observable} from 'rxjs';
import {NwHttpConfig, NwI18nService, NwValidatorResult, NwValidators} from '@ng-nwui/core';
import {HttpClient} from '../../services/http.client';
import {FormGroup} from '@angular/forms';
import {ColorInfo} from "../global/conts/color.constant";
import {map} from "rxjs/internal/operators/map";
import {SystemConstant} from "@ng-nwui-frame/merchant-manage";

export class HomeService {

  constructor(protected apiService: HttpClient,
              protected i18nService: NwI18nService) {
  }

  tenantList!: NwSelectData[];
  locale: any = this.i18nService.getCurrentLocale() as string;

  requiredValidator = NwValidators.required(this.i18nService.getI18nValue('i18n_public.field.required'));
  positiveIntegerValidator = NwValidators.pattern(RegularConstant.POSITIVE_INTEGER, this.i18nService.getI18nValue('i18n_public.validator.positive-integer.tips'));
  emailValidator = NwValidators.pattern(RegularConstant.EMAIL, this.i18nService.getI18nValue('i18n_merchant.mgmt.new.form.email.pattern'));

  emailListVerify = NwValidators.syncValidator((control) => {
    const nwValidatorResult = new NwValidatorResult();
    let result = true;
    if (control?.value?.length) {
      for (const email of control?.value) {
        if (!RegularConstant.EMAIL.test(email?.id)) {
          result = false;
        }
      }
    }
    nwValidatorResult.result = result;
    return nwValidatorResult;
  }, 'i18n_merchant.mgmt.new.form.email.pattern');
  maxLengthValidatorFn = (maxLength: number) => NwValidators.maxLength(maxLength, this.i18nService.getI18nValue('i18n_public.public.from.length_tips',
    undefined, [`${maxLength}`]))

  getColor(colorInfos: ColorInfo[], value: any): any {
    return colorInfos.find(v => v.value === value)?.color;
  }


  initTenant(): void {
    this.apiService.post(
      ApiConstant.COMBOBOX_TENANT_LIST_BY_STATUS,
      {status: MerchantStatus.PUBLISHED},
      ConfigConstant.noLoadingConfig()
    ).subscribe((resp: any) => {
      this.tenantList = resp;
    });
  }

  getTenantList(): Observable<NwSelectData[]> {
    const config = new NwHttpConfig();
    config.loading = false;
    return this.apiService.post(ApiConstant.TENANT_LIST_BY_STATUS, {status: MerchantStatus.PUBLISHED}, config);
  }

  // 获取跳转日程安排的数量限制
  getSubmitRunLimitRows(): Observable<number> {
    return this.apiService.post(ApiConstant.SUBMIT_RUN_LIMIT_NUM, {}, ConfigConstant.noLoadingConfig());
  }

  // 获取jobId
  getNextBatchSn(): Observable<string> {
    return this.apiService.post(ApiConstant.NEXT_JOB_ID, {}, ConfigConstant.noLoadingConfig());
  }

  // 获取实时下载与跳转批量任务下载的数量限制
  getTimingDownloadMaxNum(): Observable<number> {
    return this.apiService.post(ApiConstant.TIMING_DOWNLOAD_MAX_NUM, {}, ConfigConstant.noLoadingConfig());
  }

  addValidators(form: FormGroup, validators: any, ...fields: any): void {
    for (const field of fields) {
      const control = form.get(field);
      if (control) {
        control.setValue(null);
        control.addValidators(validators);
        control.updateValueAndValidity();
      }
    }
  }

  clearValidators(form: FormGroup, ...fields: any): void {
    for (let field of fields) {
      const control = form.get(field);
      if (control) {
        control.setValue(null);
        control.clearValidators();
        control.updateValueAndValidity();
      }
    }
  }

  removeValidators(form: FormGroup, validators: any, ...fields: any): void {
    for (const field of fields) {
      const control = form.get(field);
      if (control) {
        control.setValue(null);
        control.removeValidators(validators);
        control.updateValueAndValidity();
      }
    }
  }

  buildFactorySelectConfig(extraParam: any = null): any {
    return this.buildSelectConfig(ApiConstant.VENDOR_LIST, (v: any) => ({
      text: v.name,
      id: v.factoryId
    }), {status: '00', ...extraParam});
  }

  buildCustSelectConfig(): any {
    return this.buildSelectConfig(ApiConstant.CUSTOMER_LIST, (v: any) => ({
      text: v.name,
      id: v.custId
    }), {status: '00'});
  }

  buildSelectConfig(url: any, mapFunction: (v: any) => any, extraParam: any = null, selection: any = 'single'): any {
    const result = {
      selection,
      clearable: true,
      search: true,
      // @ts-ignore
      searchFn: (searchVale, pagination): Observable<NwSelectPaginationData> => {
        pagination = {currentPage: (pagination?.currentPage || 0) + 1, pageSize: 10, totalRows: 0};
        const params = {...pagination, ...extraParam};
        // @ts-ignore
        return this.apiService.post(url, params, ConfigConstant.noLoadingConfig())
          .pipe(map((data: any) => {
            // @ts-ignore
            pagination.totalRows = data?.total;
            if (!!mapFunction) {
              data = data?.list?.map((v: any) => ({...v, ...mapFunction(v)}));
            }
            return {data, pagination};
          }));
      }
    };
    return result;
  }
}
