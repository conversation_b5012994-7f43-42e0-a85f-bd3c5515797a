import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {DashboardComponent} from './dashboard/dashboard.component';
import {HomeComponent} from './home.component';
import {NwRouteAuthGuard, NwRouteSettings} from '@ng-nwui/core';
import {KeyModule} from "./key/key.module";

const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
    canActivateChild: [NwRouteAuthGuard],
    children: [
      {
        path: 'dashboard',
        component: DashboardComponent,
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'merchant/mgmt',
        loadChildren: () => import('@ng-nwui-frame/merchant-manage/merchant-manage').then(m => m.MerchantManageModule),
        ...NwRouteSettings.routeReuse()
      },

      {
        path: 'system/privilege/org',
        loadChildren: () => import('@ng-nwui-frame/merchant-manage/organization-manage').then(m => m.OrganizationManageModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'merchant/setting/appSetting',
        loadChildren: () => import('@ng-nwui-frame/merchant-manage/app-setting').then(m => m.AppSettingModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'merchant/log/applogs',
        loadChildren: () => import('@ng-nwui-frame/system-manage/app-logs').then(m => m.AppLogsModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'system/privilege/role',
        loadChildren: () => import('@ng-nwui-frame/privilege-manage/role-manage').then(m => m.RoleManageModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'system/privilege/staff',
        loadChildren: () => import('@ng-nwui-frame/privilege-manage/staff-manage').then(m => m.StaffManageModule),
        ...NwRouteSettings.routeReuse()
      },

      {
        path: 'system/logs/operation',
        loadChildren: () => import('@ng-nwui-frame/system-manage/operation-logs').then(m => m.OperationLogsModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'system/logs/login',
        loadChildren: () => import('@ng-nwui-frame/system-manage/login-logs').then(m => m.LoginLogsModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'system/console/cache',
        loadChildren: () => import('@ng-nwui-frame/system-manage/cache').then(m => m.CacheModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'system/jobs/schedule',
        loadChildren: () => import('@ng-nwui-frame/system-manage/task').then(m => m.TaskModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'system/setting',
        loadChildren: () => import('@ng-nwui-frame/system-manage/system-setting').then(m => m.SystemSettingModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'personal',
        loadChildren: () => import('./change-password/change-password.module').then(m => m.ChangePasswordModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full'
      },
      {
        path: 'myjob',
        loadChildren: () => import('./myjob/my.job.module').then(m => m.MyJobModule),
      },

      {
        path: 'factory',
        loadChildren: () => import('./factory/factory.module').then(m => m.FactoryModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'customer',
        loadChildren: () => import('./customer/customer.module').then(m => m.CustomerModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'email',
        loadChildren: () => import('./email/email.module').then(m => m.EmailModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'key',
        loadChildren: () => import('./key/key.module').then(m => m.KeyModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'imsi',
        loadChildren: () => import('./imsi/imsi.module').then(m => m.ImsiModule),
        ...NwRouteSettings.routeReuse()
      },
      {
        path: 'order',
        loadChildren: () => import('./order/order.module').then(m => m.OrderModule),
        ...NwRouteSettings.routeReuse()
      },
    ],
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule {
}
