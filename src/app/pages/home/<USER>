import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DashboardComponent} from './dashboard/dashboard.component';
import {HomeRoutingModule} from './home-routing.module';
import {HomeComponent} from './home.component';
import {
  NwButtonModule,
  NwDropdownModule,
  NwFrameModule,
  NwInputModule,
  NwMenuModule,
  NwNavigationModule,
  NwQuickSidebarModule
} from '@ng-nwui/components';
import {DictTools} from "../global/dict.tools";

@NgModule({
  declarations: [
    DashboardComponent,
    HomeComponent,
  ],
  imports: [
    CommonModule,
    HomeRoutingModule,
    NwMenuModule,
    NwButtonModule,
    NwQuickSidebarModule,
    NwNavigationModule,
    NwDropdownModule,
    NwFrameModule,
    NwInputModule,
  ],
  providers :[
    DictTools
  ],
})
export class HomeModule {
}
