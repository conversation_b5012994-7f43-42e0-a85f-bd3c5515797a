import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ApiConstant} from 'src/app/pages/global/conts/api.constant';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {DomSanitizer} from "@angular/platform-browser";

@Component({
  selector: 'app-email-detail',
  templateUrl: './email-detail.component.html',
  styleUrls: ['./email-detail.component.scss']
})
export class EmailDetailComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder, protected sanitizer: DomSanitizer) {
  }

  @Input() selectedRow !: any;
  @Output() closeReturn = new EventEmitter<any>();

  data!: any;

  emailTpl!: any;
  emailTplStyle = {border: '1px solid #e5e6e7'};


  ngOnInit(): void {
    this.getDetail();
  }

  getDetail(): void {
    this.apiService.post(`${ApiConstant.EMAIL_DETAIL}/${this.selectedRow?.factoryEmailId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.data = {...resp, ccTo: resp?.ccTo?.split(',')?.map((v: any) => ({id: v, text: v}))};
      this.getTpl(resp?.operation);
    });
  }

  getTpl(operation: any): void {
    this.apiService.post(`${ApiConstant.EMAIL_TPL_DETAIL}/${operation}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.emailTpl = this.sanitizer.bypassSecurityTrustHtml(resp?.mailBody);
    });
  }


  closePanel(): void {
    this.closeReturn.emit();
  }
}
