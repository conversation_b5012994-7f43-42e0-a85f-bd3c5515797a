import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ApiConstant} from 'src/app/pages/global/conts/api.constant';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';

@Component({
  selector: 'app-key-detail',
  templateUrl: './key-detail.component.html',
  styleUrls: ['./key-detail.component.scss']
})
export class KeyDetailComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder) {
  }

  @Input() selectedRow !: any;
  @Output() closeReturn = new EventEmitter<any>();

  data!: any;

  ngOnInit(): void {
    this.getDetail();
  }

  getDetail(): void {
    this.apiService.post(`${ApiConstant.KEY_DETAIL}/${this.selectedRow?.keyId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.data = resp;
    });
  }


  closePanel(): void {
    this.closeReturn.emit();
  }
}
