import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {DateUtils} from "../../../global/utils";

@Component({
  selector: 'app-imsi-detail',
  template: `
    <nw-panel>
      <nw-panel-header label="{{'IMSI详情'| nwi18n}}">
      </nw-panel-header>
      <nw-panel-content>
        <div class="row" *ngIf="selectedRow">
          <div class="col-md-6">
            <busi-nw-info-field label="IMSI">{{selectedRow.imsi}}</busi-nw-info-field>
            <busi-nw-info-field label="MSISDN">{{selectedRow.msisdn}}</busi-nw-info-field>
            <busi-nw-info-field label="供应商ID">{{selectedRow.vendorId}}</busi-nw-info-field>
            <busi-nw-info-field label="重用次数">{{selectedRow.reuseCount}}</busi-nw-info-field>
          </div>
          <div class="col-md-6">
            <busi-nw-info-field label="服务开始日期">{{selectedRow.serviceStartDate}}</busi-nw-info-field>
            <busi-nw-info-field label="服务结束日期">{{selectedRow.serviceEndDate}}</busi-nw-info-field>
            <busi-nw-info-field label="状态">{{selectedRow.statusName}}</busi-nw-info-field>
            <busi-nw-info-field label="创建时间">{{selectedRow.createTime}}</busi-nw-info-field>
          </div>
        </div>
      </nw-panel-content>
      <nw-panel-footer>
        <button nw-button color="secondary" (click)="close()">{{'关闭'|nwi18n}}</button>
      </nw-panel-footer>
    </nw-panel>
  `,
  styleUrls: ['./imsi-detail.component.scss']
})
export class ImsiDetailComponent implements OnInit {

  @Input() selectedRow: any;
  @Output() closeReturn = new EventEmitter<boolean>();

  constructor(public nwI18nService: NwI18nService) { }

  ngOnInit(): void {
    if(this.selectedRow?.serviceStartDate){
      this.selectedRow.serviceStartDate=DateUtils.formatCompactDateTimeToLocale(this.selectedRow?.serviceStartDate, this.nwI18nService.getCurrentLocale() as string);
    }
    if(this.selectedRow?.serviceEndDate){
      this.selectedRow.serviceEndDate=DateUtils.formatCompactDateTimeToLocale(this.selectedRow?.serviceEndDate, this.nwI18nService.getCurrentLocale() as string);
    }
  }

  close(): void {
    this.closeReturn.emit(false);
  }
}
