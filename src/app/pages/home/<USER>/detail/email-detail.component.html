<nw-panel>
  <nw-panel-header label="{{'i18n_public.detail.button'| nwi18n}}" [icons]="['close']" (iconClick)="closePanel()">
  </nw-panel-header>
  <nw-panel-content noPaddingVertical="true" noPaddingHorizontal="true">
    <nw-form-field label="{{'i18n_factory.form.name'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.factoryName}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_email.form.operation'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.operationName}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_email.form.send.to'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.sendTo}}"></nw-input>
    </nw-form-field>

    <nw-form-field label="{{'i18n_email.form.cc.to'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input-multiple disabled [value]="data?.ccTo"></nw-input-multiple>
    </nw-form-field>
    <nw-form-field label="{{'i18n_email.form.send.by'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.sendBy}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_email.form.subject'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.subject}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_email.form.content'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6" required *ngIf="!!emailTpl">
      <div [innerHTML]="emailTpl" [style]="emailTplStyle"></div>
    </nw-form-field>
    <nw-form-field label="{{'i18n_production.create.name'| nwi18n}}" layout="horizontal"
                   labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.createTime | nwDTF}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_production.create.time'| nwi18n}}" layout="horizontal"
                   labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.createTime | nwDTF}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_production.update.name'| nwi18n}}" layout="horizontal" labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.updateName}}"></nw-input>
    </nw-form-field>
    <nw-form-field label="{{'i18n_production.update.time'| nwi18n}}" layout="horizontal"
                   labelAlign="right"
                   labelClass="col-lg-3" fieldClass="col-lg-6">
      <nw-input disabled value="{{data?.updateTime | nwDTF}}"></nw-input>
    </nw-form-field>
  </nw-panel-content>
</nw-panel>
