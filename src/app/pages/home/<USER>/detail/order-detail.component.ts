import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService, NwModalService} from '@ng-nwui/components';
import {ApiConstant, ConfigConstant} from 'src/app/pages/global/conts';
import {PolicyEnum} from 'src/app/pages/global/enums/policy.enum';
import {DictTools} from 'src/app/pages/global/dict.tools';
import {DateUtils} from '../../../global/utils';
import {SimPrintTypeEnum} from 'src/app/pages/global/enums/sim.print.type.enum';
import {SimCosTypeEnum} from 'src/app/pages/global/enums/sim.cos.type.enum';

@Component({
  selector: 'app-order-detail-form',
  templateUrl: './order-detail.component.html',
  styleUrls: ['./order-detail.component.scss']
})
export class OrderDetailComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools, private nwModalService: NwModalService) {
  }

  @Input() selectedRow: any;

  @Output() closeReturn = new EventEmitter<any>();

  PolicyEnum = PolicyEnum;
  SimPrintTypeEnum = SimPrintTypeEnum;
  SimCosTypeEnum =  SimCosTypeEnum;

  DateUtils = DateUtils;

  data!: any;

  locale = this.nwI18nService.getCurrentLocale() as string;

  ngOnInit(): void {
    this.getDetail();
  }

  getDetail(): any {
    this.apiService.post(`${ApiConstant.ORDER_DETAIL}/${this.selectedRow?.orderId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.data = resp;
    });
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  download(fileId: any): void {
    this.apiService.download(ApiConstant.FILE_DOWNLOAD_V2, {fileId}).subscribe((res) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_public.success'));
    });
  }

  submit(): any {
    this.closePanel();
  }
}
