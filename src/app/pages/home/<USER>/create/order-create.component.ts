import {After<PERSON>iewInit, Component, <PERSON>E<PERSON>ter, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService, NwModalService, NwStepper,} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {OrderFormService} from '../order-form.service';
import {PolicyEnum} from 'src/app/pages/global/enums/policy.enum';
import {AnalyticsCommand} from "@angular/cli/commands/analytics-impl";

@Component({
  selector: 'app-order-create',
  templateUrl: './order-create.component.html',
  styleUrls: ['./order-create.component.scss']
})
export class OrderCreateComponent implements On<PERSON>nit, OnD<PERSON>roy, AfterViewInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools, private nwModalService: NwModalService) {
    this.formService = new OrderFormService(fb, nwDialogService, apiService, nwI18nService, dictTools, nwModalService);
  }


  formService: OrderFormService;

  PolicyEnum = PolicyEnum;

  @ViewChild('createNwStepper')
  createNwStepper!: NwStepper;

  closeReturn = new EventEmitter<any>();

  ngOnInit(): void {

  }

  ngOnDestroy(): void {
    this.formService.destroy();
  }

  ngAfterViewInit(): void {
    this.closeReturn.emit = (value?: any) => {
     if (value && this.createNwStepper) {
       this.createNwStepper.switchTo(0);
     }
    };
  }


  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.ORDER_CREATE, this.closeReturn);
  }

}
