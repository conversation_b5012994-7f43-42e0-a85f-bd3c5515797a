import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {NwDialogService} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {DictCode} from '../../../global/enums/dict-code.enum';
import { HomeService } from '../../home.service';
import {FactoryFormService} from '../../factory/factory-form.service';

@Component({
  selector: 'app-factory-create',
  templateUrl: './customer-create.component.html',
  styleUrls: ['./customer-create.component.scss']
})
export class CustomerCreateComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools) {
    this.formService = new FactoryFormService(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();

  formService: FactoryFormService;

  ngOnInit(): void {

  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.CUSTOMER_CREATE, this.closeReturn);
  }
}
