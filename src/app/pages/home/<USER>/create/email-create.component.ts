import {AfterViewInit, Component, EventEmitter, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService,} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {EmailFormService} from '../email-form.service';
import {DomSanitizer} from "@angular/platform-browser";

@Component({
  selector: 'app-email-create',
  templateUrl: './email-create.component.html',
  styleUrls: ['./email-create.component.scss']
})
export class EmailCreateComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools, protected sanitizer: DomSanitizer) {
    this.formService = new EmailFormService(fb, nwDialogService, apiService, nwI18nService, dictTools, sanitizer);
  }

  @Output() closeReturn = new EventEmitter<any>();

  formService: EmailFormService;

  ngOnInit(): void {

  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.EMAIL_CREATE, this.closeReturn);
  }
}
