<nw-panel>
  <nw-panel-header label="{{'i18n_public.create.button'| nwi18n}}" [icons]="['close']" (iconClick)="closePanel()">
  </nw-panel-header>
  <nw-panel-content>
    <div [formGroup]="formService.form">
      <nw-form-field label="{{'i18n_factory.form.name'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-select [selectConfig]="formService.homeService.buildFactorySelectConfig()" formControlName="factoryId"
                   (change)="formService.factoryChange($event)"></nw-select>
      </nw-form-field>
      <nw-form-field label="{{'i18n_key.form.operation'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-select [selectConfig]="dictTools.simpleSelectConfig" [selectData]="formService.operationList"
                   formControlName="operation" [disabled]="formService.disableOperation()"></nw-select>
      </nw-form-field>
      <nw-form-field label="{{'i18n_key.form.key.type'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-select [selectConfig]="dictTools.simpleSelectConfig" [selectData]="formService.keyTypeList"
                   formControlName="keyType" (change)="formService.keyTypeChange($event)"></nw-select>
      </nw-form-field>

      <nw-form-field label="{{'i18n_key.form.key.alg'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-select [selectConfig]="dictTools.simpleSelectConfig" [selectData]="formService.keyAlgList"
                   formControlName="keyAlg" [disabled]="formService.disableKeyAlg()" (change)="formService.keyAlgChange($event)"></nw-select>
      </nw-form-field>

      <nw-form-field label="{{'i18n_key.form.key.file'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-input-file #nwInputFile [config]="formService.fileConfig" (selectFile)="formService.onSelectFile($event)"></nw-input-file>
      </nw-form-field>

    </div>
  </nw-panel-content>
  <nw-panel-footer style="text-align: center;">
    <button icon="far fa-check" nw-button [color]="'primary'" [disabled]="!formService.checkParams()"
            (click)="submit()">{{'i18n_public.submit.button'|nwi18n}}</button>&nbsp;
    <button nw-button color="warning" icon="fa-times"
            (click)="cancel()">{{'i18n_public.cancel.button' |nwi18n}}</button>
  </nw-panel-footer>
</nw-panel>
