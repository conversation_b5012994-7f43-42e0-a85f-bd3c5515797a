<nw-panel>
  <nw-panel-header label="{{'订单创建'| nwi18n}}" [icons]="['close']">
  </nw-panel-header>
  <nw-panel-content>
    <nw-stepper (finishStep)="submit()" (switchStep)="formService.switchStep($event)" #createNwStepper>
<!--      <nw-step label="{{'采购合同'|nwi18n}}" [validatable]="!formService.form?.valid">-->
      <nw-step label="{{'采购合同'|nwi18n}}">
        <div [formGroup]="formService.form">
          <nw-form-field label="{{'采购订单号'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input formControlName="contractNo"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'客户名称'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectConfig]="formService.homeService.buildCustSelectConfig()"
                       formControlName="custId"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'制卡工厂'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectConfig]="formService.buildFactorySelectConfig()"
                       formControlName="factoryId"></nw-select>
          </nw-form-field>

          <nw-form-field label="{{'制卡类型'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.simTypeList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="simType"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'制卡数量'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input formControlName="simQuantity"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'制卡规格'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.simSpecList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="simSpec"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'COS类型'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.simCosTypeList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="simCosType" (change)="formService.simCosTypeChange($event)"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'COS版本'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" *ngIf="formService.showSimCosFileId" required>
            <nw-select [selectData]="formService.simCosVersionList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="simCosFileId"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'版面供装方式'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.simPrintTypeList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="simPrintType" (change)="formService.simPrintTypeChange($event)"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'版面文件'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" *ngIf="formService.showSimPrintFileId" required>
            <nw-input-file formControlName="simPrintFileId"
                           class="col-lg-12"
                           [config]="formService.fileConfig" [hook]="formService.hook"
                           placeholder="{{'i18n_public.query.select.please-holder.tips' | nwi18n}}">
            </nw-input-file>
          </nw-form-field>
          <nw-form-field label="{{'采购合同附件1'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>

            <nw-input-file formControlName="contractFileId1"
                           class="col-lg-12"
                           [config]="formService.fileConfig" [hook]="formService.hook"
                           placeholder="{{'i18n_public.query.select.please-holder.tips' | nwi18n}}">
            </nw-input-file>
          </nw-form-field>
          <nw-form-field label="{{'采购合同附件2'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6">
            <nw-input-file formControlName="contractFileId2"
                           class="col-lg-12"
                           [config]="formService.fileConfig" [hook]="formService.hook"
                           placeholder="{{'i18n_public.query.select.please-holder.tips' | nwi18n}}">
            </nw-input-file>
          </nw-form-field>
          <nw-form-field label="{{'备注'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6">
            <nw-textarea formControlName="contractRemark"></nw-textarea>
          </nw-form-field>
        </div>
      </nw-step>
      <nw-step label="{{'制卡数据'|nwi18n}}" [validatable]="!formService.policyForm?.valid">
        <div [formGroup]="formService.policyForm">
          <nw-form-field label="{{'复用模式'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.reuseModeList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="reuseMode" (change)="formService.reuseModeChange($event)"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'复用次数'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required *ngIf="formService.showReuseAmount">
            <nw-input formControlName="reuseAmount"></nw-input>
          </nw-form-field>
          <nw-form-field label="{{'智能选号策略'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-input-group>
              <nw-select [selectData]="formService.policyIdList" [selectConfig]="dictTools.simpleSelectConfig" *ngIf="!!formService.policyId" style="flex: 1"
                         formControlName="policyId" [disabled]="true" nw-input-group-prepend></nw-select>
              <button nw-input-group-append nw-button outline color="white" size="small" (click)="formService.clickPolicyId()"
                      icon="fa fa-graduation-cap"></button>
            </nw-input-group>
          </nw-form-field>

          <ng-container *ngIf="formService.policyId === PolicyEnum.SPECIFIED_RANGE">
            <nw-form-field label="{{'首ICCID' | nwi18n}}"
                           layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" >
              <nw-input-group>
                <nw-select nw-input-group-prepend formControlName="cardType"
                           [selectConfig]="dictTools.simpleSelectConfig"
                           [selectData]="formService.cardTypeList" [disabled]="true"
                           [style]="formService.cardTypeStyle">
                </nw-select>
                <nw-input formControlName="rangeBeginIccid" [disabled]="true">
                </nw-input>
              </nw-input-group>
            </nw-form-field>
            <nw-form-field label="{{'尾ICCID' | nwi18n}}"
                           layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" >
              <nw-input-group>
                <nw-select nw-input-group-prepend formControlName="cardType"
                           [selectConfig]="dictTools.simpleSelectConfig"
                           [selectData]="formService.cardTypeList" [disabled]="true"
                           [style]="formService.cardTypeStyle">
                </nw-select>
                <nw-input formControlName="rangeEndIccid"  [disabled]="true">
                </nw-input>
              </nw-input-group>
            </nw-form-field>
<!--            <app-policy-range [form]="formService.policyForm" [disabled]="true"></app-policy-range>-->
          </ng-container>

          <ng-container *ngIf="formService.policyId === PolicyEnum.BLOCK">

          </ng-container>

          <nw-form-field label="{{'RSP+平台'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.rspAppIdList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="rspAppId" (change)="formService.rspAppIdChange($event)"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'RSP+CID前缀'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.rspCidPrefixList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="rspCidPrefix"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'RSP+资源池'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.rspPoolIdList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="rspPoolId"></nw-select>
          </nw-form-field>
          <nw-form-field label="{{'RSP+PinPuk生成模式'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-select [selectData]="formService.rspPinpukModeList" [selectConfig]="dictTools.simpleSelectConfig"
                       formControlName="rspPinpukMode" (change)="formService.rspPinpukModeChange($event)"></nw-select>
          </nw-form-field>
          <ng-container *ngIf="formService.showRspPinpukAttr">
            <nw-form-field label="{{'PIN1'| nwi18n}}" layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" required>
              <nw-input formControlName="pin1"></nw-input>
            </nw-form-field>
            <nw-form-field label="{{'PUK1'| nwi18n}}" layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" required>
              <nw-input formControlName="puk1"></nw-input>
            </nw-form-field>
            <nw-form-field label="{{'PIN2'| nwi18n}}" layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" required>
              <nw-input formControlName="pin2"></nw-input>
            </nw-form-field>
            <nw-form-field label="{{'PUK2'| nwi18n}}" layout="horizontal" labelAlign="right"
                           labelClass="col-lg-3" fieldClass="col-lg-6" required>
              <nw-input formControlName="puk2"></nw-input>
            </nw-form-field>
          </ng-container>
        </div>
      </nw-step>
      <nw-step label="{{'制卡交付'|nwi18n}}" [validatable]="!formService.deliverForm?.valid">
        <div [formGroup]="formService.deliverForm" >
          <nw-form-field label="{{'卡厂交付日期'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-datetime formControlName="factoryDeliverDate" [config]="formService.dateConfig"></nw-datetime>
          </nw-form-field>
          <nw-form-field label="{{'客户交付日期'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6" required>
            <nw-datetime formControlName="custDeliverDate" [config]="formService.dateConfig"></nw-datetime>
          </nw-form-field>
          <nw-form-field label="{{'备注'| nwi18n}}" layout="horizontal" labelAlign="right"
                         labelClass="col-lg-3" fieldClass="col-lg-6">
            <nw-textarea formControlName="deliverRemark"></nw-textarea>
          </nw-form-field>
        </div>
      </nw-step>
    </nw-stepper>
  </nw-panel-content>
</nw-panel>
