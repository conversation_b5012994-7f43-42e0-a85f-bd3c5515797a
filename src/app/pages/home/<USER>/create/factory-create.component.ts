import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService,} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {FactoryFormService} from '../factory-form.service';

@Component({
  selector: 'app-factory-create',
  templateUrl: './factory-create.component.html',
  styleUrls: ['./factory-create.component.scss']
})
export class FactoryCreateComponent implements OnInit {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools) {
    this.formService = new FactoryFormService(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();

  formService: FactoryFormService;

  ngOnInit(): void {

  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.VENDOR_CREATE, this.closeReturn);
  }

}
