<nw-panel>
  <nw-panel-header label="{{'i18n_public.create.button'| nwi18n}}" [icons]="['close']" (iconClick)="closePanel()">
  </nw-panel-header>
  <nw-panel-content>
    <div [formGroup]="formService.form">
      <nw-form-field label="{{'i18n_factory.form.name'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-select [selectConfig]="formService.homeService.buildFactorySelectConfig()" formControlName="factoryId"></nw-select>
      </nw-form-field>
      <nw-form-field label="{{'i18n_email.form.operation'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-select [selectConfig]="dictTools.simpleSelectConfig" [selectData]="formService.operationList" (change)="formService.operationChange($event)" formControlName="operation"></nw-select>
      </nw-form-field>
      <nw-form-field label="{{'i18n_email.form.send.to'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-input  formControlName="sendTo"></nw-input>
      </nw-form-field>

      <nw-form-field label="{{'i18n_email.form.cc.to'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6">
        <nw-input-multiple formControlName="ccTo"></nw-input-multiple>
      </nw-form-field>

      <nw-form-field label="{{'i18n_email.form.send.by'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-input  formControlName="sendBy"></nw-input>
      </nw-form-field>
      <nw-form-field label="{{'i18n_email.form.subject'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required>
        <nw-input  formControlName="subject">"></nw-input>
      </nw-form-field>

      <nw-form-field label="{{'i18n_email.form.content'| nwi18n}}" layout="horizontal" labelAlign="right"
                     labelClass="col-lg-3" fieldClass="col-lg-6" required *ngIf="!!formService.emailTpl">
        <div [innerHTML]="formService.emailTpl" [style]="formService.emailTplStyle"></div>
      </nw-form-field>

    </div>
  </nw-panel-content>
  <nw-panel-footer style="text-align: center;">
    <button  icon="far fa-check" nw-button [color]="'primary'" [disabled]="!formService.checkParams()"
             (click)="submit()">{{'i18n_public.submit.button'|nwi18n}}</button>&nbsp;
    <button nw-button color="warning" icon="fa-times" (click)="cancel()">{{'i18n_public.cancel.button' |nwi18n}}</button>
  </nw-panel-footer>
</nw-panel>
