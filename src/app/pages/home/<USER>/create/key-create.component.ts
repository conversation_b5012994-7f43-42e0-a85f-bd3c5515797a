import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService,} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {KeyFormService} from '../key-form.service';

@Component({
  selector: 'app-key-create',
  templateUrl: './key-create.component.html',
  styleUrls: ['./key-create.component.scss']
})
export class KeyCreateComponent implements OnInit {


  formService!: KeyFormService;

  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools) {
    this.formService = new KeyFormService(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();

  ngOnInit(): void {
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.KEY_CREATE, this.closeReturn);
  }

}
