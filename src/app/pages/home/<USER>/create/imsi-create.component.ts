import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {FormBuilder, Validators} from '@angular/forms';
import {NwDialogService} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {HttpClient} from '../../../../services/http.client';
import {ApiConstant} from '../../../global/conts/api.constant';
import {RegularConstant} from '../../../global/conts/regular.constant';

@Component({
  selector: 'app-imsi-create',
  template: `
    <nw-panel>
      <nw-panel-header label="{{'新增IMSI'| nwi18n}}">
      </nw-panel-header>
      <nw-panel-content>
        <div class="row" [formGroup]="formBuilder">
          <nw-form-field label="{{'IMSI'| nwi18n}}" class="col-lg-6" [required]="true">
            <nw-input formControlName="imsi"
                      [placeholder]="nwI18nService.getI18nValue('请输入IMSI')"
                      [maxlength]="15">
            </nw-input>
          </nw-form-field>

          <nw-form-field label="{{'MSISDN'| nwi18n}}" class="col-lg-6" [required]="true">
            <nw-input formControlName="msisdn"
                      [placeholder]="nwI18nService.getI18nValue('请输入MSISDN')"
                      [maxlength]="15">
            </nw-input>
          </nw-form-field>

          <nw-form-field label="{{'供应商ID'| nwi18n}}" class="col-lg-6" [required]="true">
            <nw-input formControlName="vendorId"
                      [placeholder]="nwI18nService.getI18nValue('请输入供应商ID')">
            </nw-input>
          </nw-form-field>

          <nw-form-field label="{{'重用次数'| nwi18n}}" class="col-lg-6">
            <nw-input formControlName="reuseCount"
                      [placeholder]="nwI18nService.getI18nValue('请输入重用次数')">
            </nw-input>
          </nw-form-field>

          <nw-form-field label="{{'服务开始日期'| nwi18n}}" class="col-lg-6">
            <nw-datetime formControlName="serviceStartDate"
                         >
            </nw-datetime>
          </nw-form-field>

          <nw-form-field label="{{'服务结束日期'| nwi18n}}" class="col-lg-6">
            <nw-datetime formControlName="serviceEndDate"
                         >
            </nw-datetime>
          </nw-form-field>
        </div>
      </nw-panel-content>
      <nw-panel-footer>
        <button nw-button color="primary" [disabled]="!formBuilder.valid" (click)="save()">{{'保存'|nwi18n}}</button>&nbsp;
        <button nw-button color="secondary" (click)="cancel()">{{'取消'|nwi18n}}</button>
      </nw-panel-footer>
    </nw-panel>
  `,
  styleUrls: ['./imsi-create.component.scss']
})
export class ImsiCreateComponent implements OnInit {

  @Output() closeReturn = new EventEmitter<boolean>();

  formBuilder = this.fb.group({
    imsi: ['', [Validators.required, Validators.pattern(RegularConstant.IMSI)]],
    msisdn: ['', [Validators.required, Validators.pattern(RegularConstant.MSISDN)]],
    vendorId: ['', [Validators.required]],
    reuseCount: [0, [Validators.min(0)]],
    serviceStartDate: [''],
    serviceEndDate: ['']
  });

  constructor(
    private fb: FormBuilder,
    private apiService: HttpClient,
    private nwDialogService: NwDialogService,
    public nwI18nService: NwI18nService
  ) { }

  ngOnInit(): void {
  }

  save(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      const params = this.formBuilder.value;
      this.apiService.post(ApiConstant.IMSI_RENEW, params).subscribe((res: any) => {
        this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
          this.closeReturn.emit(true);
        });
      }, (error) => {
        this.nwDialogService.error(error?.apiResponse?.message);
      });
    }
  }

  cancel(): void {
    this.closeReturn.emit(false);
  }
}
