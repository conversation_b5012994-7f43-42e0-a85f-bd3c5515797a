import {Component, Inject, OnInit, Optional} from '@angular/core';
import {NW_MODAL_DATA, NwDialogService, NwInputFileHook, NwModalRef} from '@ng-nwui/components';
import {FormBuilder} from '@angular/forms';
import {NwI18nService} from '@ng-nwui/core';
import { HttpClient } from 'src/app/services/http.client';
import { DictTools } from 'src/app/pages/global/dict.tools';
import { FormService } from '../../form.service';
import {ApiConstant, ConfigConstant} from '../../../global/conts';
import {FunCodeGlobalConstant} from '../../../global/conts/fun-code-global.constant';

@Component({
  selector: 'app-order-audit-first-card-request-modal',
  templateUrl: 'order-audit-first-card-modal.component.html',
  styleUrls: ['./order-audit-first-card-modal.component.scss']
})
export class OrderAuditFirstCardModalComponent extends FormService implements OnInit {

  fileConfig!: any;
  hook: NwInputFileHook = {
    handleBeforeUpload: (host, file) => {
      return file.text().then(() => {
        host.params = {opCode: FunCodeGlobalConstant.ORDER_AUDIT_FIRST_CARD_REQUEST};
      });
    }
  };

  showApduFileId = false;
  apduFileId!: any;

  constructor(@Optional() @Inject(NW_MODAL_DATA) private _data: any,
              protected fb: FormBuilder,
              protected nwDialogService: NwDialogService,
              protected i18nService: NwI18nService,
              protected apiService: HttpClient,
              private modalRef: NwModalRef,
              public dictTools: DictTools,
  ) {
    super(fb, nwDialogService, apiService, i18nService, dictTools);
    this.fileConfig = ConfigConstant.getFileConfigBySuffix('*');
    this.setForm({orderId: this._data?.orderId});
  }

  ngOnInit(): void {
    this.getOrderFirstCardDetail();
  }


  closeModal(value: any = null): void {
    this.modalRef?.close(value);
  }

  protected initForm(): void {
    this.form = this.fb.group({
      orderId: [null, [this.homeService.requiredValidator]],
      firstcardId: [null, [this.homeService.requiredValidator]],
      checkFileId1: [null, [this.homeService.requiredValidator]],
      checkFileId2: [null],
      checkFileId3: [null],
      logFileId: [null],
      apduFileId: [null],
    });
  }

  protected getDictList(): void {

  }

  getOrderFirstCardDetail(): any {
    this.apiService.post(`${ApiConstant.ORDER_ORDER_FIRST_CARD_DETAIL}/${this._data?.orderId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
        this.setForm(resp);
    });
  }

  doSubmit(): void {
    this.submit(ApiConstant.ORDER_AUDIT_FIRST_CARD, this.modalRef);
  }

  download(fileId: any): void {
    this.apiService.download(ApiConstant.FILE_DOWNLOAD_V2, {fileId}).subscribe((res) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_public.success'));
    });
  }
}
