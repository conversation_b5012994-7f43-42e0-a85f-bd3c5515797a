import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {FormBuilder, Validators} from '@angular/forms';
import {NwDialogService, NwDatetimeConfig} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {HttpClient} from '../../../../services/http.client';
import {DateUtils} from '../../../global/utils';
import {ApiConstant, RegularConstant} from "../../../global/conts";

@Component({
  selector: 'app-imsi-renew',
  templateUrl: './imsi-renew.component.html',
  styleUrls: ['./imsi-renew.component.scss']
})
export class ImsiRenewComponent implements OnInit {

  @Output() closeReturn = new EventEmitter<boolean>();

  // 时间配置
  locale: any = this.nwI18nService.getCurrentLocale() as string;
  dateConfig: NwDatetimeConfig = {
    format: DateUtils.getDateFormatter(this.locale),
  };

  formBuilder = this.fb.group({
    startIccid: ['', [Validators.required, Validators.pattern(RegularConstant.ICCID)]],
    endIccid: ['', [Validators.required, Validators.pattern(RegularConstant.ICCID)]],
    renewalDate: ['', [Validators.required]],
    remark: ['']
  });
  constructor(
    private fb: FormBuilder,
    private apiService: HttpClient,
    private nwDialogService: NwDialogService,
    public nwI18nService: NwI18nService
  ) { }

  ngOnInit(): void {
    // 设置默认续期时间为当前时间
    this.formBuilder.patchValue({
      renewalDate: new Date()
    });
  }

  save(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      // 验证ICCID范围
      if (!this.validateIccidRange()) {
        return;
      }

      const params = this.buildRenewParams();
      this.apiService.post(ApiConstant.IMSI_RENEW, params).subscribe((res: any) => {
        this.nwDialogService.success(this.nwI18nService.getI18nValue('续期成功'), () => {
          this.closeReturn.emit(true);
        });
      }, (error) => {
        this.nwDialogService.error(error?.apiResponse?.message || '续期失败');
      });
    }
  }

  cancel(): void {
    this.closeReturn.emit(false);
  }

  // 验证ICCID范围
  private validateIccidRange(): boolean {
    const startIccid = this.formBuilder.value.startIccid;
    const endIccid = this.formBuilder.value.endIccid;

    if (startIccid && endIccid) {
      // 简单的数值比较验证
      const startNum = parseInt(startIccid.substring(startIccid.length - 10));
      const endNum = parseInt(endIccid.substring(endIccid.length - 10));

      if (startNum >= endNum) {
        this.nwDialogService.error('结束ICCID必须大于开始ICCID');
        return false;
      }

      // 检查范围是否过大
      if (endNum - startNum > 10000) {
        this.nwDialogService.confirm('ICCID范围较大，可能影响系统性能，是否继续？', () => {
          return true;
        }, () => {
          return false;
        });
      }
    }
    return true;
  }

  // 构建续期参数
  private buildRenewParams(): any {
    const formValue = this.formBuilder.value;
    console.log(formValue.renewalDate);
    return {
      startIccid: formValue.startIccid,
      endIccid: formValue.endIccid,
      newServiceEndDate:DateUtils.formatDateToCompactThenAppendEndTimeOfDay(formValue.renewalDate, this.locale), // DateUtils.formatDateToCompact(formValue.renewalDate, this.locale),
      remark: formValue.remark
    };
  }

  // 自动填充结束ICCID
  onStartIccidChange(): void {
    const startIccid = this.formBuilder.value.startIccid;
    if (startIccid && startIccid.length >= 15) {
      // 如果结束ICCID为空，自动设置为开始ICCID + 1
      if (!this.formBuilder.value.endIccid) {
        const prefix = startIccid.substring(0, startIccid.length - 5);
        const suffix = startIccid.substring(startIccid.length - 5);
        const nextSuffix = (parseInt(suffix) + 1).toString().padStart(5, '0');
        this.formBuilder.patchValue({
          endIccid: prefix + nextSuffix
        });
      }
    }
  }

}
