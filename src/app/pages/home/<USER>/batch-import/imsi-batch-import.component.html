<nw-panel>
  <nw-panel-header label="{{'IMSI批量导入'| nwi18n}}">
  </nw-panel-header>
  <nw-panel-content>
    <div  [formGroup]="formBuilder">


        <nw-form-field label="{{'供应商ID'| nwi18n}}" labelAlign="right" layout="horizontal"
                       labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
          <nw-select formControlName="vendorId"
                     [selectConfig]="selectConfig"
                     [selectData]="vendorList"
                     [placeholder]="nwI18nService.getI18nValue('请选择供应商')">
          </nw-select>
        </nw-form-field>

        <nw-form-field label="{{'漫游数据开通'| nwi18n}}" labelAlign="right" layout="horizontal"
                       labelClass="col-lg-3" fieldClass="col-lg-6">
          <nw-switch formControlName="roamDataEnabled">
          </nw-switch>
          <div class="form-text text-muted">
            {{'是否开通漫游数据服务'| nwi18n}}
          </div>
        </nw-form-field>

        <nw-form-field label="{{'服务结束日期'| nwi18n}}" labelAlign="right" layout="horizontal"
                       labelClass="col-lg-3" fieldClass="col-lg-6"[required]="true">
          <nw-datetime formControlName="serviceEndDate"
                      >
          </nw-datetime>
        </nw-form-field>

        <nw-form-field label="{{'状态'| nwi18n}}" labelAlign="right" layout="horizontal"
                       labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
          <nw-select formControlName="status"
                     [selectConfig]="selectConfig"
                     [selectData]="statusList"
                     [placeholder]="nwI18nService.getI18nValue('请选择状态')">
          </nw-select>
        </nw-form-field>

        <nw-form-field label="{{'服务开通区域'| nwi18n}}" labelAlign="right" layout="horizontal"
                       labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
          <nw-select
                     [selectConfig]="selectConfig"
                     [selectData]="areaList"
                     [placeholder]="nwI18nService.getI18nValue('请选择服务开通区域')"
                     (change)="onAreaChange($event)">
          </nw-select>
          <div class="form-text text-muted">
            {{'可选择多个区域，系统会自动关联对应的MCC代码'| nwi18n}}
          </div>
        </nw-form-field>
      <nw-form-field label="{{'服务开通MCC'| nwi18n}}" labelAlign="right" layout="horizontal"
                     labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
        <nw-select formControlName="roamArea"
                   [selectConfig]="multiSelectConfig"
                   [selectData]="mccList"
                   [placeholder]="nwI18nService.getI18nValue('请选择服务开通区域（可多选）')"
                   (change)="onMccChange($event)"
                   >
        </nw-select>
        <div class="form-text text-muted">
          {{'可选择多个MCC代码'| nwi18n}}
        </div>
      </nw-form-field>

      <nw-form-field label="{{'服务开通区域1'| nwi18n}}" labelAlign="right" layout="horizontal"
                     labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
        <nw-select formControlName="roamArea"
                   [selectConfig]="multiSelectConfig"
                   [selectData]="mccAllList"
                   [placeholder]="nwI18nService.getI18nValue('请选择服务开通区域（可多选）')"

        ></nw-select>
      </nw-form-field>
      <nw-form-field label="{{'服务开通区域2'| nwi18n}}" labelAlign="right" layout="horizontal"
                     labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
      <nw-input-multiple clearable formControlName="roamArea"></nw-input-multiple>
      </nw-form-field>
      <div class="row" *ngIf="mccList.length > 0">
        <div class="col-lg-12">
          <div class="mcc-list-container">
            <h5>{{'关联的MCC代码'| nwi18n}}</h5>
            <div class="mcc-tags">
              <nw-label *ngFor="let mcc of mccList"
                        color="info"
                        class="mcc-tag">
                {{mcc.text}} ({{mcc.id}})
              </nw-label>
            </div>
          </div>
        </div>
      </div>
        <nw-form-field label="{{'导入文件'| nwi18n}}" labelAlign="right" layout="horizontal"
                       labelClass="col-lg-3" fieldClass="col-lg-6" [required]="true">
          <nw-input-file #nwInputFile
                         [config]="fileConfig"
                         (selectFile)="onSelectFile($event)">
          </nw-input-file>
          <div class="form-text" [ngClass]="{'text-success': isFileUploaded(), 'text-muted': !isFileUploaded()}">
            {{getFileStatusText()}}
          </div>
        </nw-form-field>
    </div>
  </nw-panel-content>
  <nw-panel-footer>
    <div class="row">
      <div class="col-lg-3"></div>
      <div class="col-lg-9">
        <button nw-button color="primary" icon="fa-check" (click)="save()" [disabled]="!formBuilder.valid">
          {{'i18n_public.submit.button' | nwi18n}}
        </button>&nbsp;
        <button nw-button color="warning" icon="fa-times"
                (click)="cancel()">{{'i18n_public.cancel.button'|nwi18n}}</button>
      </div>
    </div>


  </nw-panel-footer>
</nw-panel>
