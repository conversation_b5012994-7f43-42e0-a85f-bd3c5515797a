import {Component, EventEmitter, OnInit, Output, ViewChild} from '@angular/core';
import {<PERSON><PERSON><PERSON>er, Validators} from '@angular/forms';
import {NwDialogService, NwInputFile, NwInputFileConfig, NwSelectConfig} from '@ng-nwui/components';
import {NwFormUtils, NwI18nService} from '@ng-nwui/core';
import {HttpClient} from '../../../../services/http.client';
import {ApiConstant} from '../../../global/conts/api.constant';
import {ConfigConstant} from '../../../global/conts/config.constant';
import {DictTools} from '../../../global/dict.tools';
import {DictCode} from '../../../global/enums/dict-code.enum';

@Component({
  selector: 'app-imsi-batch-import',
  templateUrl: './imsi-batch-import.component.html',
  styleUrls: ['./imsi-batch-import.component.scss']
})
export class ImsiBatchImportComponent implements OnInit {

  @Output() closeReturn = new EventEmitter<boolean>();
  @ViewChild('nwInputFile') nwInputFile?: NwInputFile;

  // 文件上传配置
  fileConfig: NwInputFileConfig = {
    url: ApiConstant.FILE_UPLOAD,
    chunk: false,
    fileType: ['.xlsx', '.xls', '.csv'],
    maxSize: 10 * 1024, // 10MB
    dataAdapter: {
      code(resp: any): string {
        return resp.code;
      },
      message(resp: any): string {
        return resp.message;
      },
      data(resp: any): any {
        return resp.data;
      },
      success(resp: any): boolean {
        return resp.code === '000';
      }
    }
  };

  formBuilder = this.fb.group({
    vendorId: ['', [Validators.required]],
    roamDataEnabled: [false],
    serviceEndDate: ['', [Validators.required]],
    status: ['', [Validators.required]],
    roamArea: [[], [Validators.required]],
    fileId: ['', [Validators.required]]
  });

  // 选择器配置
  selectConfig: NwSelectConfig = {clearable: false,search:false};
  multiSelectConfig: NwSelectConfig = {
    selection: 'multiple',
    clearable: true,
    search: true
  };

  // 数据选项
  vendorList: any[] = [];
  statusList: any[] = [];
  areaList: any[] = [];
  mccList: any[] = [];
  mccAllList: any[] = [];
  selectedAreaList: any[] = [];

  // 上传的文件信息
  uploadedFile: any = null;

  constructor(
    private fb: FormBuilder,
    private apiService: HttpClient,
    private nwDialogService: NwDialogService,
    public nwI18nService: NwI18nService,
    private dictTools: DictTools
  ) { }

  ngOnInit(): void {
    this.loadDictData();
    this.loadMccList();
  }

  // 加载字典数据
  loadDictData(): void {
    // 加载供应商列表
    this.dictTools.getDictList(DictCode.IMSI_VENDOR).subscribe((resp: any) => {
      this.vendorList = resp;
    });

    // 加载状态列表
    this.dictTools.getDictList(DictCode.IMSI_STATUS).subscribe((resp: any) => {
      this.statusList = resp;
    });

    // 加载区域列表
    this.dictTools.getDictList(DictCode.SY_CODE_AREA).subscribe((resp: any) => {
      this.areaList = resp;
    });
  }

  loadMccList(): void {
    this.apiService.post(ApiConstant.SY_CODE_MCC_LIST, {}, ConfigConstant.noLoadingConfig())
      .subscribe((resp: any) => {
        this.mccAllList = resp.map((item: any) => {
          return {
            id: item.mccCode,
            text: item.nameUs
          };
        });
      }, (error) => {
        this.nwDialogService.error('加载MCC列表失败：' + (error?.apiResponse?.message || '未知错误'));
      });
  }
  // 区域选择变化时加载对应的MCC列表
  onAreaChange(areaCode: any): void {
    if(!areaCode)return;
    const params = {
      areaCode: areaCode
    };

    this.apiService.post(ApiConstant.SY_CODE_MCC_LIST_BY_AREA, params, ConfigConstant.noLoadingConfig())
      .subscribe((resp: any) => {
        this.mccList = resp.map((item: any) => {
          return {
            id: item.mccCode,
            text: item.nameUs
          };
        });
      }, (error) => {
        this.nwDialogService.error('加载MCC列表失败：' + (error?.apiResponse?.message || '未知错误'));
      });
  }

  // 根据区域ID加载MCC列表
  onMccChange(areaCode: string[]): void {
    if(!areaCode||areaCode.length==0)return;
 
    console.log(areaCode);

    var roamArea=this.formBuilder.value.roamArea ;
    this.formBuilder.patchValue({
      roamArea: roamArea.concat(areaCode)
    });

  }

  // 文件选择回调
  onSelectFile(fileData: any): void {
    if (fileData && fileData.length > 0) {
      this.uploadedFile = fileData[0];
      this.formBuilder.patchValue({
        fileId: this.uploadedFile.id
      });
    } else {
      this.uploadedFile = null;
      this.formBuilder.patchValue({
        fileId: ''
      });
    }
  }

  // 下载模板
  downloadTemplate(): void {
    const templateUrl = `${ApiConstant.IMSI_BATCH_IMPORT}/template`;
    this.apiService.download(templateUrl, {}).subscribe((res: any) => {
      const blob = new Blob([res], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `IMSI批量导入模板_${new Date().getTime()}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
    }, (error) => {
      this.nwDialogService.error('下载模板失败：' + (error?.apiResponse?.message || '未知错误'));
    });
  }

  // 执行批量导入
  save(): void {
    if (NwFormUtils.validate(this.formBuilder)) {
      const params = this.buildImportParams();

      this.nwDialogService.confirm(
        '确认执行批量导入操作？导入过程可能需要一些时间，请耐心等待。',
        () => {
          this.apiService.post(ApiConstant.IMSI_BATCH_IMPORT, params).subscribe((res: any) => {
            this.nwDialogService.success(
              `批量导入任务已提交成功！\n成功导入：${res.data?.successCount || 0} 条\n失败：${res.data?.failCount || 0} 条`,
              () => {
                this.closeReturn.emit(true);
              }
            );
          }, (error) => {
            this.nwDialogService.error('批量导入失败：' + (error?.apiResponse?.message || '未知错误'));
          });
        }
      );
    }
  }

  // 构建导入参数
  private buildImportParams(): any {
    const formValue = this.formBuilder.value;
    return {
      vendorId: formValue.vendorId,
      roamDataEnabled: formValue.roamDataEnabled,
      serviceEndDate: formValue.serviceEndDate,
      status: formValue.status,
      roamArea: formValue.roamArea?.map((area: any) => area.id) || [],
      fileId: formValue.fileId
    };
  }

  cancel(): void {
    this.closeReturn.emit(false);
  }

  // 验证文件是否已上传
  isFileUploaded(): boolean {
    return this.uploadedFile && this.uploadedFile.id;
  }

  // 获取文件上传状态文本
  getFileStatusText(): string {
    if (!this.uploadedFile) {
      return '请选择要导入的文件';
    }
    return `已选择文件：${this.uploadedFile.name}`;
  }
}
