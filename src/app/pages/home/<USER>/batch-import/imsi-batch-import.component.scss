/* IMSI批量导入组件样式 */
.batch-import-form {
  padding: 20px;
}

.section-title {
  margin-bottom: 20px;
  margin-top: 30px;

  &:first-child {
    margin-top: 0;
  }

  h4 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
    border-left: 4px solid #3498db;
    padding-left: 12px;
  }

  h5 {
    color: #34495e;
    font-weight: 500;
    margin-bottom: 12px;
  }

  p {
    margin-bottom: 0;
    font-size: 14px;
    color: #7f8c8d;
  }
}

.form-text {
  font-size: 12px;
  margin-top: 4px;

  &.text-muted {
    color: #6c757d;
  }

  &.text-success {
    color: #28a745;
    font-weight: 500;
  }
}

/* MCC标签容器 */
.mcc-list-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin-top: 10px;

  h5 {
    margin-bottom: 12px;
    color: #495057;
  }
}

.mcc-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .mcc-tag {
    margin: 0;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

/* 警告和信息框样式 */
.alert {
  border-radius: 6px;
  border: none;

  &.alert-info {
    background-color: #e3f2fd;
    color: #1565c0;
    border-left: 4px solid #2196f3;

    i {
      color: #2196f3;
      margin-right: 8px;
    }
  }

  &.alert-warning {
    background-color: #fff8e1;
    color: #e65100;
    border-left: 4px solid #ff9800;

    i {
      color: #ff9800;
      margin-right: 8px;
    }
  }

  ul {
    padding-left: 20px;

    li {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/* 表单字段样式 */
nw-form-field {
  margin-bottom: 20px;

  .nw-form-field-label {
    font-weight: 500;
    color: #2c3e50;
  }

  &.required .nw-form-field-label::after {
    content: ' *';
    color: #e74c3c;
  }
}

/* 输入框和选择器样式 */
nw-input, nw-select, nw-datetime, nw-switch, nw-input-file {
  .nw-input, .nw-select, .nw-datetime, .nw-switch, .nw-input-file {
    border-radius: 4px;
    border: 1px solid #ddd;

    &:focus {
      border-color: #3498db;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
  }
}

/* 文件上传区域样式 */
nw-input-file {
  .file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3498db;
      background-color: #f8f9fa;
    }

    &.file-selected {
      border-color: #28a745;
      background-color: #f8fff9;
    }
  }
}

/* 按钮样式 */
.nw-panel-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 15px 20px;

  button {
    min-width: 120px;

    i {
      margin-right: 6px;
    }
  }
}

/* 开关样式 */
nw-switch {
  .nw-switch {
    &.checked {
      background-color: #28a745;
    }
  }
}

/* 多选下拉框样式 */
nw-select[multiple] {
  .nw-select-selection {
    min-height: 40px;

    .nw-select-selection-item {
      background-color: #e3f2fd;
      color: #1565c0;
      border: 1px solid #2196f3;
      border-radius: 4px;
      padding: 2px 8px;
      margin: 2px;
      font-size: 12px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-import-form {
    padding: 15px;
  }

  .section-title {
    margin-top: 20px;
    margin-bottom: 15px;

    h4 {
      font-size: 16px;
    }
  }

  .col-lg-6, .col-lg-8, .col-lg-12 {
    margin-bottom: 15px;
  }

  .mcc-tags {
    .mcc-tag {
      font-size: 11px;
      padding: 3px 6px;
    }
  }

  .alert {
    font-size: 14px;

    ul {
      padding-left: 15px;
    }
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-content {
    text-align: center;

    .spinner {
      margin-bottom: 10px;
    }

    .loading-text {
      color: #6c757d;
      font-size: 14px;
    }
  }
}
