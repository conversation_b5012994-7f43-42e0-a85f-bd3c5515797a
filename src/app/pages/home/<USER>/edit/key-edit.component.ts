import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService, NwSelect,} from '@ng-nwui/components';
import {ApiConstant, ConfigConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {KeyFormService} from '../key-form.service';

@Component({
  selector: 'app-key-edit',
  templateUrl: './key-edit.component.html',
  styleUrls: ['./key-edit.component.scss']
})
export class KeyEditComponent implements OnInit {


  formService!: KeyFormService;


  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools) {
    this.formService = new KeyFormService(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();
  @Input() selectedRow!: any;

  @ViewChild('factoryNwSelect')
  factoryNwSelect!: NwSelect;

  ngOnInit(): void {
    this.getDetail();
  }

  getDetail(): void {
    this.apiService.post(`${ApiConstant.KEY_DETAIL}/${this.selectedRow?.keyId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
        this.formService.setForm(resp);
        this.factoryNwSelect.initialText = resp?.factoryName;
    });
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.KEY_UPDATE, this.closeReturn, {keyId: this.selectedRow?.keyId});
  }

}
