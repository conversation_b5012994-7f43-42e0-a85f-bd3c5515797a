import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {NwDialogService, NwSelect} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {EmailFormService} from '../email-form.service';
import {DomSanitizer} from "@angular/platform-browser";

@Component({
  selector: 'app-email-edit',
  templateUrl: './email-edit.component.html',
  styleUrls: ['./email-edit.component.scss']
})
export class EmailEditComponent implements OnInit {
  constructor(protected apiService: HttpClient, public dictTools: DictTools,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, protected sanitizer: DomSanitizer) {
    this.formService = new EmailFormService(fb, nwDialogService, apiService, nwI18nService, dictTools, sanitizer);
  }

  @Output() closeReturn = new EventEmitter<any>();
  @Input() selectedRow !: any;

  @ViewChild('factoryNwSelect')
  factoryNwSelect!: NwSelect;

  formService: EmailFormService;

  ngOnInit(): void {
    this.getDetail();
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  getDetail(): any {
    this.apiService.post(`${ApiConstant.EMAIL_DETAIL}/${this.selectedRow?.factoryEmailId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.formService.setForm(resp);
      this.factoryNwSelect.initialText = resp.factoryName;
    });
  }

  submit(): any {
    this.formService.submit(ApiConstant.EMAIL_UPDATE, this.closeReturn, {factoryEmailId: this.selectedRow?.factoryEmailId});
  }
}
