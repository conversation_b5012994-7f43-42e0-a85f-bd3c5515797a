<div class="modal-change">
  <nw-modal>
    <nw-modal-header label="{{'i18n_console_jobs.table.adjust.task.time'| nwi18n}}"></nw-modal-header>
    <nw-modal-content>
      <div [formGroup]="editFormBuilder" style="height: 100px">
        <nw-form-field label="{{'i18n_console_jobs.batch.id' | nwi18n}}"
                       labelAlign="right" layout="horizontal" labelClass="col-lg-3" fieldClass="col-lg-7"
                       required="true">
          <nw-input formControlName="batchId" readonly="true"></nw-input>
        </nw-form-field>
        <nw-form-field label="{{'i18n_console_logs.schedule.time' | nwi18n}}"
                       labelAlign="right" layout="horizontal" labelClass="col-lg-3" fieldClass="col-lg-7"
                       required="true">

          <nw-datetime formControlName="editDateArray"
                       [config]="dateConfig">
          </nw-datetime>

        </nw-form-field>
      </div>
    </nw-modal-content>
    <nw-modal-footer>
      <div class="col-lg-9" style="text-align: left">
        <button nw-button color="primary" icon="fa-check"
                (click)="doSubmit()">{{'i18n_public.submit.button' | nwi18n}}</button>&nbsp;
        <button nw-button color="warning" icon="fa-times"
                (click)="closeModal()">{{'i18n_public.cancel.button' | nwi18n}}</button>
      </div>

    </nw-modal-footer>
  </nw-modal>
</div>
