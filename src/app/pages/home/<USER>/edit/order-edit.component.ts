import {AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {NwDialogService, NwModalService, NwSelect, NwStepper,} from '@ng-nwui/components';
import {ApiConstant, ConfigConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {OrderFormService} from '../order-form.service';
import {PolicyEnum} from 'src/app/pages/global/enums/policy.enum';

@Component({
  selector: 'app-order-edit-form',
  templateUrl: './order-edit.component.html',
  styleUrls: ['./order-edit.component.scss']
})
export class OrderEditComponent implements OnInit, OnDestroy {
  constructor(protected apiService: HttpClient,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService, public dictTools: DictTools, private nwModalService: NwModalService) {
    this.formService = new OrderFormService(fb, nwDialogService, apiService, nwI18nService, dictTools, nwModalService);
  }


  @Input() selectedRow: any;
  @Output() closeReturn = new EventEmitter<any>();

  formService: OrderFormService;

  PolicyEnum = PolicyEnum;

  @ViewChild('createNwStepper')
  createNwStepper!: NwStepper;

  @ViewChild('custNwSelect')
  custNwSelect!: NwSelect;

  @ViewChild('factoryNwSelect')
  factoryNwSelect!: NwSelect;

  ngOnInit(): void {
    this.getDetail();
  }

  ngOnDestroy(): void {
    this.formService.destroy();
  }

  getDetail(): any {
    this.apiService.post(`${ApiConstant.ORDER_DETAIL}/${this.selectedRow?.orderId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.formService.setForm(resp);
      // @ts-ignore
      this.custNwSelect.initialText = resp?.custName;
      // @ts-ignore
      this.factoryNwSelect.initialText = resp?.factoryName;
    });
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  submit(): any {
    this.formService.submit(ApiConstant.ORDER_EDIT, this.closeReturn);
  }

}
