import {Component, Inject, OnInit, Optional} from '@angular/core';
import {NW_MODAL_DATA, NwDialogService, NwModalRef} from '@ng-nwui/components';
import {FormBuilder, FormGroup} from '@angular/forms';
import {ApiConstant} from '../../../global/conts/api.constant';
import {NwHttpService, NwI18nService} from '@ng-nwui/core';
import {DateUtils} from '../../../global/utils/DateUtils';

@Component({
  templateUrl: 'job.editTaskTime.html'
})
export class JobEditTaskTimeComponent implements OnInit {
  editFormBuilder!: FormGroup;
  batchId!: '';
  modelParams: any;
  locale: any = this.i18nService.getCurrentLocale() as string;
  myJobFlag: any;
  dateConfig = {
    minDate: new Date(),
    autoApply: false,
    format: this.getDateTimeFormat(this.locale)
  };

  constructor(@Optional() @Inject(NW_MODAL_DATA) private _data: any,
              protected fb: FormBuilder,
              private nwDialogService: NwDialogService,
              protected i18nService: NwI18nService,
              protected apiService: NwHttpService,
              private modalRef: NwModalRef
  ) {
    this.modelParams = _data;
    this.myJobFlag = this.modelParams.myJobFlag;
  }

  ngOnInit(): void {
    this.editFormBuilder = this.fb.group({
      batchId: this.modelParams.batchId,
      editDateArray: ['']
    });
  }

  closeModal(): void {
    this.modalRef?.close(false);
  }


  doSubmit(): void {


    // tslint:disable-next-line:triple-equals
    if (this.editFormBuilder.value.editDateArray == '') {
      this.nwDialogService.info(this.i18nService.getI18nValue('i18n_console_jobs.edit.task.time.info.null.tips'));
      return;
    }

    const param = {
      batchId: this.modelParams.batchId,
      reserveTime: DateUtils.formatDateToCompact(this.editFormBuilder.value.editDateArray + ':59', this.locale)
    };
    let editUrl="";
    // return;
    if (this.myJobFlag == '1') {
      editUrl=ApiConstant.SYS_CONSOLE_MY_JOB_EDITTASKTIME
    }else {
      editUrl=ApiConstant.SYS_CONSOLE_JOB_EDITTASKTIME
    }
    this.nwDialogService.confirm(this.i18nService.getI18nValue('i18n_console_jobs.edit.task.time.tips'), () => {
        this.apiService.post(editUrl, param).subscribe((resp: any) => {
          this.nwDialogService.success(this.i18nService.getI18nValue('i18n_privilege.success'), () => {
            this.editFormBuilder.value.editDateArray = '';
            this.modalRef?.close(true);
          });
        }, (error => {
          this.nwDialogService.error(this.i18nService.getI18nValue('i18n_console_jobs.edit.task.time.failure.tips'), () => {

          });
        }));
    }, () => {

    });

  }
  getDateTimeFormat(lang: string): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return 'yyyy-MM-dd HH:mm:59';
    } else if (lang === 'us' || lang === 'en-US') {
      return 'MM/dd/yyyy HH:mm:59';
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return 'MM/dd/yyyy HH:mm:59';
    }
    return 'yyyy-MM-dd HH:mm:59';
  }
  getNowDate(): string {
    return DateUtils.getI18NTodayCompact(this.i18nService.getCurrentLocale() as string) as string;
  }

}
