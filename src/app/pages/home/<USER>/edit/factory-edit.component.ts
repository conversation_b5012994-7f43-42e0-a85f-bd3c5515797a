import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {NwDialogService} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {FactoryFormService} from '../factory-form.service';

@Component({
  selector: 'app-factory-edit',
  templateUrl: './factory-edit.component.html',
  styleUrls: ['./factory-edit.component.scss']
})
export class FactoryEditComponent implements OnInit {
  constructor(protected apiService: HttpClient, public dictTools: DictTools,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService) {
    this.formService = new FactoryFormService(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();
  @Input() selectedRow !: any;

  formService: FactoryFormService;


  ngOnInit(): void {
    this.getDetail();
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  getDetail(): any {
    this.apiService.post(`${ApiConstant.VENDOR_DETAIL}/${this.selectedRow?.factoryId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe(resp => {
      this.formService.setForm(resp);
    });
  }

  submit(): any {
    this.formService.submit(ApiConstant.VENDOR_UPDATE, this.closeReturn, {factoryId: this.selectedRow?.factoryId});
  }
}
