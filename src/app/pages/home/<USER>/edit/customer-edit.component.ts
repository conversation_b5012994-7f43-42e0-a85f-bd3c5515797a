import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {NwDialogService} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {FactoryFormService} from '../../factory/factory-form.service';

@Component({
  selector: 'app-factory-edit',
  templateUrl: './customer-edit.component.html',
  styleUrls: ['./customer-edit.component.scss']
})
export class CustomerEditComponent implements OnInit {
  constructor(protected apiService: HttpClient, public dictTools: DictTools,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              private nwDialogService: NwDialogService) {
    this.formService = new FactoryFormService(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();
  @Input() selectedRow !: any;

  formService: FactoryFormService;

  ngOnInit(): void {
    this.getDetail();
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  getDetail(): any {
    this.apiService.post(`${ApiConstant.CUSTOMER_DETAIL}/${this.selectedRow?.custId}`, null,
      ConfigConstant.noLoadingConfig()).subscribe(resp => {
      this.formService.setForm(resp);
    });
  }

  submit(): any {
    this.formService.submit(ApiConstant.CUSTOMER_UPDATE, this.closeReturn, {custId: this.selectedRow?.custId});
  }
}
