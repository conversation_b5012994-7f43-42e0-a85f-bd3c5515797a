import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';
import {FormBuilder} from '@angular/forms';
import {HttpClient} from 'src/app/services/http.client';
import {ConfigConstant} from 'src/app/pages/global/conts/config.constant';
import {NwDialogService, NwInputFileHook} from '@ng-nwui/components';
import {ApiConstant} from 'src/app/pages/global/conts';
import {DictTools} from '../../../global/dict.tools';
import {FormService} from '../../form.service';
import {FunCodeGlobalConstant} from '../../../global/conts/fun-code-global.constant';

@Component({
  selector: 'app-order-delivery',
  templateUrl: './order-delivery.component.html',
  styleUrls: ['./order-delivery.component.scss']
})
export class OrderDeliveryComponent extends FormService implements OnInit {
  constructor(protected apiService: HttpClient, public dictTools: DictTools,
              public nwI18nService: NwI18nService, protected fb: FormBuilder,
              protected nwDialogService: NwDialogService) {
    super(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  @Output() closeReturn = new EventEmitter<any>();
  @Input() selectedRow !: any;

  fileConfig = ConfigConstant.getFileConfigBySuffix('*');
  hook: NwInputFileHook = {
    handleBeforeUpload: (host, file) => {
      return file.text().then(() => {
        host.params = {opCode: FunCodeGlobalConstant.ORDER_AUDIT_FIRST_CARD_REQUEST};
      });
    }
  };

  protected getDictList(): void {
  }

  protected initForm(): void {
    this.form = this.fb.group({
      orderId: [null, [this.homeService.requiredValidator]],
      deliveryContactName: [null, [this.homeService.requiredValidator]],
      deliveryContactPhone: [null, [this.homeService.requiredValidator]],
      deliveryNo: [null, [this.homeService.requiredValidator]],
      deliveryFee: [null, [this.homeService.requiredValidator]],
      deliveryFileId1: [null, [this.homeService.requiredValidator]],
      deliveryFileId2: [null],
      deliveryFileId3: [null],
    });
  }



  ngOnInit(): void {
    this.setForm({orderId: this.selectedRow?.orderId});
  }

  cancel(): void {
    this.closePanel();
  }

  closePanel(refresh?: any): void {
    this.closeReturn.emit(refresh);
  }

  doSubmit(): any {
    this.submit(ApiConstant.ORDER_DELIVERY, this.closeReturn);
  }
}
