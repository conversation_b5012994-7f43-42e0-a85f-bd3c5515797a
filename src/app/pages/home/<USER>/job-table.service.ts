import {NwI18nService} from '@ng-nwui/core';
import {DateUtils} from '../../global/utils/DateUtils';
import {NwTableConfig} from '@ng-nwui/components';
import {BatchStatus} from '../../global/enums/batch-status.enum';
import {TableSearchService} from '../../global/table-search.service';
import {HttpClient} from '../../../services/http.client';
import {CacheManagerTools} from '../../global/cache-manager.tools';

export class JobTableService extends TableSearchService {

  constructor(protected i18nService: NwI18nService, protected httpService: HttpClient) {
    super(httpService);
  }

  url: any;
  batchTableConfig: NwTableConfig = {
    selection: 'single',
    customAction: true,
    columnStyle: {headerCellNowrap: true, bodyCellOverflow: 'nowrap-fill'},
    columnConfigs: [
      {
        id: 'tenantName',
        title: this.i18nService.getI18nValue('i18n_privilege.tenant'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        hidden: !CacheManagerTools.isSuperAdm(),
      },
      {
        id: 'jobId',
        title: this.i18nService.getI18nValue('i18n_console_jobs.batch.id')
      },
      {
        id: 'jobCodeName',
        title: this.i18nService.getI18nValue('i18n_console_logs.user.business'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'}
      },
      {
        id: 'jobName',
        title: this.i18nService.getI18nValue('i18n_console_jobs.batch.name'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'}
      },
      {
        id: 'reserveTime',
        title: this.i18nService.getI18nValue('i18n_console_logs.schedule.time'),
        bodyRender: (value: any) => {
          return DateUtils.formatCompactDateTimeToLocale(value, this.i18nService.getCurrentLocale() as string);
        }
      },
      {
        id: 'startTime',
        title: this.i18nService.getI18nValue('i18n_console_logs.start.time'),
        bodyRender: (value: any) => {
          return DateUtils.formatCompactDateTimeToLocale(value, this.i18nService.getCurrentLocale() as string);
        }
      },
      {
        id: 'finishTime',
        title: this.i18nService.getI18nValue('i18n_console_logs.finish.time'),
        bodyRender: (value: any) => {
          if (!value) {
            return '-';
          }
          return DateUtils.formatCompactDateTimeToLocale(value, this.i18nService.getCurrentLocale() as string);
        }
      },
      {
        id: 'status',
        title: this.i18nService.getI18nValue('i18n_privilege.status'),
        bodyType: 'template'
      },
      {
        id: 'results',
        title: this.i18nService.getI18nValue('i18n_console_logs.operation.result'),
        bodyType: 'template'
      },

      // {id: 'staffName', title: this.i18nService.getI18nValue('i18n_console_logs.user.operator'), index: 10}
    ]
  };

  itemTableConfig: NwTableConfig = {
    selection: 'none',
    customAction: true,
    columnConfigs: [
      {
        id: 'batchItemId',
        title: this.i18nService.getI18nValue('i18n_console_logs.batch.item'), index: 1
      },
      {
        id: 'content',
        title: this.i18nService.getI18nValue('i18n_console_logs.content'),
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'},
        index: 2
      },
      {
        id: 'status',
        title: this.i18nService.getI18nValue('i18n_privilege.status'),
        index: 3,
        bodyType: 'template'
      },
      {
        id: 'reason',
        title: this.i18nService.getI18nValue('i18n_console_jobs.error.reason'),
        index: 5,
        style: { headerCellHorizontalAlign: 'left', bodyCellHorizontalAlign: 'left'}
      },
    ]
  };

  translateColumnColor(cell: any): any {
    let color = '';
    switch (cell) {
      case BatchStatus.LOG_PENDING:
        color = 'info';
        break;
      case BatchStatus.LOG_PROCESSING:
        color = 'warning';
        break;
      case BatchStatus.LOG_SUCCESS:
        color = 'primary';
        break;
      case BatchStatus.LOG_CANCELED:
        color = 'success';
        break;
      case BatchStatus.LOG_FAILURE:
        color = 'danger';
        break;
    }
    return color;
  }

  translateDetailColor(cell: any): any {
    let color = '';
    switch (cell) {
      case BatchStatus.DETAIL_PENDING:
        color = 'info';
        break;
      case BatchStatus.DETAIL_WAITING:
        color = 'warning';
        break;
      case BatchStatus.DETAIL_SUCCESS:
        color = 'primary';
        break;
      case BatchStatus.DETAIL_FAILURE:
        color = 'danger';
        break;
    }
    return color;
  }

  getSearchUrl(): string {
    return this.url;
  }
}
