import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {OrderCreateComponent} from './create/order-create.component';
import {OrderMgmtComponent} from './mgmt/order-mgmt.component';

const routes: Routes = [
  {path: 'create', component: OrderCreateComponent},
  {path: 'mgmt', component: OrderMgmtComponent},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class OrderRoutingModule {
}
