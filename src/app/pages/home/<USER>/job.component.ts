import {Component, Input, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {
  NwDatetimeRangeConfig,
  NwDialogService, NwDropDownItem, NwModalService,
  NwScrollAnchorAuto,
  NwSelectData,
  NwTableData,
} from '@ng-nwui/components';
import {
  NwFormUtils,
  NwHttpConfig,
  NwHttpDownloadOptions,
  NwI18nService,
  NwPermissionService,
  NwScrollerService,
  NwValidators
} from '@ng-nwui/core';
import {DateUtils} from '../../global/utils/DateUtils';
import {JobTableService} from './job-table.service';
import {ApiConstant} from '../../global/conts/api.constant';
import {SystemConstant} from '../../global/conts/system.constant';
import {CacheManagerTools} from '../../global/cache-manager.tools';
import {Observable, Subscription} from 'rxjs';
import {ConfigConstant} from '../../global/conts/config.constant';
import {FileSuffixConstant} from '../../global/conts/file-suffix.constant';
import {HttpClient} from '../../../services/http.client';
import {DateUtilsV2} from '../../global/utils/DateUtilsV2';
import {NwModalRef} from '@ng-nwui/components/elements/dialogs/modal.service';
import {JobRouterNavigateService} from "../../../services/job-router-navigate.service";
import {HomeService} from "../home.service";
import {JobEditTaskTimeComponent} from "./edit/job.editTaskTime.component";

@Component({
  selector: 'app-batch-logs',
  templateUrl: './job.component.html',
  styleUrls: ['./job.component.scss']
})
export class JobComponent implements OnInit, OnDestroy {

  constructor(protected nwI18nService: NwI18nService, protected scrollerService: NwScrollerService,
              protected nwDialogService: NwDialogService,
              protected apiService: HttpClient,
              protected nwModalService: NwModalService,
              protected i18nService: NwI18nService,
              protected fb: FormBuilder,
              protected permissionService: NwPermissionService,
              protected jobRouterNavigateService: JobRouterNavigateService,
  ) {
    this.tableService = new JobTableService(i18nService, apiService);
    this.homeService = new HomeService(apiService, i18nService);
    this._subscription = this.jobRouterNavigateService.getParams().subscribe(params => {
      if (params && this._moduleInited) {
        this.doSearchByType();
      }
    });
  }

  private readonly _subscription: Subscription;
  private _moduleInited = false;

  @Input()
  myJobFlag: any;

  @Input()
  MyTenantId: any;
  username: any;
  SystemConstant = SystemConstant;
  locale: any = this.nwI18nService.getCurrentLocale() as string;
  language: any = DateUtils.getDateInputFormatter(this.locale);
  format: any = DateUtils.getDateFormatter(this.locale);
  // @ts-ignore
  dateConfig: NwDatetimeRangeConfig = {format: this.format, maxDate: new Date(), nowButton: false};
  formBuilder!: FormGroup;
  itemFormBuilder!: FormGroup;
  // TODO 预定义数据

  itemTableData!: NwTableData;
  tenantList: any;
  statusList!: NwSelectData[];
  statusItemList!: NwSelectData[];
  searchOptions!: NwSelectData[];
  isSuperAdm = CacheManagerTools.isSuperAdm();
  tenantId: any = CacheManagerTools.getUserTenantId();
  queryBatch = false;
  detailPage = false;
  buttonFalg = true;
  clickDetailFalg = true;
  checkFailure = false;
  currentBatchSelectRow: any;
  batchId: any;
  pageNum = SystemConstant.DEFAULT_PAGE_NUM;
  pageSize = SystemConstant.DEFAULT_PAGE_SIZE;
  itemPageNum = SystemConstant.DEFAULT_PAGE_NUM;
  itemPageSize = SystemConstant.DEFAULT_PAGE_SIZE;
  returnBatchId: any;
  returnBatchName: any;
  returnFileId: any;
  returnFileName: any;
  returnRemark: any;
  returnTotal: any;
  returnSuccess: any;
  returnFailure: any;
  returnWarning: any;
  returnStatus: any;
  returnStatusName: any;
  returnCreator: any;
  returnJobCodeName: any;
  returnJobCode: any;
  returnJobTypeName: any;
  returnStartTime: any;
  returnFinishTime: any;
  returnCreateTime: any;
  returnjobData: any;
  homeService!: HomeService;
  downloadItems: NwDropDownItem[] = [
    {id: 'excel', text: this.i18nService.getI18nValue('i18n_public.download.button.excel')},
    {id: 'txt', text: this.i18nService.getI18nValue('i18n_public.download.button.txt')}];

  @ViewChild(NwScrollAnchorAuto)
  scrollAnchor?: NwScrollAnchorAuto;

  tableService!: JobTableService;

  downloadVr!: boolean;
  downloadJob!: boolean;
  downloadBatchId: any;
  editTaskTimeModalRef!: NwModalRef;

  ngOnInit(): void {
    this.locale = this.nwI18nService.getCurrentLocale() as string;
    this.formBuilder = this.fb.group({
      tenantId: '',
      orgIds: [['']],
      batchId: '',
      batchName: ['',
        [NwValidators.maxLength(50, this.i18nService.getI18nValue('i18n_public.public.from.length_tips', undefined, ['50']))]],
      status: '',
      dateArray: [[DateUtils.getI18NDateBeforeDays(this.locale, 30), DateUtils.getI18NTodayCompact(this.locale)]],
    });

    this.itemFormBuilder = this.fb.group({
      keyword: '',
      itemStatus: ''
    });
    this.format = DateUtils.getDateFormatter(this.locale);
    this.username = this.permissionService.username;
    this.doSearchByType();
    this.selectStatus();
    this.selectItemStatus();
    this.downloadVr = false;
    this._moduleInited = true;
    if (!CacheManagerTools.isSuperAdm()) {
      this.formBuilder.patchValue({
        tenantId: this.tenantId
      });
    } else {
      this.homeService.initTenant();
    }
  }


  doDisable(value: any): void {
    value.forEach((item: any) => {
      if (item.children) {
        item.disabled = true;
        this.doDisable(item.children);
      }
    });

  }

  // 递归方法，用于展开树形下拉框的所有子节点
  recursion(value: any): void {
    if (value && value.children?.length > 0) {
      value.expanded = true;
      value.children.forEach((childValue: any) => {
        this.recursion(childValue);
      });
    }
  }

  selectStatus(): void {
    const config = new NwHttpConfig();
    config.loading = false;
    this.apiService.post(ApiConstant.DICT_GET, {code: 'JOB_STATUS'}, config).subscribe((resp: any) => {
      this.statusList = resp;
    });
  }

  selectItemStatus(): void {
    const config = new NwHttpConfig();
    config.loading = false;
    this.apiService.post(ApiConstant.DICT_GET, {code: 'JOB_ITEM_STATUS'}, config).subscribe((resp: any) => {
      this.statusItemList = resp;
    });
  }

  onChangePage(pageNum: any): void {
    this.pageNum = pageNum;
    this.tableService.onChangePage(pageNum);
    this.clickDetailFalg = true;
    this.buttonFalg = true;
    this.queryBatch = true;
    this.closeDetailPage();
    this.pageNum = SystemConstant.DEFAULT_PAGE_NUM;
    this.pageSize = SystemConstant.DEFAULT_PAGE_SIZE;

  }

  onChangePageSize(pageSize: any): void {
    this.pageSize = pageSize;
    this.tableService.onChangePageSize(this.pageSize);
    this.clickDetailFalg = true;
    this.buttonFalg = true;
    this.queryBatch = true;
    this.closeDetailPage();
    this.pageNum = SystemConstant.DEFAULT_PAGE_NUM;
    this.pageSize = SystemConstant.DEFAULT_PAGE_SIZE;
  }

  changeDownloadVr(row: any): void {
    this.apiService.post(ApiConstant.SYS_CONSOLE_JOB_CHECK_ENCRYPT,
      {jobId: row.currentSelected.jobId}, ConfigConstant.noLoadingConfig()).subscribe((resp: any) => {
      this.downloadVr = false;
      // tslint:disable-next-line:triple-equals
      this.downloadJob=resp.download;
      if (row.currentSelected.status == '2' && resp.download) {
        this.downloadVr = true;
      }
    });

  }

  closeDetailPage(): void {
    this.detailPage = false;
    this.itemFormBuilder.patchValue({
      keyword: '',
      itemStatus: ''
    });
  }


  batchSelectRow(row: any): void {
    this.tableService.onSelectRow(row);
    if (this.itemTableData) {
      this.itemTableData.data = [];
    }
    this.downloadBatchId = row.currentSelected.jobId;
    this.changeDownloadVr(row);
    this.clickDetailFalg = false;
    this.resetReturn();
    this.currentBatchSelectRow = row.currentSelected;
    this.batchId = this.currentBatchSelectRow.jobId;

    this.closeDetailPage();
    // tslint:disable-next-line:triple-equals
    if (this.currentBatchSelectRow.status == 0) {
      this.buttonFalg = false;
    } else {
      this.buttonFalg = true;
    }
  }

  resetReturn(): void {
    this.returnBatchId = '';
    this.returnBatchName = '';
    this.returnFileId = '';
    this.returnFileName = '';
    this.returnRemark = '';
    this.returnTotal = '';
    this.returnSuccess = '';
    this.returnFailure = '';
    this.returnWarning = '';
    this.returnStatus = '';
    this.returnStatusName = '';
    this.returnCreator = '';
    this.returnJobCodeName = '';
    this.returnJobCode = '';
    this.returnJobTypeName = '';
    this.returnStartTime = '';
    this.returnCreateTime = '';
    this.returnFinishTime = '';
    this.returnjobData = '';
  }

  goItemDetail(): void {
    this.detailPage = true;
    this.searchItemDetail(this.currentBatchSelectRow.jobId);
    const params = {
      batchId: this.currentBatchSelectRow.jobId
    };
    if(!this.downloadJob){
      this.searchItem(params, false, SystemConstant.DEFAULT_PAGE_NUM, SystemConstant.DEFAULT_PAGE_SIZE);
    }
  }

  onQuery(): void {
    this.downloadVr = false;
    this.clickDetailFalg = true;
    this.buttonFalg = true;
    this.queryBatch = true;
    this.closeDetailPage();
    this.doSearch(this.pageNum, this.pageSize);
  }

  doSearchByType(): void {
    if (!NwFormUtils.validate(this.formBuilder)) {
      return;
    }
    this.MyTenantId = this.formBuilder.value.tenantId;
    this.onQuery();
  }

  doSearch(pageNum: number, pageSize: number): void {
    const params = {
      tenantId: this.MyTenantId,
      batchId: this.formBuilder.value.batchId,
      batchName: this.formBuilder.value.batchName,
      status: this.formBuilder.value.status,
      business: this.formBuilder.value.business,
      dateArray: this.formBuilder.value.dateArray,
      startDate: DateUtils.formatDateToCompact(this.formBuilder.value.dateArray[0], this.locale),
      endDate: DateUtils.formatDateToCompact(this.formBuilder.value.dateArray[1], this.locale),
      offset: (pageNum - 1) * pageSize,
      limit: pageSize
    };
    if (this.myJobFlag == '1') {
      this.tableService.url = ApiConstant.SYS_CONSOLE_MY_JOB_LIST;
    } else {
      this.tableService.url = ApiConstant.SYS_CONSOLE_JOB_LIST;
    }
    this.tableService.onSearch(params);
    if (this.scrollAnchor) {
      this.scrollerService.scrollTo(this.scrollAnchor);
    }
  }

  resetItem(): void {
    this.itemFormBuilder.patchValue({
      keyword: '',
      itemStatus: ''
    });
    this.doSearchItem();
  }

  onItemChangePage(page: number): void {
    this.itemPageNum = page;
    const params = {
      batchId: this.batchId,
      keyword: this.itemFormBuilder.value.keyword,
      itemStatus: this.itemFormBuilder.value.itemStatus,
    };
    this.searchItem(params, false, this.itemPageNum, this.itemPageSize);
    // this.itemPageSize = SystemConstant.DEFAULT_PAGE_SIZE;

  }

  onItemChangePagSize(pageSize: any): void {
    this.itemPageSize = pageSize;
    const params = {
      batchId: this.batchId,
      keyword: this.itemFormBuilder.value.keyword,
      itemStatus: this.itemFormBuilder.value.itemStatus,
    };
    this.searchItem(params, false, SystemConstant.DEFAULT_PAGE_NUM, this.itemPageSize);
    // this.itemPageNum = SystemConstant.DEFAULT_PAGE_SIZE;

  }

  doSearchItem(): void {
    const httpConfig = new NwHttpConfig();
    httpConfig.loading = true;
    const params = {
      batchId: this.batchId,
      keyword: this.itemFormBuilder.value.keyword,
      itemStatus: this.itemFormBuilder.value.itemStatus,
      onlyFail: false,
      offset: (SystemConstant.DEFAULT_PAGE_NUM - 1) * SystemConstant.DEFAULT_PAGE_SIZE,
      limit: SystemConstant.DEFAULT_PAGE_SIZE
    };
    let host = ApiConstant.SYS_CONSOLE_MY_JOB_LIST_ITEM;
    this.apiService.post(host, params, httpConfig).subscribe((resp: any) => {
      this.itemTableData = {
        pagination: {
          currentPage: SystemConstant.DEFAULT_PAGE_NUM,
          pageSize: SystemConstant.DEFAULT_PAGE_SIZE,
          totalRows: resp.count
        },
        data: resp.list
      };
      if (this.scrollAnchor) {
        this.scrollerService.scrollTo(this.scrollAnchor);
      }
    }, (error => {
    }));

  }

  reset(): void {
    this.queryBatch = false;
    this.formBuilder.patchValue({
      tenantId: '',
      orgIds: [''],
      batchId: '',
      batchName: '',
      status: '',
      business: [''],
      dateArray: [DateUtils.getI18NDateBeforeDays(this.locale, 30), DateUtils.getI18NTodayCompact(this.locale)],
    });
    this.formBuilder.patchValue({tenantId: this.tenantId});
    this.doSearchByType();
  }

  download(item: any): void {
    const isHasResult = this.tableService.tableData.data.length !== 0;
    if (!isHasResult) {
      this.nwDialogService.info(this.i18nService.getI18nValue('i18n_public.undone-tips'));
      return;
    }
    if (this.itemTableData.data.length <= 0) {
      this.nwDialogService.info(this.nwI18nService.getI18nValue('i18n_console_logs.download_tips'));
      return;
    }


    let infileName = '';
    switch (item.id) {
      case 'txt':
        infileName = this.tableService.currentSelected.jobId + '_Detail_Logs_'
          + DateUtilsV2.toStringByYMDHms(new Date()) + FileSuffixConstant.TXT;
        break;
      case 'excel':
        infileName = this.tableService.currentSelected.jobId + '_Detail_Logs_'
          + DateUtilsV2.toStringByYMDHms(new Date()) + FileSuffixConstant.XLS;
        break;
    }

    const downloadOptions: NwHttpDownloadOptions = {fileName: infileName};
    // tslint:disable-next-line:triple-equals
    const submitPara = {
      itemStatus: this.itemFormBuilder.value.itemStatus,
      keyword: this.itemFormBuilder.value.keyword,
      batchId: this.batchId,
      onlyFail: this.checkFailure,
      downloadType: item.id,
    };
    this.apiService.download(ApiConstant.SYS_CONSOLE_JOB_DOWNLOAD, submitPara, downloadOptions).subscribe((res: any) => {
      const blob = new Blob([res], {type: ''});
      const url = window.URL.createObjectURL(blob);
      window.URL.revokeObjectURL(url);
    });
  }

  clickEdit(): void {

    // tslint:disable-next-line:triple-equals
    if (this.batchId == undefined) {
      this.nwDialogService.info(this.i18nService.getI18nValue('i18n_console_jobs.edit.batchId.null.tips'));
      return;
    }

    // tslint:disable-next-line:triple-equals
    if (this.currentBatchSelectRow.status != '0') {
      this.nwDialogService.error(this.i18nService.getI18nValue('i18n_console_jobs.edit.task.time.failure.tips'));
      return;
    }

    const params = {
      batchId: this.batchId,
      myJobFlag: this.myJobFlag
    };
    this.editTaskTimeModalRef = this.nwModalService.open(JobEditTaskTimeComponent, {
      data: params,
      padding: '100px',
      width: '60%'
    });
    this.editTaskTimeModalRef.onClose().subscribe((resp: any) => {
      if (resp) {
        this.doSearchByType();
      }
    });
  }

  cancelTask(): void {
    const param = {
      batchId: this.batchId
    };
    var cancelUrl = "";
    if (this.myJobFlag == '1') {
      cancelUrl = ApiConstant.SYS_CONSOLE_MY_JOB_CANCEL;
    } else {
      cancelUrl = ApiConstant.SYS_CONSOLE_JOB_CANCEL;
    }
    this.nwDialogService.confirm(this.i18nService.getI18nValue('i18n_console_jobs.cancel.task.tips'), () => {
      // tslint:disable-next-line:triple-equals
      this.apiService.post(cancelUrl, param).subscribe((resp: any) => {
        this.nwDialogService.success(this.i18nService.getI18nValue('i18n_privilege.success'),
          () => this.doSearchByType());
      }, (error => {
        this.nwDialogService.error(this.i18nService.getI18nValue('i18n_console_jobs.cancel.task.failure.tips'),
          () => this.doSearchByType());
      }));

    }, () => {
    });

  }

  setParams(): any {
    const params = Object.assign({}, this.formBuilder.value);
    params.startDate = DateUtils.formatDateToCompact(this.formBuilder.value.dateArray[0], this.locale);
    params.endDate = DateUtils.formatDateToCompact(this.formBuilder.value.dateArray[1], this.locale);

    return params;
  }


  searchItemDetail(batchIdParams: any): void {
    const httpConfig = new NwHttpConfig();
    httpConfig.loading = true;
    const params = {
      jobId: batchIdParams,
    };
    let host = '';
    if (this.myJobFlag == '1') {
      host = ApiConstant.SYS_CONSOLE_MY_JOB_LIST_ITEM_DETAIL;
    } else {
      host = ApiConstant.SYS_CONSOLE_JOB_LIST_ITEM_DETAIL;
    }
    this.apiService.post(host, params).subscribe((resp: any) => {
      // 任务编号
      this.returnBatchId = resp.jobId;
      // 创建人
      this.returnCreator = resp.createBy;
      // 开始时间
      this.returnStartTime = DateUtils.formatCompactDateTimeToLocale(resp.startTime, this.i18nService.getCurrentLocale() as string);
      // 任务名称
      this.returnBatchName = resp.jobName;
      // 创建时间
      this.returnCreateTime = DateUtils.formatCompactDateTimeToLocale(resp.createTime, this.i18nService.getCurrentLocale() as string);
      // 完成时间
      this.returnFinishTime = DateUtils.formatCompactDateTimeToLocale(resp.finishTime, this.i18nService.getCurrentLocale() as string);
      // 状态名称
      this.returnStatusName = resp.statusName;
      // 状态编号
      this.returnStatus = resp.status;
      // 总数
      this.returnTotal = resp.jobCnt;
      // 成功数量
      this.returnSuccess = resp.jobSuccCnt;
      // 失败数量
      this.returnFailure = resp.jobFailCnt;
      // 警告数量
      this.returnWarning = resp.jobWaitCnt;
      // 业务名称
      this.returnJobCodeName = resp.jobCodeName;
      // 业务编码
      this.returnJobCode = resp.jobCode;
      // 任务类型
      this.returnJobTypeName = resp.jobTypeName;
      // 备注
      this.returnRemark = resp.description;
      // jobData
      this.returnjobData = resp.jobData;

      const params2 = {
        jobType: this.currentBatchSelectRow.jobType,
        jobData: this.returnjobData
      };
      // DynamicTool.loadComponent(this.componentFactoryResolver, this.dynamicContent,
      //   JobServiceDynamicMap.get()[this.currentBatchSelectRow.jobCode], params2);
    }, (error => {
    }));
  }

  searchItem(inputParams: any, onlyFailParams: boolean, pageNumParams: number, limitParams: number): void {
    const httpConfig = new NwHttpConfig();
    httpConfig.loading = true;
    const params = {
      batchId: inputParams.batchId,
      itemStatus: inputParams.itemStatus,
      keyword: inputParams.keyword,
      onlyFail: onlyFailParams,
      offset: (pageNumParams - 1) * limitParams,
      limit: limitParams
    };
    let host = ApiConstant.SYS_CONSOLE_MY_JOB_LIST_ITEM;
    this.apiService.post(host, params, httpConfig).subscribe((resp: any) => {
      this.itemTableData = {
        pagination: {currentPage: pageNumParams, pageSize: limitParams, totalRows: resp.count},
        data: resp.list
      };
      if (this.scrollAnchor) {
        this.scrollerService.scrollTo(this.scrollAnchor);
      }
    }, (error => {
    }));
  }


  selectBatchNo(inCode: string, inTenantId: string, inOrgIds: string[], inBusiness: string[],
                inDateArray: string[], inStatus: string, inPageNum: number, inPageSize: number): Observable<any> {
    const params = {
      keyword: inCode,
      tenantId: inTenantId,
      orgIds: inOrgIds,
      business: inBusiness,
      status: inStatus,
      startDate: DateUtils.formatDateToCompactThenAppendStartTimeOfDay(inDateArray[0], this.locale),
      endDate: DateUtils.formatDateToCompactThenAppendEndTimeOfDay(inDateArray[1], this.locale),
      offset: inPageNum,
      limit: inPageSize
    };
    const config = new NwHttpConfig();
    config.loading = false;
    // tslint:disable-next-line:triple-equals
    return this.apiService.post(ApiConstant.SYS_CONSOLE_MY_JOB_BATCHNO_LIST, params, config);

  }

  downloadVoucherFile(): void {
    let fileSuffix = '';
    switch (this.currentBatchSelectRow.fileSuffix) {
      case 'gpg':
        fileSuffix = FileSuffixConstant.gpg;
        break;
      case 'csv':
        fileSuffix = FileSuffixConstant.CSV;
        break;
      case 'txt':
        fileSuffix = FileSuffixConstant.TXT;
        break;
      case 'xls':
        fileSuffix = FileSuffixConstant.XLS;
        break;
    }
    const downloadOptions: NwHttpDownloadOptions = {
      fileName: this.downloadBatchId + fileSuffix
    };
    const httpConfig = new NwHttpConfig();
    httpConfig.timeout = 300000;
    this.apiService.download(ApiConstant.SYS_CONSOLE_JOB_UPLOADED_FILE_DOWNLOAD,
      {batchId: this.batchId}, downloadOptions, httpConfig).subscribe();
  }


  ngOnDestroy(): void {
    this.jobRouterNavigateService.cancelSubscribe(this._subscription);
    if (this.editTaskTimeModalRef) {
      this.editTaskTimeModalRef.close(false);
    }
  }
}
