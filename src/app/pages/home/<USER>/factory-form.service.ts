import {FormBuilder} from '@angular/forms';
import {NwDialogService} from '@ng-nwui/components';
import {NwI18nService} from '@ng-nwui/core';
import {HttpClient} from 'src/app/services/http.client';
import {DictTools} from '../../global/dict.tools';
import {DictCode} from '../../global/enums/dict-code.enum';
import {FormService} from '../form.service';

export class FactoryFormService extends FormService{

  statusList!: any;

  constructor(
    protected fb: FormBuilder,
    protected nwDialogService: NwDialogService,
    protected apiService: HttpClient,
    protected nwI18nService: NwI18nService,
    protected dictTools: DictTools) {
    super(fb, nwDialogService, apiService, nwI18nService, dictTools);
  }

  override initForm(): void {
    this.form = this.fb.group({
      name: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(82)]],
      shortName: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(3)]],
      rspCode: [null, [this.homeService.requiredValidator, this.homeService.maxLengthValidatorFn(64)]],
      address: [null, [this.homeService.maxLengthValidatorFn(100)]],
      contactName: [null, [this.homeService.maxLengthValidatorFn(50)]],
      contactPhone: [null, [this.homeService.maxLengthValidatorFn(30)]],
      status: ['00', [this.homeService.requiredValidator]],
    });
  }

  override getDictList(): void {
    this.dictTools.getDictList(DictCode.FACTORY_STATUS).subscribe(resp => {
      this.statusList = resp;
    });
  }
}
