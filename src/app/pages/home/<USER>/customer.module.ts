import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DictTools} from '../../global/dict.tools';
import {
  NwBadgeModule,
  NwButtonGroupModule,
  NwButtonModule,
  NwCheckboxModule,
  NwDatetimeModule,
  NwDatetimeRangeModule,
  NwDialogModule,
  NwFormFieldModule,
  NwInputGroupModule,
  NwInputModule,
  NwLabelModule,
  NwModalService,
  NwPanelModule,
  NwRadioModule,
  NwScrollAnchorModule,
  NwSelectModule,
  NwSelectTreeModule,
  NwStepperModule,
  NwSwitchModule,
  NwTableModule,
  NwTabModule,
  NwTextareaModule,
  NwTreeModule,
  NwTreeTableModule
} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {CustomerRoutingModule} from './customer-routing.module';
import {CustomerMgmtComponent} from './mgmt/customer-mgmt.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NwAuthModule} from "@ng-nwui/integration";
import {PipeModule} from "../../../shared/pipe/pipe.module";
import {CustomerDetailComponent} from "./detail/customer-detail.component";
import {CustomerCreateComponent} from "./create/customer-create.component";
import {CustomerEditComponent} from "./edit/customer-edit.component";

@NgModule({
  imports: [
    NwPanelModule,
    NwCoreModule,
    NwButtonModule,
    NwButtonGroupModule,
    NwInputModule,
    NwRadioModule,
    NwCheckboxModule,
    NwLabelModule,
    ReactiveFormsModule,
    NwSelectModule,
    NwTableModule,
    NwSelectTreeModule,
    NwTabModule,
    NwBadgeModule,
    NwFormFieldModule,
    NwTextareaModule,
    FormsModule,
    NwTreeModule,
    CommonModule,
    NwTreeTableModule,
    NwSwitchModule,
    NwScrollAnchorModule,
    NwAuthModule,
    CustomerRoutingModule,
    NwInputGroupModule,
    PipeModule,
    NwDatetimeRangeModule,
    NwDialogModule,
    NwStepperModule,
    NwDatetimeModule
  ],
  declarations: [
    CustomerMgmtComponent,
    CustomerDetailComponent,
    CustomerCreateComponent,
    CustomerEditComponent
  ],
  providers: [DictTools, NwModalService]
})
export class CustomerModule {
}
