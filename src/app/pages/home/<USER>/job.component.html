<nw-panel>
  <nw-panel-header label="{{'i18n_public.query.result.condition'| nwi18n}}" [icons]="['collapse']">
  </nw-panel-header>
  <nw-panel-content>
    <div [formGroup]="formBuilder">
      <div class="row">
        <nw-form-field label="{{'i18n_public.tenant' | nwi18n}}" class="col-lg-3" *ngIf="myJobFlag!=1 && isSuperAdm">
          <nw-select formControlName="tenantId"
                     [selectData]="homeService.tenantList" [selectConfig]="{clearable: true}"></nw-select>
        </nw-form-field>
      <nw-form-field label="{{'i18n_console_jobs.batch.name' | nwi18n}}" class="col-lg-3">
        <nw-input formControlName="batchName"></nw-input>
      </nw-form-field>
      <nw-form-field label="{{'i18n_console_jobs.start.end'| nwi18n}}" class="col-lg-3">
        <nw-datetime-range formControlName="dateArray" [config]="dateConfig" clearable="false"></nw-datetime-range>
      </nw-form-field>
        <nw-form-field label="{{'i18n_console_jobs.status' | nwi18n}}" class="col-lg-3">
          <nw-select
            formControlName="status"
            [selectData]="statusList" [selectConfig]="{clearable: true}">
          </nw-select>
        </nw-form-field>
      </div>
      </div>
  </nw-panel-content>
  <nw-panel-footer>
    <button icon="fas fa-search" nw-button [color]="'primary'"
            (click)="doSearchByType()">{{'i18n_public.search.button'|nwi18n}}</button>&nbsp;
    <button nw-button color="warning" icon="fa fa-undo" (click)="reset()">{{'i18n_public.reset.button'|nwi18n}}</button>&nbsp;
  </nw-panel-footer>
</nw-panel>

<nw-panel *ngIf="queryBatch">
  <nw-panel-header label="{{'i18n_public.query.result.title'| nwi18n}}" >
  </nw-panel-header>
  <nw-panel-content>
    <div class="row">
      <nw-table-action *ngIf="currentBatchSelectRow">
        <div class="col-lg-12">{{currentBatchSelectRow.batchId}}</div>
      </nw-table-action>
      <div class="col-lg-12">
        <button nw-button icon="fa-list-alt" color="white" [disabled]="clickDetailFalg"
                (click)="goItemDetail()">{{'i18n_privilege.detail'| nwi18n}}</button>
<!--        <button nw-button icon="fa-edit " color="white" [disabled]="buttonFalg"-->
<!--                (click)="clickEdit()">-->
<!--          {{'i18n_console_jobs.table.adjust.task.time'| nwi18n}}</button>-->
<!--        <button nw-button icon="fa-trash " color="white" [disabled]="buttonFalg"-->
<!--                (click)="cancelTask()">{{'i18n_console_jobs.table.cancel.task'| nwi18n}}</button>-->
        <button nw-button color="white" *ngIf="downloadVr"
                (click)="downloadVoucherFile()"><i
          class="fas fa-arrow-down"></i>  {{'i18n_console_jobs.export.file'| nwi18n}}</button>

      </div>

      <nw-table [tableConfig]="tableService.batchTableConfig"
                [pageSizeOptions]="[10,25,50,100]"
                [tableData]="tableService.tableData"
                (changePage)="onChangePage($event)"
                (changePageSize)="onChangePageSize($event)"
                (selectRow)="batchSelectRow($event)" class="col-lg-12">
        <ng-template nw-table-column="jobCnt" let-row="row" let-cell="cell">
        <span class='badge badge-info' style='min-width: 60px;'
              title="{{'i18n_console_logs.total'|nwi18n}}">{{row.count}}</span>
          <span class='badge badge-success' style='min-width: 60px; margin: 0 3px'
                title="{{'i18n_console_logs.success.cnt'|nwi18n}}">{{row.successCount}}</span>
          <span class='badge badge-danger' style='min-width: 60px;'
                title="{{'i18n_console_logs.fail.cnt'|nwi18n}}">{{row.failureCount}}</span>
        </ng-template>

        <ng-template nw-table-column="status" let-row="row" let-cell="cell">
          <nw-label [color]='tableService.translateColumnColor(cell)'
                    *ngIf="cell">{{row.statusName}}</nw-label>
        </ng-template>

        <ng-template nw-table-column="results" let-row="row">
          <span class="badge badge-success" style="min-width: 60px; margin: 0 3px">{{row.jobCnt}}</span>
          <span class="badge badge-primary" style="min-width: 60px ; margin: 0 3px">{{row.jobSuccCnt}}</span>
          <span class="badge badge-warning" style="min-width: 60px; margin: 0 3px">{{row.jobWaitCnt}}</span>
          <span class="badge badge-danger" style="min-width: 60px ;margin: 0 3px">{{row.jobFailCnt}}</span>
        </ng-template>
      </nw-table>
    </div>


  </nw-panel-content>
</nw-panel>

<nw-panel *ngIf="detailPage" nw-scroll-anchor-auto>
  <nw-panel-content>
    <ul class="nav nav-tabs">
      <li><a class="nav-link active" data-toggle="tab" href="#tab-1"><i
        class="fa fa-list-alt"></i> {{'i18n_privilege.detail'| nwi18n}}</a></li>
      <li *ngIf="!downloadVr && !downloadJob"><a class="nav-link" data-toggle="tab" href="#tab-2"><i
        class="fa fa-align-justify"></i> {{'i18n_console_logs.user.item'| nwi18n}}</a></li>
    </ul>
    <div class="tab-content">
      <div id="tab-1" class="tab-pane active" style="padding-top: 20px">

        <div class="row">
          <div class="col-lg-12 m-b-sm m-t-sm">
            <span class="font-bold">{{ 'i18n_console_jobs.detail.job-info' | nwi18n}}</span>
            <div><hr  class="m-n "></div>
          </div>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.batch.id'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
             {{returnBatchId}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.batch.name'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{returnBatchName}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4 col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.create.by'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{currentBatchSelectRow.createName}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.create.time'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{returnCreateTime}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.start.time'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{returnStartTime}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.finish.time'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{returnFinishTime}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.business.name'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{currentBatchSelectRow.jobCodeName}}
            </dt>
          </dl>

          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.type'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              {{returnJobTypeName}}
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-4">
            <dd class="col-lg-4 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.status'| nwi18n }}</dd>
            <dt class="col-sm-8 text-sm-left p-w-xs font-weight-bold">
              <nw-label [color]='tableService.translateColumnColor(returnStatus)'>{{returnStatusName}}</nw-label>
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-8">
            <dd class="col-lg-2 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_logs.operation.details.result'| nwi18n }}</dd>
            <dt class="col-sm-6 text-sm-left p-w-xs font-weight-bold">
              <b class='badge badge-success'
                 style="margin: 0px 0px;padding: 5px 5px">&nbsp;{{"i18n_console_logs.user.detail.total"| nwi18n}}
                : {{returnTotal}}&nbsp;</b>
              <b class='badge badge-primary'
                 style="margin: 0px 3px;padding: 5px 5px">&nbsp;{{"i18n_console_logs.user.detail.success"| nwi18n}}
                : {{returnSuccess}}&nbsp;</b>
              <b class='badge badge-warning'
                 style="margin: 0px 3px;padding: 5px 5px">&nbsp;{{"i18n_console_logs.user.detail.warning"| nwi18n}}
                : {{returnWarning}}&nbsp;</b>
              <b class='badge badge-danger'
                 style="margin: 0px 0px;padding: 5px 5px">&nbsp;{{"i18n_console_logs.user.detail.failure"| nwi18n}}
                : {{returnFailure}}&nbsp;</b>
            </dt>
          </dl>
          <dl class="row m-b-none m-t-none col-lg-8">
            <dd class="col-lg-2 text-sm-right" style="padding-right: 0;padding-left: 0">{{ 'i18n_console_jobs.remark'| nwi18n }}</dd>
            <dt class="col-sm-5 text-sm-left p-w-xs font-weight-bold">
              <div class="remark">
                {{ returnRemark | nwText: '-'}}</div>
            </dt>
          </dl>
        </div>
      </div>
      <div id="tab-2" class="tab-pane">
        <div class="row" [formGroup]="itemFormBuilder">
          <nw-form-field class="col-lg-3">
            <nw-input formControlName="keyword" [placeholder]="'i18n_console_jobs.item.keyword'| nwi18n"></nw-input>
          </nw-form-field>
          <nw-form-field class="col-lg-3">
            <nw-select
              formControlName="itemStatus"
              [selectData]="statusItemList" [selectConfig]="{clearable: true}">
            </nw-select>
          </nw-form-field>
          <nw-form-field class="col-lg-6">
            <button icon="fas fa-search" nw-button [color]="'primary'"
                    (click)="doSearchItem()">{{'i18n_public.search.button'|nwi18n}}</button>&nbsp;
            <button icon="fa fa-undo" nw-button [color]="'warning'"
                    (click)="resetItem()">{{'i18n_public.reset.button'|nwi18n}}</button>&nbsp;

<!--            <nw-dropdown color="white"-->
<!--                         icon="fa-download"-->
<!--                         direction="down"-->
<!--                         title="{{'i18n_public.download.button'| nwi18n}}"-->
<!--                         align="left"-->
<!--                         (itemClick)="download($event)"-->
<!--                         [items]="downloadItems">-->
<!--            </nw-dropdown>-->
            <!--            <button icon="fas fa-download" nw-button [color]="'white'"-->
            <!--                    (click)="download()">{{'i18n_privilege_log.public.table.download.button'| nwi18n}}</button>&nbsp;-->
          </nw-form-field>
        </div>
        <nw-table [tableConfig]="tableService.itemTableConfig"
                  [pageSizeOptions]="[10,25,50,100]"
                  [tableData]="itemTableData"
                  (changePage)="onItemChangePage($event)"
                  (changePageSize)="onItemChangePagSize($event)">
          <ng-template nw-table-column="status" let-row="row" let-cell="cell">
            <nw-label [color]='tableService.translateDetailColor(cell)'
                      *ngIf="cell">{{row.statusName}}</nw-label>
          </ng-template>

        </nw-table>
      </div>
    </div>
  </nw-panel-content>
</nw-panel>
