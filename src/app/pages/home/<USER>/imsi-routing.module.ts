import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ImsiMgmtComponent} from './mgmt/imsi-mgmt.component';
import {ImsiBatchImportComponent} from "./batch-import/imsi-batch-import.component";

const routes: Routes = [
  {path: 'mgmt', component: ImsiMgmtComponent},
  {path: 'import', component: ImsiBatchImportComponent},
  {path: '', redirectTo: 'mgmt', pathMatch: 'full'}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: []
})
export class ImsiRoutingModule {
}
