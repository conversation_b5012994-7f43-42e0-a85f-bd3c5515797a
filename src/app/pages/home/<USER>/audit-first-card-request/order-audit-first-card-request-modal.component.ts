import {Component, Inject, OnInit, Optional} from '@angular/core';
import {NW_MODAL_DATA, NwDialogService, NwInputFileHook, NwModalRef} from '@ng-nwui/components';
import {FormBuilder} from '@angular/forms';
import {NwI18nService} from '@ng-nwui/core';
import { HttpClient } from 'src/app/services/http.client';
import { DictTools } from 'src/app/pages/global/dict.tools';
import { FormService } from '../../form.service';
import {ApiConstant, ConfigConstant} from '../../../global/conts';
import {FunCodeGlobalConstant} from '../../../global/conts/fun-code-global.constant';

@Component({
  selector: 'app-order-audit-first-card-request-modal',
  templateUrl: 'order-audit-first-card-request-modal.component.html',
  styleUrls: ['./order-audit-first-card-request-modal.component.scss']
})
export class OrderAuditFirstCardRequestModalComponent extends FormService implements OnInit {

  fileConfig!: any;
  hook: NwInputFileHook = {
    handleBeforeUpload: (host, file) => {
      return file.text().then(() => {
        host.params = {opCode: FunCodeGlobalConstant.ORDER_AUDIT_FIRST_CARD_REQUEST};
      });
    }
  };

  showApduFileId = false;
  apduFileId!: any;

  constructor(@Optional() @Inject(NW_MODAL_DATA) private _data: any,
              protected fb: FormBuilder,
              protected nwDialogService: NwDialogService,
              protected i18nService: NwI18nService,
              protected apiService: HttpClient,
              private modalRef: NwModalRef,
              public dictTools: DictTools,
  ) {
    super(fb, nwDialogService, apiService, i18nService, dictTools);
    this.fileConfig = ConfigConstant.getFileConfigBySuffix('*');
    this.setForm({orderId: this._data?.orderId});
  }

  ngOnInit(): void {

  }


  closeModal(value: any = null): void {
    this.modalRef?.close(value);
  }

  protected initForm(): void {
    this.form = this.fb.group({
      orderId: [null, [this.homeService.requiredValidator]],
      logFileId: [null, [this.homeService.requiredValidator]],
    });
  }

  protected getDictList(): void {

  }

  doSubmit(): void {
    if (!this.checkParams()) {
      return;
    }
    this.nwDialogService.confirm(this.nwI18nService.getI18nValue('i18n_public.submit.tips'),
      () => {
        this.apiService.post(ApiConstant.ORDER_AUDIT_FIRST_CARD_REQUEST, this.buildParams()).subscribe((res) => {
          this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_privilege.success'), () => {
            this.showApduFileId = true;
            this.apduFileId = res;
          });
        }, (res: any) => {
          this.nwDialogService.error(res?.apiResponse?.message);
        });
      }, () => {
      });
  }

  download(fileId: any): void {
    this.apiService.download(ApiConstant.FILE_DOWNLOAD_V2, {fileId}).subscribe((res) => {
      this.nwDialogService.success(this.nwI18nService.getI18nValue('i18n_public.success'));
    });
  }
}
