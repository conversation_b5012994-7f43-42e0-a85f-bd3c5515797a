<div class="gray-bg">
  <div class="loginColumns animated fadeInDown">
    <div class="row">

      <div class="col-md-6">
        <h2 class="font-bold" style="margin-bottom: 25px">SIM Profile Management System</h2>
        <p>
          Key ability =  Generation + Storage + Transmission.
        </p>

        <p>
          Safe and efficient.
        </p>

        <p>
          Developer-friendly HTTP(s) API.
        </p>

        <p>
          “Pay-As-You-Go”, low-cost professional services
        </p>
        <p>
          and much more...
        </p>

      </div>
      <div class="col-md-6">
        <div class="ibox-content" style="padding-bottom: 35px">
          <div class="m-t">
            <nw-form-field>
              <nw-dropdown block
                           color="light"
                           align="right"
                           [items]="items"
                           [title]="language.text"
                           (itemClick)="onItemClick($event)">
              </nw-dropdown>
            </nw-form-field>
            <nw-form-field>
              <nw-input type="email" [placeholder]="'i18n_username' | nwi18nrt" [(ngModel)]="username" (keyEnter)="onLogin()"></nw-input>
            </nw-form-field>
            <nw-form-field>
              <nw-input type="password" [placeholder]="'i18n_password' | nwi18nrt" [(ngModel)]="password" (keyEnter)="onLogin()"></nw-input>
            </nw-form-field>
            <div *ngIf="error" class="alert alert-danger">{{error}}</div>
            <div *ngIf="success" class="alert alert-success">{{success}}</div>
            <nw-form-field>
              <button nw-button color="primary" block (click)="onLogin()" [loading]="_logging" [disabled]="!username || !password">{{'i18n_login' | nwi18nrt}}</button>
            </nw-form-field>

            <div style="margin-top: 10px">
              <a routerLink="/login">
                <small>{{'i18n_forgot_password' | nwi18nrt}}?</small>
              </a>
            </div>

          </div>
        </div>
      </div>
    </div>
    <hr/>
    <div class="row">
      <div class="col-md-6">
        Copyright &copy; 2015-2024 Neware All rights reserved
      </div>
      <div class="col-md-6 text-right">
        Powered by <a href="https://neware.site">Neware. LTD</a>
      </div>
    </div>
  </div>
</div>

