import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {NwDialogService, NwDropDownItem} from '@ng-nwui/components';
import {NwHttpConfig, NwHttpErrorResponse, NwI18nService, NwPermissionService} from '@ng-nwui/core';
import {NwAuthService} from '@ng-nwui/integration';
import {NwFrameCoreService} from '@ng-nwui-frame/core';
import {finalize} from 'rxjs/operators';
import {SystemConstant} from '../global/conts/system.constant';
import {ApiConstant} from "../global/conts";
import {HttpClient} from "../../services/http.client";

const DEFAULT_USERNAME = 'superadm';
const DEFAULT_PASSWORD = '11111111';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  username = '';
  password = '';
  _logging = false;

  items!: NwDropDownItem[];
  error: any;
  success: any;
  _language!: NwDropDownItem;

  get language(): NwDropDownItem {
    return this._language;
  }

  set language(value) {
    this._language = value;
    this.i18nService.setLocale(value.id || 'us');
  }

  constructor(private router: Router,
              private i18nService: NwI18nService,
              private httpClient: HttpClient,
              private dialogService: NwDialogService,
              private permissionService: NwPermissionService,
              private authService: NwAuthService,
              private frameCoreService: NwFrameCoreService) {
    this.items = [{id: 'us', text: 'English'}, {id: 'cn', text: '中文简体'}];
    const cachedLocale = i18nService.getCurrentLocale();
    if (cachedLocale) {
      this.language = this.items.find(x => x.id === cachedLocale) || this.items[0];
    } else {
      this.language = this.items[0];
    }
  }

  ngOnInit(): void {
  }

  onItemClick(event: NwDropDownItem): void {
    this.language = event;
  }

  onLogin(): void {
    if (this.verifyParameters()) {
      return;
    }
    this._logging = true;
    this.frameCoreService.login({
      username: this.username.trim(),
      password: this.password.trim(),
      tokenExpiryTime: 30 * 60,
      language: this.language.id || 'us'
    }).pipe(finalize(() => this._logging = false))
      .subscribe(
        resp => {
          sessionStorage.setItem(SystemConstant.SESSION_STORAGE_CURRENT_TENANT_ID, resp.tenantId);
          this.getTokenExpiryTime();
          // setTimeout(() => sessionStorage.setItem('auth_token', 'eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************.2N-PGVpkbpwJn3ib7_fRpwqs7oLVNw4wPX4D-gmhHWc'), 8000);
        },
        (error: NwHttpErrorResponse) => {
          this.success = null;
          if (error.apiResponse) {
            this.error = error.apiResponse.message;
          } else {
            this.error = 'Login Failed!';
          }
        }
      );
  }

  getTokenExpiryTime(){
    const httpConfig = new NwHttpConfig();
    httpConfig.loading = false;
    this.httpClient.post(ApiConstant.GET_DEFAULT_PARA_VALUE, {}, httpConfig).subscribe((resp: any) => {
      sessionStorage.setItem(SystemConstant.SESSION_STORAGE_AUTH_EXPIRE_SECONDS, resp);
    },error => {
      sessionStorage.setItem(SystemConstant.SESSION_STORAGE_AUTH_EXPIRE_SECONDS, "6000");
    })

  }
  verifyParameters(): boolean {
      return !this.username || !this.password;
  }

}
