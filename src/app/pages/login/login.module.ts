import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {LoginComponent} from './login.component';
import {LoginRoutingModule} from './login-routing.module';
import {NwButtonModule, NwDropdownModule, NwFormFieldModule, NwInputModule, NwPanelModule} from '@ng-nwui/components';
import {NwCoreModule} from '@ng-nwui/core';
import {FormsModule} from '@angular/forms';

@NgModule({
  declarations: [
    LoginComponent
  ],
  imports: [
    CommonModule,
    LoginRoutingModule,
    NwButtonModule,
    NwPanelModule,
    NwFormFieldModule,
    NwDropdownModule,
    NwCoreModule,
    NwInputModule,
    FormsModule
  ]
})
export class LoginModule {
}
