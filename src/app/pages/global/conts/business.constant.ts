import {BusiOperationEnum} from '../enums/busi.operation.enum';

export class BusinessConstant {
  public static readonly FACTORY_ID_IMSI = '0000000';
  public static readonly KEY_TYPE_SYMMETRIC_KEY = '02';

  public static readonly BUSI_OPERATION_FACTORY = [BusiOperationEnum.FACTORY_ENCRYPT, BusiOperationEnum.FIRST_CARD_CHECK];
  public static readonly BUSI_OPERATION_IMSI = [BusiOperationEnum.IMPORT, BusiOperationEnum.STORAGE];
}
