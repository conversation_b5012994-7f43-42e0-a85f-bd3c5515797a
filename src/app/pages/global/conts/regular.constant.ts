/**
 * @author: <PERSON><PERSON>
 * @version: v1.1.0
 */
export class RegularConstant {

  public static readonly NUMBER = '^[0-9]*$';
  // 正整数
  public static readonly POSITIVE_INTEGER = '^[1-9]+[0-9]*$';
  public static readonly POSITIVE_INTEGER_AND_UNLIMIT = /^[[1-9]+[0-9]*$|^(\+&)$/;
  public static readonly FACE_AMOUNT = '((^[1-9][0-9]{0,8})+(.?[0-9]{1,3})?$)|(^[0]+(.[0-9]{1,3})?$)';
  public static readonly FACE_AMOUNT2 = /((^[1-9][0-9]{0,8})+(.?[0-9]{1,3})?$)|(^[0]+(.[0-9]{1,3})?$)/;
  public static readonly INTEGER = '^[+,-]{0,1}(\\d+)$';
  public static readonly POSITIVE_NUMBER = '^[0-9]*[1-9][0-9]*$';
  public static readonly AMOUNT = '(^[1-9]{1}[0-9]{0,4}$)|(^[0-9]{1,5}\\.[0-9]{1,2}$)';

  public static readonly CFG_OPERATOR_OID = /^[0-9]*[0-9\.]*[0-9]$/;
  // 大小写字母、数字、下划线，且不能以下划线开头和结尾
  public static readonly LETTER_NUMBER_UNDERLINE = /^(?!_)(?!.*?_$)[a-zA-Z0-9_]+$/;

  //只能输入16进制
  public static readonly POSITIVE_HEXADECIMAL = /^[0-9a-fA-F]+$/;

  public static readonly POSITIVE_HEXADECIMAL_UPPER = /^[0-9A-F]+$/;
  // 十六进制大写-支持存在空格
  public static readonly POSITIVE_HEXADECIMAL_UPPER_SPACE = /^\s*[0-9A-F]{4}(?:\s+[0-9A-F]{4})*\s*$/;
  // 大于等于0的正整数
  public static readonly POSITIVE_AND_ZERO_NUMBER = '(^[1-9]{1}[0-9]*$)|(^[0]{1}$)';
  public static readonly DECIMAL_NUMBER = '^\\d+(\\.[0-9]{1,4}){0,1}$';
  public static readonly FLOAT_POSITIVE_NUNBER = '^[1-9][0-9]*$|^[+]{0,1}(\\d+\\.\\d{0,2})$';
  public static readonly SECRET_KEY = '^[A-Za-z0-9@#]{16}';
  public static readonly IP = /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$/;
  public static readonly IP_DOMAIN = /(((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3})|([a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?)/;
  public static readonly SFTP_SERVER = /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/;
  public static readonly IP_HEX = /^[A-Fa-f0-9]{8}$/;
  public static readonly IPLIST = /^(\d{1,3}.\d{1,3}.\d{1,3}.[0-9x]{1,3},){0,}(\d{1,3}.\d{1,3}.\d{1,3}.[0-9x]{1,3})?$/;
  public static readonly SERVER_ERROR = /^50.*$/;
  public static readonly ZIP_CODE = /^[0-9]{6}$/;
  public static readonly KIT = /^[0-9]{2,10}$/;

  public static readonly MONEY = /((^[1-9]\d*)|^0)(\.\d{1,2})?$/;
  public static readonly IP_LIST = /^(?:(?:((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3})(?:\s*,\s*((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3})*)$/;
  public static readonly IP_LIST2 = /^(?:(?:((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3})(?:\s*,\s*((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3})*)(\/[0-9]|\/[1-2][0-9]|\/[3][0-2])?$/;
  public static readonly SFTP_PATH = /^\/[0-9a-zA-Z_\/]+$/;
  public static readonly SN = /^[A-Za-z0-9]{6,20}$/;

  public static readonly COMMON_PHONE = '^\\d{6,20}$';
  public static readonly STRICT_PHONE = '^1(3|4|5|7|8)\\d{9}$';
  public static readonly EMAIL = /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/;
  public static readonly EMAIL2 = /^[a-z0-9A-Z]+[- | a-z0-9A-Z . _]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-z]{2,}$/;
  public static readonly PORT = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;
  /**  以逗号分隔的邮箱列表 */
  public static readonly EMAIL_LIST = /^([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})([,]([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6}))*$/;
  public static readonly PASSWORD = /[a-zA-Z0-9^&#?^~!$<>\'"\\|+%/]/;
  public static readonly STRONG_PASSWORD = /^[a-zA-Z0-9^&#?^~!$<>\'"\\|+%/]{8,20}$/;
  public static readonly WEBSITE = '/(((^https?:(?:\\/\\/)?)(?:[-;:&=\\+\\$,\\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=\\+\\$,\\w]+@)[A-Za-z0-9.-]+)((?:\\/[\\+~%\\/.\\w-_]*)?\\??(?:[-\\+=&;%@.\\w_]*)#?(?:[\\w]*))?)$/';
  public static readonly IMSI = /^[1-9]{1}[0-9]{14}$/;
  public static readonly IMEI = /^[1-9]{1}[0-9]{14}$/;
  public static readonly EID = /^[1-9]{1}[0-9]{31}$/;
  public static readonly CPUID = /^[0-9a-fA-F]{16}$/;
  public static readonly MSISDN = /^[1-9]{1}[0-9]{7,14}$/;
  public static readonly SIM = /^[1-9]{1}[0-9]{18,19}$/;
  public static readonly MANX_SIM = /^[1-9]{1}[0-9]{17,18}$/;
  public static readonly MFM_SIM_PREFIX = /^89\d{7}$/;
  public static readonly SIM_FULL = '^[1-9]{1}[0-9]{19}$';
  public static readonly IMSI_SIM_MSISDN = /^[1-9]{1}[0-9]{7,19}$/;
  public static readonly SIM_CONTENT_OP = /^([a-fA-F0-9]{32})$/;
  public static readonly SIM_CONTENT_K4 = /^([a-fA-F0-9]{16})$/;
  public static readonly SIM_CONTENT_PIN = /^[0-9]{4}$/;
  public static readonly SIM_IMSI = '^[1-9]{1}[0-9]{18,19}$|^[1-9]{1}[0-9]{14}$';
  public static readonly MOBILE = /^[0-9\+\-]{1,20}$/;

  public static readonly MVNO_CODE = '^[A-Z][A-Z]$';
  public static readonly CONTACT_PHONE = '^\\d{6,20}$';
  // public static readonly NOTICE_PHONE = /^[0-9]{6,20}$/;
  public static readonly NOTICE_PHONE = /^[0-9A-z]{6,20}$/;
  public static readonly MGMT_PHONE = /^((\d{3,4}-)|\d{3,4}-)?\d{7,11}$/;
  public static readonly MGMT_FAT = /^((\d{3,4}-)|\d{3,4}-)?\d{7,11}$/;

  public static readonly MNO_CODE = '^[A-Z0-9]{5,6}$';
  public static readonly MNO_PHONE = '^\\d{8,15}$';
  public static readonly SWIFF_CODE = '^[A-Z]{8,10}$';
  public static readonly ACNOS = '^[0-9]{16,21}$';
  public static readonly PLMN_CODE = '^[A-Z0-9]{5}$';
  public static readonly MVNE_BRAND_CODE = '^[A-Z0-9]{2}$';
  public static readonly MVNE_CATALOG_CODE = '^[A-Z0-9]{4}$';

  public static readonly PRODUCT_CODE = '^(?![0-9]+$)(?![A-Z]+$)[0-9A-Z]*$';
  public static readonly ICCID = /^[0-9]{19}[0-9F]?$/;
  public static readonly COUPON_NUMBER = '^[1-9][0-9]*$';
  public static readonly COUPON_NUMBER2 = /^[1-9][0-9]*$/;
  public static readonly ACCOUNT_TOPUP = '^[1-9][0-9]*$';
  // public static readonly MONEY_REG = '^([0-9]+|[0-9]{1,3}(,[0-9]{3})*)(.[0-9]{1,2})?$';
  public static readonly MONEY_REG = /((^[1-9]{1}[0-9]*(,[0-9]{3})*)|^[1-9]{1}[0-9]+|^0)(\.\d{1,3})?$/;
  public static readonly MONEY_REG_FOUR = '^([0-9]+|[0-9]{1,3}(,[0-9]{3})*)(.[0-9]{4})?$';
  public static readonly MONEY_REG_THREE = /((^[1-9]{1}[0-9]*(,[0-9]{3})*)|^[1-9]{1}[0-9]+|^0)(\.\d{1,3})?$/;
  public static readonly CUST_NAME = '^[^&#?^~!$<>\'"\\|+%\/]*$';
  public static readonly CDR_AUTH_USERNAME = '^[^&#?*@^~!$<>\'"\\|+%\/]*$';
  public static readonly URL = '(https?|http)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]';
  public static readonly FORWORD_SLASH = '^/[A-Za-z0-9\\_\\/\\-]*';
  public static readonly CDR_LOCAL_PATH = '[A-Za-z0-9\\_\\-]*';
  public static readonly CDR_SFTP_HOST = '^(?:((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3})(?:\\s*,\\s*((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3})*$';
  public static readonly MNC = '^[1-9]{1}[0-9]{4,5}$';
  public static readonly DOMAIN = /^[0-9a-zA-z]+$/;
  public static readonly ORDER_ID = /^[0-9]{1,20}$/;

  public static readonly NAME_REG = '^[\u4E00-\u9FA5 A-Za-z0-9_]+$';
  public static readonly ICCID_LIST = /^[1-9a-zA-Z][0-9a-zA-Z]{18,19}([;；，,|\s][1-9a-zA-Z][0-9a-zA-Z]{18,19})*$/;

  public static readonly BASE_16 = /^[A-Fa-f0-9]{32}$/;
  public static readonly SETTING_SMS_SENDER = /^[A-Za-z0-9]{6,20}$/;
  public static readonly SETTING_SMS_RECIPIENT = /^[1-9][0-9]{5,19}$/;
  public static readonly TENANT_ID = /^[A-Z]{2}$/;
}
