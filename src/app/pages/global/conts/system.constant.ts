export class SystemConstant {

  public static readonly LANGUAGE_US = {id: 'us', text: 'English'};
  public static readonly LANGUAGE_CN = {id: 'cn', text: '简体中文'};
  public static readonly BASE_URL = 'base_url';
  // public static readonly LANGUAGE_HK = {id: 'hk', text: '繁體中文'};

  public static readonly TOTAL_LANGUAGE = [SystemConstant.LANGUAGE_US, SystemConstant.LANGUAGE_CN];

  public static readonly SESSION_STORAGE_CURRENT_TENANT_ID = 'current_tenantId';
  public static readonly SESSION_STORAGE_CURRENT_ORGID = 'current_orgId';
  public static readonly SESSION_STORAGE_CURRENT_STAFFNAME = 'current_staffName';
  public static readonly SESSION_STORAGE_PUBLIC_KEY = 'rsa_public_key';
  public static readonly SESSION_STORAGE_TENANT_CURRENCY = 'currency';
  public static readonly SESSION_STORAGE_AUTH_EXPIRE_SECONDS = 'auth_expire_seconds';
  public static readonly SUPER_TENANT = 'ZZ';
  public static readonly SUPER_TENANT_TEXT = 'MVNE';
  public static readonly NODE_PARENT_ID = 'ROOT';

  public static readonly DEFAULT_PAGE_NUM = 1;
  public static readonly DEFAULT_PAGE_SIZE = 10;
  public static readonly DEFAULT_TOTAL = 0;

  public static readonly RETCODE_SUCC_000 = '000';
  public static readonly MCC_CN = '460';

  public static readonly DEFAULT_PAGE_SIZE_LIST = [10, 25, 50, 100];

  public static readonly DEFAULT_UPLOAD_FILE_MAXIMUM_SIZE = '50';
  public static readonly UPLOAD_FILE_MAXIMUM_SIZE = 'upload_file_maximum_size';

  public static readonly SESSION_STORAGE_CURRENT_UNREAD_MESSAGE_COUNT = 'current_unread_message_count';
}
