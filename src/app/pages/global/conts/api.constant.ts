import {environment} from '../../../../environments/environment';

export class ApiConstant {
  constructor() {
  }

  /**
   * BASE
   * ************************************************************
   */
  public static readonly BASE = environment.frameBaseUrl + '/';
  public static readonly UICCAPI = environment.uiccUrl;
  public static readonly TIMINGAPI = environment.timingUrl + '/';

  /**
   * Login
   * ************************************************************
   */
  public static readonly SESSION: string = ApiConstant.BASE + '/base';
  public static readonly LOGIN_VERIFICATION_CODE: string = ApiConstant.SESSION + '/sendVerificationCode';
  public static readonly LOGIN: string = ApiConstant.SESSION + '/login';
  public static readonly LOGOUT: string = ApiConstant.SESSION + '/logout';
  public static readonly GLOBAL: string = ApiConstant.BASE + '/base';
  public static readonly RSA_PUBLIC_KEY: string = ApiConstant.GLOBAL + '/getRSAPublicKey';
  // public static readonly DICT_LIST = ApiConstant.GLOBAL + '/combo/queryDict';
  public static readonly DICT_GET = ApiConstant.BASE + '/global/combo/get';
  public static readonly DICT_LIST_GET = ApiConstant.BASE + '/global/combo/list/get';
  public static readonly NEXT_JOB_ID = ApiConstant.BASE + 'global/sequence/getNextJobId';
  public static readonly TIMING_MAX_OP_NUM = ApiConstant.GLOBAL + '/getTimingMaxOperationNum';
  public static readonly SUBMIT_RUN_LIMIT_NUM = ApiConstant.GLOBAL + '/getSubmitRunLimitNum';
  public static readonly TIMING_DOWNLOAD_MAX_NUM = ApiConstant.GLOBAL + '/getTimingDownloadMaxNum';
  public static readonly BILL_CONDITION_MAX_MONTH_COUNT = ApiConstant.GLOBAL + '/getConditionMaxMonthNum';
  public static readonly PRODUCT_SELECT_MAX_NUM = ApiConstant.GLOBAL + '/getProductSelectMaxNum';
  public static readonly TABLE_SELECTED_MAX_NUM = ApiConstant.GLOBAL + '/getTableSelectedMaxNum';
  public static readonly UPLOAD_FILE_MAXIMUM_SIZE = ApiConstant.GLOBAL + '/getUploadFileMaximumSize';
  public static readonly FILE_UPLOAD = ApiConstant.TIMINGAPI + 'global/flat/file/upload';
  public static readonly FILE_DELETE = ApiConstant.TIMINGAPI + 'global/flat/file/delete';
  public static readonly FILE_DOWNLOAD = ApiConstant.TIMINGAPI + 'global/flat/file/download';
  public static readonly FILE_DOWNLOAD_V2 = ApiConstant.TIMINGAPI + 'global/flat/file/downloadV2';
  public static readonly CUST_PANEL_SHOW_BOOLEAN = ApiConstant.GLOBAL + '/getCustPanelShow';
  public static readonly BILL_PANEL_SHOW_BOOLEAN = ApiConstant.GLOBAL + '/getBillPanelShow';
  public static readonly VERIFICATIONCODE_FLAG_BOOLEAN = ApiConstant.GLOBAL + '/getVerificationCodeFlag';
  public static readonly STAFF_PASSWORD_CHANGE = ApiConstant.BASE + '/privilege/staff/personal/password/change';

  public static readonly IMSI_BASE = ApiConstant.UICCAPI + '/imsi';
  public static readonly IMSI_LIST = ApiConstant.IMSI_BASE + '/list';
  public static readonly IMSI_DETAIL = ApiConstant.IMSI_BASE + '/get';
  public static readonly IMSI_TERMINATE = ApiConstant.IMSI_BASE + '/terminate';
  public static readonly IMSI_LOCK = ApiConstant.IMSI_BASE + '/lock';
  public static readonly IMSI_UNLOCK = ApiConstant.IMSI_BASE + '/unlock';
  public static readonly IMSI_RENEW = ApiConstant.IMSI_BASE + '/renewal';
  public static readonly IMSI_BATCH_IMPORT = ApiConstant.IMSI_BASE + '/batchImport';

  // 区域代码相关API
  public static readonly SY_CODE_MCC_LIST_BY_AREA = ApiConstant.IMSI_BASE + '/getMccCodeByArea';
  public static readonly SY_CODE_MCC_LIST = ApiConstant.IMSI_BASE + '/getMccCode';


  /**
   * Privilege
   * ************************************************************
   */
  public static readonly PRIVILEGE: string = ApiConstant.BASE + '/privilege';
  public static readonly MENU_TREE = ApiConstant.BASE + '/base/loadTree';
  public static readonly PERMISSION_LIST = ApiConstant.BASE + '/base/getFuncCodes';
  public static readonly ORG_TREE = ApiConstant.PRIVILEGE + '/org/tree';
  public static readonly ROLE_TREE = ApiConstant.BASE + '/privilege/role/tree';
  public static readonly ROLE_NEW = ApiConstant.BASE + '/privilege/role/create';
  public static readonly ROLE_EDIT = ApiConstant.BASE + '/privilege/role/edit';
  public static readonly ROLE_DELETE = ApiConstant.BASE + '/privilege/role/delete';
  public static readonly ROLE_MENU_TREE = ApiConstant.BASE + '/privilege/role/menu/tree';
  public static readonly ROLE_MENU_GRANT = ApiConstant.BASE + '/privilege/role/menu/grant';
  public static readonly ROLE_STAFF_LIST = ApiConstant.BASE + '/privilege/role/staff/list';
  public static readonly STAFF_LIST = ApiConstant.PRIVILEGE + '/staff/list';
  /**
   * Tenant
   * ************************************************************
   */
  public static readonly TENANT_LIST_BY_STATUS = ApiConstant.BASE + '/tenant/listByStatus';
  public static readonly COMBOBOX_TENANT_LIST_BY_STATUS = ApiConstant.BASE + '/tenant/comboBoxListByStatus';

  public static readonly GET_DEFAULT_PARA_VALUE = ApiConstant.UICCAPI + '/para/getAuthExpireSeconds';

  /**
   * my job
   * ************************************************************
   */
  public static readonly SYS_CONSOLE_MY_JOB_LIST = ApiConstant.TIMINGAPI + '/job/myJobList';
  public static readonly SYS_CONSOLE_JOB_LIST = ApiConstant.TIMINGAPI + '/job/list';

  public static readonly SYS_CONSOLE_JOB_CHECK_ENCRYPT = ApiConstant.TIMINGAPI + '/job/checkEncryptDownloadFlag';
  public static readonly SYS_CONSOLE_MY_JOB_LIST_ITEM = ApiConstant.TIMINGAPI + '/job/item/myJobLog';
  public static readonly SYS_CONSOLE_JOB_LIST_ITEM_DETAIL = ApiConstant.TIMINGAPI + '/job/itemDetails';
  public static readonly SYS_CONSOLE_MY_JOB_LIST_ITEM_DETAIL = ApiConstant.TIMINGAPI + '/job/myJobItemDetails';
  public static readonly SYS_CONSOLE_JOB_DOWNLOAD = ApiConstant.TIMINGAPI + '/job/item/log/download';
  public static readonly SYS_CONSOLE_JOB_UPLOADED_FILE_DOWNLOAD = ApiConstant.TIMINGAPI + '/job/downUploadedFile';
  public static readonly SYS_CONSOLE_MY_JOB_CANCEL = ApiConstant.TIMINGAPI + '/job/myJobCancel';
  public static readonly SYS_CONSOLE_JOB_CANCEL = ApiConstant.TIMINGAPI + '/job/cancel';
  public static readonly SYS_CONSOLE_MY_JOB_BATCHNO_LIST = ApiConstant.TIMINGAPI + '/job/myJobBatchNo';
  public static readonly SYS_CONSOLE_JOB_EDITTASKTIME = ApiConstant.TIMINGAPI + '/job/editTaskTime';
  public static readonly SYS_CONSOLE_MY_JOB_EDITTASKTIME = ApiConstant.TIMINGAPI + '/job/myJobEditTaskTime';

  public static readonly VENDOR_BASE = ApiConstant.UICCAPI + '/factory';
  public static readonly VENDOR_LIST = ApiConstant.VENDOR_BASE + '/list';
  public static readonly VENDOR_DETAIL = ApiConstant.VENDOR_BASE + '/get';
  public static readonly VENDOR_DELETE = ApiConstant.VENDOR_BASE + '/delete';
  public static readonly VENDOR_CREATE = ApiConstant.VENDOR_BASE + '/create';
  public static readonly VENDOR_UPDATE = ApiConstant.VENDOR_BASE + '/update';

  public static readonly CUSTOMER_BASE = ApiConstant.UICCAPI + '/customer';
  public static readonly CUSTOMER_LIST = ApiConstant.CUSTOMER_BASE + '/list';
  public static readonly CUSTOMER_DETAIL = ApiConstant.CUSTOMER_BASE + '/get';
  public static readonly CUSTOMER_DELETE = ApiConstant.CUSTOMER_BASE + '/delete';
  public static readonly CUSTOMER_CREATE = ApiConstant.CUSTOMER_BASE + '/create';
  public static readonly CUSTOMER_UPDATE = ApiConstant.CUSTOMER_BASE + '/update';

  public static readonly EMAIL_BASE = ApiConstant.UICCAPI + '/email';
  public static readonly EMAIL_LIST = ApiConstant.EMAIL_BASE + '/list';
  public static readonly EMAIL_DETAIL = ApiConstant.EMAIL_BASE + '/get';
  public static readonly EMAIL_TPL_DETAIL = ApiConstant.EMAIL_BASE + '/getTpl';
  public static readonly EMAIL_DELETE = ApiConstant.EMAIL_BASE + '/delete';
  public static readonly EMAIL_CREATE = ApiConstant.EMAIL_BASE + '/create';
  public static readonly EMAIL_UPDATE = ApiConstant.EMAIL_BASE + '/update';

  public static readonly KEY_BASE = ApiConstant.UICCAPI + '/key';
  public static readonly KEY_LIST = ApiConstant.KEY_BASE + '/list';
  public static readonly KEY_DETAIL = ApiConstant.KEY_BASE + '/get';
  public static readonly KEY_DELETE = ApiConstant.KEY_BASE + '/delete';
  public static readonly KEY_CREATE = ApiConstant.KEY_BASE + '/create';
  public static readonly KEY_UPDATE = ApiConstant.KEY_BASE + '/update';
  public static readonly KEY_EXPORT = ApiConstant.KEY_BASE + '/export';

  public static readonly ORDER_BASE = ApiConstant.UICCAPI + '/order';
  public static readonly ORDER_CREAT_LIST_BLOCK = ApiConstant.ORDER_BASE + '/listBlock';
  public static readonly ORDER_LIST_SIM = ApiConstant.ORDER_BASE + '/listSim';
  public static readonly ORDER_CREATE = ApiConstant.ORDER_BASE + '/create';
  public static readonly ORDER_EDIT = ApiConstant.ORDER_BASE + '/edit';
  public static readonly ORDER_LIST = ApiConstant.ORDER_BASE + '/list';
  public static readonly ORDER_DETAIL = ApiConstant.ORDER_BASE + '/get';
  public static readonly ORDER_EMAIL_DETAIL = ApiConstant.ORDER_BASE + '/getOrderEmail';
  public static readonly ORDER_ORDER_FIRST_CARD_DETAIL = ApiConstant.ORDER_BASE + '/getOrderFirstCard';
  public static readonly ORDER_AUDIT_INTERNAL_OPERATOR = ApiConstant.ORDER_BASE + '/audit/internal/operator';
  public static readonly ORDER_AUDIT_INTERNAL_TECHNOLOGY = ApiConstant.ORDER_BASE + '/audit/internal/technology';
  public static readonly ORDER_AUDIT_EXTERNAL_OPERATOR = ApiConstant.ORDER_BASE + '/audit/external/operator';
  public static readonly ORDER_AUDIT_EXTERNAL_TECHNOLOGY = ApiConstant.ORDER_BASE + '/audit/external/technology';
  public static readonly ORDER_AUDIT_FIRST_CARD_REQUEST = ApiConstant.ORDER_BASE + '/audit/firstCardRequest';
  public static readonly ORDER_AUDIT_FIRST_CARD = ApiConstant.ORDER_BASE + '/audit/firstCard';
  public static readonly ORDER_DELIVERY = ApiConstant.ORDER_BASE + '/delivery';
  public static readonly ORDER_RECEIPT = ApiConstant.ORDER_BASE + '/receipt';




}
