import {NwHttpConfig} from '@ng-nwui/core';
import {NwInputFileConfig, NwModalConfig} from '@ng-nwui/components';
import {ApiConstant} from './api.constant';
import {SystemConstant} from './system.constant';

export class ConfigConstant {

  private static readonly NO_LOADING_CONFIG = {loading: false};

  public static noLoadingConfig(): NwHttpConfig {
    return Object.assign(new NwHttpConfig(), ConfigConstant.NO_LOADING_CONFIG);
  }

  public static mdSizeModal(data: any, fixedWidth: boolean = false, padding?: string): NwModalConfig {
    if (fixedWidth) {
      return this.getSizeModalForFixedWidth(data, '540px', padding);
    } else {
      return this.getSizeModal(data, '540px', padding);
    }
  }

  public static lgSizeModal(data: any, fixedWidth: boolean = false, padding?: string): NwModalConfig {
    if (fixedWidth) {
      return this.getSizeModalForFixedWidth(data, '720px', padding);
    } else {
      return this.getSizeModal(data, '720px', padding);
    }
  }

  public static xlSizeModal(data: any, fixedWidth: boolean = false, padding?: string): NwModalConfig {
    if (fixedWidth) {
      return this.getSizeModalForFixedWidth(data, '960px', padding);
    } else {
      return this.getSizeModal(data, '960px', padding);
    }
  }

  public static xlPlugSizeModal(data: any, fixedWidth: boolean = false, padding?: string): NwModalConfig {
    if (fixedWidth) {
      return this.getSizeModalForFixedWidth(data, '1172px', padding);
    } else {
      return this.getSizeModal(data, '1172px', padding);
    }
  }

  private static getSizeModal(data: any, width: string, padding?: string): NwModalConfig {
    return {
      data,
      width,
      padding,
    };
  }

  private static getSizeModalForFixedWidth(data: any, width: string, padding?: string): NwModalConfig {
    const minWidth = width;
    return {
      data,
      minWidth,
      width,
      padding,
    };
  }

  public static getFileConfig(): NwInputFileConfig {
    return {
      url: ApiConstant.FILE_UPLOAD,
      chunk: false,
      fileType: ['.txt'],
      maxSize: 5 * 1024, // Number(sessionStorage.getItem(SystemConstant.UPLOAD_FILE_MAXIMUM_SIZE)) * 1024,
      dataAdapter: {
        code(resp: any): string {
          return resp.code;
        },
        message(resp: any): string {
          return resp.message;
        },
        data(resp: any): [] {
          return resp.data;
        },
        success(resp: any): boolean {
          return resp.code === SystemConstant.RETCODE_SUCC_000;
        }
      }
    };
  }

  public static getFileConfigBySuffix(fileType:string): NwInputFileConfig {
    return {
      url: ApiConstant.FILE_UPLOAD,
      chunk: false,
      fileType: [fileType],
      maxSize: 5 * 1024, // Number(sessionStorage.getItem(SystemConstant.UPLOAD_FILE_MAXIMUM_SIZE)) * 1024,
      dataAdapter: {
        code(resp: any): string {
          return resp.code;
        },
        message(resp: any): string {
          return resp.message;
        },
        data(resp: any): [] {
          return resp.data;
        },
        success(resp: any): boolean {
          return resp.code === SystemConstant.RETCODE_SUCC_000;
        }
      }
    };
  }

}
