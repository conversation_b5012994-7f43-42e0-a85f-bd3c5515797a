import {DocTaskStatusEnum} from '../enums/doc-task-status.enum';
import {NwColor} from '@ng-nwui/components/commons/basic/themes.directive';
import {DocStatusEnum} from '../enums/doc-status.enum';
import {DocAuditModeEnum} from '../enums/doc-audit-mode.enum';
import {FactoryStatusEnum} from "../enums/factory-status.enum";
import {BusiOperationEnum} from "../enums/busi.operation.enum";
import {IMSIStatus} from "../enums/imsi-status.enum";
import {OrderStatusEnum} from "../enums/order-status.enum";

export class ColorInfo {
  color!: NwColor | string;
  value!: string;
}

export class ColorConstant {
  public static readonly DOC_TASK_STATUS_COLOR: ColorInfo[] = [
    {color: 'secondary', value: DocTaskStatusEnum.INIT},
    {color: 'warning', value: DocTaskStatusEnum.PROCESSING},
    {color: 'primary', value: DocTaskStatusEnum.SUCCESS},
    {color: 'danger', value: DocTaskStatusEnum.FAIL},
  ];

  public static readonly DOC_STATUS_COLOR: ColorInfo[] = [
    {color: 'info', value: DocStatusEnum.PROCESSING},
    {color: 'success', value: DocStatusEnum.PENDING_AUDIT},
    {color: 'primary', value: DocStatusEnum.SUCCESS},
    {color: 'danger', value: DocStatusEnum.FAIL},
    {color: 'light', value: DocStatusEnum.DELETE},
  ];

  public static readonly DOC_AUDIT_MODE_COLOR: ColorInfo[] = [
    {color: 'secondary', value: DocAuditModeEnum.NO},
    {color: 'info', value: DocAuditModeEnum.AUTOMATIC},
    {color: 'success', value: DocAuditModeEnum.MANUAL},
    {color: 'primary', value: DocAuditModeEnum.MANUAL_AND_AUTOMATIC},
  ];

  public static readonly FACTORY_STATUS_COLOR: ColorInfo[] = [
    // {color: 'success', value: FactoryStatusEnum.MANUAL},
    {color: 'primary', value: FactoryStatusEnum.ENABLED},
    {color: 'danger', value: FactoryStatusEnum.DISABLIED},
  ];

  public static readonly BUSI_OPERATION_COLOR: ColorInfo[] = [
    // {color: 'success', value: FactoryStatusEnum.MANUAL},
    {color: 'primary', value: BusiOperationEnum.IMPORT},
    {color: 'secondary', value: BusiOperationEnum.STORAGE},
    {color: 'info', value: BusiOperationEnum.FACTORY_ENCRYPT},
    {color: 'light', value: BusiOperationEnum.FIRST_CARD_CHECK},
  ];

  public static readonly IMSI_STATUS_COLOR: ColorInfo[] = [
    {color: 'primary', value: IMSIStatus.NORMAL},
    {color: 'secondary', value: IMSIStatus.LOCKED},
    {color: 'danger', value: IMSIStatus.TERMINATED},
  ];

  public static readonly ORDER_STATUS_COLOR: ColorInfo[] = [
    {color: 'info', value: OrderStatusEnum.DRAFT},
    {color: 'secondary', value: OrderStatusEnum.REVIEWING},
    {color: 'secondary', value: OrderStatusEnum.DATA_PRODUCTION_PEDDING},
    {color: 'secondary', value: OrderStatusEnum.DATA_PRODUCTION},
    {color: 'danger', value: OrderStatusEnum.DATA_PRODUCTION_FAILED},
    {color: 'secondary', value: OrderStatusEnum.DATA_AUDIT},
    {color: 'danger', value: OrderStatusEnum.DATA_AUDIT_REJECTED},
    {color: 'secondary', value: OrderStatusEnum.EMAIL_SENDING},
    {color: 'secondary', value: OrderStatusEnum.EMAIL_SENDING_FAILED},
    {color: 'secondary', value: OrderStatusEnum.WAITING_FOR_FIRST_CARD_LOG},
    {color: 'secondary', value: OrderStatusEnum.FIRST_CARD_LOG_REVIEW},
    {color: 'secondary', value: OrderStatusEnum.CONFIRMATION_EMAIL_SENDING},
    {color: 'secondary', value: OrderStatusEnum.CONFIRMATION_EMAIL_SENDING_FAILED},
    {color: 'secondary', value: OrderStatusEnum.ORDER_TRACKING},
    {color: 'primary', value: OrderStatusEnum.ORDER_RECEIVED},
  ];
}
