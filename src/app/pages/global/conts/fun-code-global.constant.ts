export class FunCodeGlobalConstant {
  public static readonly FACTORY_CREATE = 'SYM010101';
  public static readonly FACTORY_MODIFY = 'SYM010102';
  public static readonly FACTORY_DELETE = 'SYM010103';

  public static readonly CUSTOMER_CREATE = 'SYM010401';
  public static readonly CUSTOMER_MODIFY = 'SYM010402';
  public static readonly CUSTOMER_DELETE = 'SYM010403';

  public static readonly EMAIL_CREATE = 'SYM010301';
  public static readonly EMAIL_MODIFY = 'SYM010302';
  public static readonly EMAIL_DELETE = 'SYM010303';

  public static readonly KEY_CREATE = 'SYM010201';
  public static readonly KEY_MODIFY = 'SYM010202';
  public static readonly KEY_DELETE = 'SYM010203';
  public static readonly KEY_EXPORT = 'SYM010204';

  public static readonly IMSI_RENEW = 'IMSI01';
  public static readonly IMSI_LOCK = 'IMSI02';
  public static readonly IMSI_UNLOCK = 'IMSI03';
  public static readonly IMSI_TERMINATE = 'IMSI04';
  public static readonly IMSI_BATCH_IMPORT = 'IMSI05';


  public static readonly ORDER_CREATE = 'OM01';
  public static readonly ORDER_LIST = 'OM02';
  public static readonly ORDER_EDIT = 'OM0201';
  public static readonly ORDER_AUDIT_INTERNAL_OPERATOR = 'OM0202';
  public static readonly ORDER_AUDIT_INTERNAL_TECHNOLOGY = 'OM0203';
  public static readonly ORDER_AUDIT_EXTERNAL_OPERATOR = 'OM0204';
  public static readonly ORDER_AUDIT_EXTERNAL_TECHNOLOGY = 'OM0205';
  public static readonly ORDER_FIRST_CARD_REQUEST = 'OM0206';
  public static readonly ORDER_AUDIT_FIRST_CARD_REQUEST = 'OM0207';
  public static readonly ORDER_DELIVERY = 'OM0208';
  public static readonly ORDER_RECEIPT = 'OM0209';






}
