// order-status.enum.ts
export enum OrderStatusEnum {
  // 草稿
  DRAFT = '00',
  // 订单审核中
  REVIEWING = '10',

  // 制卡数据待生产
  DATA_PRODUCTION_PEDDING = '20',

  // 制卡数据生产中
  DATA_PRODUCTION = '22',

  // 制卡数据生产失败
  DATA_PRODUCTION_FAILED = '21',
  // 制卡数据审核中
  DATA_AUDIT = '30',
  // 制卡数据审核拒绝
  DATA_AUDIT_REJECTED = '31',
  // 制卡数据审核确认
  DATA_AUDIT_CONFIRM = '32',
  // 制卡邮件发送中
  EMAIL_SENDING = '40',
  // 制卡邮件发送失败
  EMAIL_SENDING_FAILED = '41',
  // 首卡日志等待中
  WAITING_FOR_FIRST_CARD_LOG = '50',
  // 首卡日志复核中
  FIRST_CARD_LOG_REVIEW = '51',
  // 首卡确认邮件发送中
  CONFIRMATION_EMAIL_SENDING = '60',
  // 首卡确认邮件发送失败
  CONFIRMATION_EMAIL_SENDING_FAILED = '61',
  // 订单交付跟踪中
  ORDER_TRACKING = '70',
  // 订单签收
  ORDER_RECEIVED = '80',
}

