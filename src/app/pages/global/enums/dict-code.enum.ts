/**
 * 字典数据 Code 编码
 */

export enum DictCode {
  IMPORT_BATCH_PROFILE_WARNING_TYPE = 'IMPORT_BATCH_PROFILE_WARNING_TYPE',
  PROFILE_BATCH_FORMAT = 'PROFILE_BATCH_FORMAT',
  SIM_CONTENT = 'SIM_CONTENT',

  FACTORY_STATUS = 'FACTORY_STATUS',
  BUSI_OPERATION = 'BUSI_OPERATION',
  KEY_TYPE = 'KEY_TYPE',
  IMSI_STATUS = 'IMSI_STATUS',
  IMSI_VENDOR = 'IMSI_VENDOR',
  SY_CODE_AREA = 'IMSI_REGION',
  SIM_TYPE = 'SIM_TYPE',
  SIM_SPEC = 'SIM_SPEC',
  SIM_COS_TYPE = 'SIM_COS_TYPE',
  SIM_PRINT_TYPE = 'SIM_PRINT_TYPE',
  REUSE_MODE = 'REUSE_MODE',
  RSP_PINPUK_MODE = 'RSP_PINPUK_MODE',
  POLICY_ID = 'POLICY_ID',
  RSP_APP = 'RSP_APP',
  RSP_POOL_ID_NWE = 'RSP_POOL_ID_NWE',
  RSP_POOL_ID_JOY = 'RSP_POOL_ID_JOY',
  RSP_CID_PREFIX_NWE = 'RSP_CID_PREFIX_NWE',
  RSP_CID_PREFIX_JOY = 'RSP_CID_PREFIX_JOY',
  SIM_COS_VERSION = 'SIM_COS_VERSION',
  AUDIT_RESULT = 'AUDIT_RESULT',
}
