import {SystemConstant} from './conts/system.constant';
import {NwI18nService} from '@ng-nwui/core';

export class CacheManagerTools {
  private static i18nService: NwI18nService;
  constructor() {
  }

  static getUserTenantId(): string {
    const tenantId = this.getData(SystemConstant.SESSION_STORAGE_CURRENT_TENANT_ID);
    return <string>tenantId;
  }

  static getUserOrgId(): string {
    const orgId = this.getData(SystemConstant.SESSION_STORAGE_CURRENT_ORGID);
    return orgId ? orgId : '';
  }

  static getUserName(): string {
    let data = this.getData(SystemConstant.SESSION_STORAGE_CURRENT_STAFFNAME);
    if (!data) {
      // 理论上不存在执行
       data = '';
    }
    return data;
  }

  static getRsaPublicKey(): string {
    let data = this.getData(SystemConstant.SESSION_STORAGE_PUBLIC_KEY);
    if (!data) {
      // 理论上不存在执行
      data = '';
    }
    return data;
  }

  static getData(key: string): string|null {
    return sessionStorage.getItem(key);
  }

  static getCurrency(): string {
    const currency = sessionStorage.getItem(SystemConstant.SESSION_STORAGE_TENANT_CURRENCY);
    if (currency) {
      return currency;
    }
    return 'USD';
  }

  static isSuperAdm(): boolean{
    const tenantId = this.getUserTenantId();
    return tenantId === SystemConstant.SUPER_TENANT;
  }



}
