import {NwSelectConfig} from '@ng-nwui/components';
import {Injectable} from '@angular/core';
import {ApiConstant} from './conts/api.constant';
import {DictCode} from './enums/dict-code.enum';
import {ConfigConstant} from './conts/config.constant';
import {Observable} from 'rxjs';
import {HttpClient} from '../../services/http.client';

@Injectable()
export class DictTools {
  constructor(private httpService: HttpClient) {

  }

  defaultSelectConfig: NwSelectConfig = {
    selection: 'single',
    search: false,
    clearable: true
  };

  simpleSelectConfig: NwSelectConfig = {
    selection: 'single',
    search: false,
    clearable: false
  };

  searchSelectConfig: NwSelectConfig = {
    selection: 'single',
    search: true,
    clearable: true
  };

  multipleSelectConfig: NwSelectConfig = {
    selection: 'multiple',
    search: false,
    clearable: true
  };

  getDictList(dictCode: DictCode, parent?: string, tenant?: string, paramsMap?: Map<string, string>): Observable<any> {
    const param: any = {
      code: dictCode
    };
    if (parent != null) {
      param.parent = parent;
    }
    if (tenant != null) {
      param.tenant = tenant;
    }
    if (paramsMap != null) {
      const obj = Object.create(null);
      for (const [k, v] of paramsMap) {
        obj[k] = v;
      }
      param.params = obj;
    }
    return this.httpService.post(ApiConstant.DICT_GET, param, ConfigConstant.noLoadingConfig());
  }

  getMulitDictList(dictCodes: DictCode[], parent?: string, tenant?: string, paramsMap?: Map<string, string>): Observable<any> {
    const param: any = {
      codes: dictCodes
    };
    if (parent != null) {
      param.parent = parent;
    }
    if (tenant != null) {
      param.tenant = tenant;
    }
    if (paramsMap != null) {
      const obj = Object.create(null);
      for (const [k, v] of paramsMap) {
        obj[k] = v;
      }
      param.params = obj;
    }
    return this.httpService.post(ApiConstant.DICT_LIST_GET, param, ConfigConstant.noLoadingConfig());
  }

}
