import {Injectable} from '@angular/core';
import {DatePipe, registerLocaleData} from '@angular/common';
import zh from '@angular/common/locales/zh';
import * as moment from 'moment';


registerLocaleData(zh);

@Injectable()
export class DateUtils {
  public constructor() {
  }

  /**
   * H: 24小时制
   * h: 12小时制
   */

  public static I18N_DATETIME_HKUS = 'MM/dd/yyyy HH:mm:ss';
  public static I18N_DATETIME_CN = 'yyyy-MM-dd HH:mm:ss';
  public static I18N_DATE_HKUS = 'MM/dd/yyyy';
  public static I18N_DATE_CN = 'yyyy-MM-dd';

  public static STARTTIME_OF_DAY_COMPACT = '000000';
  public static ENDTIME_OF_DAY_COMPACT = '235959';

  public static DATETIME_COMPACT = 'YYYYMMddHHmmss';
  public static DATE_COMPACT = 'YYYYMMdd';

  public static REG_YMDHMS_CN = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)\s+([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
  public static REG_YMDHMS_HKUS = /^(?:(?:(?:0?[1-9]|1[0-2])\/(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])\/(?:29|30)|(?:0?[13578]|1[02])\/31)\/(?!0000)[0-9]{4}|0?2\/29\/(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00))\s+([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
  public static REG_YMD_CN = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
  public static REG_YMD_HKUS = /^(?:(?:(?:0?[1-9]|1[0-2])\/(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])\/(?:29|30)|(?:0?[13578]|1[02])\/31)\/(?!0000)[0-9]{4}|0?2\/29\/(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00))$/;

  /**
   * 字符串转年月日时分秒
   * @param datetimeStr
   * @param {string} lang
   * @returns {any}
   */
  public static formatCompactDateTimeToLocale(datetimeStr: any, lang: string) {
    let pattern = /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/;
    if (!datetimeStr) {
      return '-';
    }
    if (pattern.test(datetimeStr)) {
      let dateTime = new Date(datetimeStr.replace(pattern, '$1/$2/$3 $4:$5:$6'));
      let datePipe = new DatePipe(this.getLocaleFormatter(lang));
      return datePipe.transform(dateTime, DateUtils.getDateTimeFormatter(lang));
    } else {
      return datetimeStr;
    }
  }

  /**
   * 年月日时分秒转字符串
   * @param datetimeStr
   * @param lang
   * @returns {any}
   */
  public static formatDateTimeToCompact(datetimeStr: any, lang: string) {
    if (DateUtils.isCNDatetime(datetimeStr) || DateUtils.isHKUSDatetime(datetimeStr)) {
      let datePipe = new DatePipe(this.getLocaleFormatter(lang));
      let date = new Date(Date.parse(datetimeStr.replace(/-/g, '/')));
      let result = datePipe.transform(date, DateUtils.DATETIME_COMPACT);
      return result;
    } else {
      return datetimeStr;
    }
  }

  /**
   * 字符串转年月日
   * @param datetimeStr
   * @param {string} lang
   * @returns {any}
   */
  public static formatCompactDateToLocale(datetimeStr: any, lang: string) {
    let pattern = /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/;
    if (!datetimeStr) {
      return '-';
    }
    if (pattern.test(datetimeStr)) {
      let dateTime = new Date(datetimeStr.replace(pattern, '$1/$2/$3 $4:$5:$6'));
      let datePipe = new DatePipe(this.getLocaleFormatter(lang));
      return datePipe.transform(dateTime, DateUtils.getDateFormatter(lang));
    } else {
      return datetimeStr;
    }
  }

  /**
   * 年月日加00:00:00后转成字符串
   * @param dateStr
   * @param lang
   * @returns {any}
   */
  public static formatDateToCompactThenAppendStartTimeOfDay(dateStr: any, lang: string) {
    let result = DateUtils.formatDateToCompact(dateStr, lang);
    if (result && result.length <= 8) {
      return result + DateUtils.STARTTIME_OF_DAY_COMPACT;
    } else {
      return result;
    }
  }

  /**
   * 年月日加23:59:59后转成字符串
   * @param dateStr
   * @param lang
   * @returns {any}
   */
  public static formatDateToCompactThenAppendEndTimeOfDay(dateStr: any, lang: string) {
    let result = DateUtils.formatDateToCompact(dateStr, lang);
    if (result && result.length <= 8) {
      return result + DateUtils.ENDTIME_OF_DAY_COMPACT;
    } else {
      return result;
    }
  }

  /**
   * 年月日转字符串
   * @param dateStr
   * @param lang
   * @returns {any}
   */
  public static formatDateToCompact(dateStr: any, lang: string) {
    if (DateUtils.isCNDate(dateStr) || DateUtils.isHKUSDate(dateStr)) {
      let datePipe = new DatePipe(this.getLocaleFormatter(lang));
      let date = new Date(Date.parse(dateStr.replace(/-/g, '/')));
      let result = datePipe.transform(date, DateUtils.DATE_COMPACT);
      return result;
    } else {
      return dateStr;
    }
  }


  public static getYearMonthList(count: number): { id: string; text: string }[] {
    const monthList: { id: string; text: string }[] = [];
    var d = new Date();
    d.setDate(1);
    let yearMonth = "";
    for (var i = 0; i < count; i++) {
      var m = d.getMonth() + 1;
      var month = m < 10 ? "0" + m : m;
      yearMonth = d.getFullYear() + '' + month;
      monthList.push({"id": yearMonth, "text": yearMonth});
      d.setMonth(d.getMonth() - 1);
    }
    return monthList;
  }

  public static getI18NDateBeforeDays(lang: string, days: number) {
    if (!days || days < 0) {
      days = 0;
    }
    let datePipe = new DatePipe(this.getLocaleFormatter(lang));
    let now = new Date();
    let before = new Date(now.getTime() + (-1000 * 3600 * 24 * days));
    return datePipe.transform(before, DateUtils.getDateFormatter(lang));
  }

  public static getI18NDateAfterDays(lang: string, days: number) {
    if (!days || days < 0) {
      days = 0;
    }
    let datePipe = new DatePipe(this.getLocaleFormatter(lang));
    let now = new Date();
    let before = new Date(now.getTime() + (1000 * 3600 * 24 * days));
    return datePipe.transform(before, DateUtils.getDateFormatter(lang));
  }

  public static getI18NDateBeforeMonth(lang: string, month: number): string | null {
    if (!month || month < 0) {
      month = 0;
    }
    let datePipe = new DatePipe(this.getLocaleFormatter(lang));
    let now = new Date();
    now.setMonth(now.getMonth() - month);
    return datePipe.transform(now, DateUtils.getDateFormatter(lang));
  }

  public static getI18NTodayCompact(lang: string): string | null {
    let datePipe = new DatePipe(this.getLocaleFormatter(lang));
    return datePipe.transform(new Date(), DateUtils.getDateFormatter(lang));
  }

  public static getI18NDateTimeCompact(lang: string): string | null {
    let datePipe = new DatePipe(this.getLocaleFormatter(lang));
    return datePipe.transform(new Date().getTime(), DateUtils.getDateTimeFormatter(lang));
  }


  public static getDateTimeFormatter(lang: string): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return DateUtils.I18N_DATETIME_CN;
    } else if (lang === 'us' || lang === 'en-US') {
      return DateUtils.I18N_DATETIME_HKUS;
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return DateUtils.I18N_DATETIME_HKUS;
    }
    return DateUtils.I18N_DATETIME_CN;
  }


  public static replaceFormatter(format: string): string {
    let fmtDate = format.replace('mm', 'MM');
    fmtDate = fmtDate.replace('hh', 'HH').replace('yyyy', 'YYYY').replace('dd', 'DD');
    return fmtDate;
  }

  public static getDateFormatter(lang: string | undefined): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return this.I18N_DATE_CN;
    } else if (lang === 'us' || lang === 'en-US') {
      return DateUtils.I18N_DATE_HKUS;
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return DateUtils.I18N_DATE_HKUS;
    }
    return DateUtils.I18N_DATE_HKUS;
  }

  public static getLocaleFormatter(lang: string): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return 'zh-CN';
    } else if (lang === 'us') {
      return 'en-US';
    } else if (lang === 'hk') {
      return 'zh-HK';
    }
    return 'zh-CN';
  }

  public static getDateInputFormatter(lang: string): string {
    if (lang === 'cn') {
      return 'zh-CN';
    } else if (lang === 'us') {
      return 'en';
    } else if (lang === 'hk') {
      return 'zh-HK';
    }
    return 'zh-CN';
  }

  public static isCNDate(dateStr: any): boolean {
    return DateUtils.REG_YMD_CN.test(dateStr);
  }

  public static isHKUSDate(dateStr: any): boolean {
    return DateUtils.REG_YMD_HKUS.test(dateStr);
  }

  public static isCNDatetime(dateStr: any): boolean {
    return DateUtils.REG_YMDHMS_CN.test(dateStr);
  }

  public static isHKUSDatetime(dateStr: any): boolean {
    return DateUtils.REG_YMDHMS_HKUS.test(dateStr);
  }

  public static TimestampToDateString(str: any, lang: string): string | null {
    if (str != "" || str != null) {
      var oDate = new Date(str*1);
      let datePipe = new DatePipe(this.getLocaleFormatter(lang));
      return datePipe.transform(oDate, DateUtils.getDateFormatter(lang));
    }
    return "";
  }

  public static TimestampToDateTimeString(str: any, lang: string): string | null {
    if (str != "" || str != null) {
      var oDate = new Date(str*1);
      let datePipe = new DatePipe(this.getLocaleFormatter(lang));
      return datePipe.transform(oDate, DateUtils.getDateTimeFormatter(lang));
    }
    return "";
  }


  /**
   * <->
   * N unit 之前
   * @param date 变更目标
   * @param amount 变更量
   * @param unit 变更单位
   */
  public static subtract(date: Date, amount: any | number, unit: string): Date {
    return moment(date).subtract(unit, amount).toDate();
  }
}
