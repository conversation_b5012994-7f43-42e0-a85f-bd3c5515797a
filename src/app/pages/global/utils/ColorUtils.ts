import {NwColor} from '@ng-nwui/components/commons/basic/themes.directive';
import {ColorInfo} from '../conts/color.constant';

export class ColorUtils {

  public static colorArray = ['primary', 'info', 'danger', 'success', 'warning', 'secondary', 'white'];

  public static getColor(colors: ColorInfo[], target: string | number): NwColor | string {
    // 强制转为string进行比较
    target = target + '';
    const result = colors.find(color => color.value === target);
    return result ? result.color : '';
  }

  // @ts-ignore
  public static  getColorStr(index: number): string{
    while (index > (this.colorArray.length - 1)) {
      index = index - this.colorArray.length;
    }
    return this.colorArray[index];
  }

}
