import {Injectable} from "@angular/core";

@Injectable()
export class FormatUtils {

  public constructor() {
  }

  static toJsonFormat(txt: any, compress: any = false/*是否为压缩模式*/): any {/* 格式化JSON源码(对象转换为JSON文本) */
    if (!txt) {
      return '';
    }
    let indentChar = '    ';
    if (/^\s*$/.test(txt)) return;
    let data;
    try {
      data = eval('(' + txt + ')');
    } catch (e) {
      return txt;
    }
    ;
    let draw: any[] = [], last = false, This = this, line = compress ? '' : '\n', nodeCount = 0, maxDepth = 0;

    let notify = function (name: any, value: any, isLast: any, indent: any/*缩进*/, formObj: any) {
      nodeCount++;/*节点计数*/
      for (var i = 0, tab = ''; i < indent; i++) tab += indentChar; /* 缩进HTML */
      tab = compress ? '' : tab;/*压缩模式忽略缩进*/
      maxDepth = ++indent;/*缩进递增并记录*/
      if (value && value.constructor == Array) {/*处理数组*/
        draw.push(tab + (formObj ? ('"' + name + '":') : '') + '[' + line);/*缩进'[' 然后换行*/
        for (var i = 0; i < value.length; i++)
          notify(i, value[i], i == value.length - 1, indent, false);
        draw.push(tab + ']' + (isLast ? line : (',' + line)));/*缩进']'换行,若非尾元素则添加逗号*/
      } else if (value && typeof value == 'object') {/*处理对象*/
        draw.push(tab + (formObj ? ('"' + name + '":') : '') + '{' + line);/*缩进'{' 然后换行*/
        let len = 0, i = 0;
        for (var key in value) len++;
        for (var key in value) notify(key, value[key], ++i == len, indent, true);
        draw.push(tab + '}' + (isLast ? line : (',' + line)));/*缩进'}'换行,若非尾元素则添加逗号*/
      } else {
        if (typeof value == 'string') value = '"' + value + '"';
        if (value?.length > 90) {
          let lLine = '';
          const splitArr = value?.match(/.{1,90}/g);
          for (const obj of splitArr) {
            lLine += (line + tab + tab + obj);
          }
          draw.push(tab + (formObj ? ('"' + name + '":') : '') + lLine + (isLast ? '' : ',') + line);
        } else {
          draw.push(tab + (formObj ? ('"' + name + '":') : '') + value + (isLast ? '' : ',') + line);
        }
      }
    };
    var isLast = true, indent = 0;
    notify('', data, isLast, indent, false);
    return draw.join('');
  }

}
