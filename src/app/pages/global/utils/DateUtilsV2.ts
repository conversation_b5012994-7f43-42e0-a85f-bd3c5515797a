import * as moment from 'moment';

/**
 * <b> 关键词索引 </b> </br>
 * <table style="width:350px;">
 *     <tr>
 *       <th>输入</th>
 *       <th>例</th>
 *       <th>描述</th>
 *     </tr>
 *     <tr>
 *       <td><code>YYYY</code></td>
 *       <td><code>2014</code></td>
 *       <td>4或2位数年份</td>
 *     </tr>
 *     <tr>
 *       <td><code>YY</code></td>
 *       <td><code>14</code></td>
 *       <td>2位数年份</td>
 *     </tr>
 *     <tr>
 *       <td><code>Y</code></td>
 *       <td><code>-25</code></td>
 *       <td>有任意数字的数字和符号的年份</td>
 *     </tr>
 *     <tr>
 *       <td><code>Q</code></td>
 *       <td><code>1..4</code></td>
 *       <td>一年四分之一。将季度设置为季度的第一个月。</td>
 *     </tr>
 *     <tr>
 *       <td><code>M MM</code></td>
 *       <td><code>1..12</code></td>
 *       <td>月份编号</td>
 *     </tr>
 *     <tr>
 *       <td><code>MMM MMMM</code></td>
 *       <td><code>Jan..December</code></td>
 *       <td>设置的语言环境中的月份名称<code>moment.locale()</code></td>
 *     </tr>
 *     <tr>
 *       <td><code>D DD</code></td>
 *       <td><code>1..31</code></td>
 *       <td>一个月的一天</td>
 *     </tr>
 *     <tr>
 *       <td><code>Do</code></td>
 *       <td><code>1st..31st</code></td>
 *       <td>有序的月份日</td>
 *     </tr>
 *     <tr>
 *       <td><code>DDD DDDD</code></td>
 *       <td><code>1..365</code></td>
 *       <td>一年中的一天</td>
 *     </tr>
 *     <tr>
 *       <td><code>X</code></td>
 *       <td><code>1410715640.579</code></td>
 *       <td>Unix时间戳</td>
 *     </tr>
 *     <tr>
 *       <td><code>x</code></td>
 *       <td><code>1410715640579</code></td>
 *       <td>Unix ms时间戳</td>
 *     </tr>
 * </table>
 * -- <a href="http://momentjs.com/docs/">详情</a> --
 *
 * <table class="table table-striped table-bordered">
 *     <tr>
 *       <th>Key</th>
 *       <th>Shorthand</th>
 *     </tr>
 *     <tr>
 *       <td>years</td>
 *       <td>y</td>
 *     </tr>
 *     <tr>
 *       <td>quarters</td>
 *       <td>Q</td>
 *     </tr>
 *     <tr>
 *       <td>months</td>
 *       <td>M</td>
 *     </tr>
 *     <tr>
 *       <td>weeks</td>
 *       <td>w</td>
 *     </tr>
 *     <tr>
 *       <td>days</td>
 *       <td>d</td>
 *     </tr>
 *     <tr>
 *       <td>hours</td>
 *       <td>h</td>
 *     </tr>
 *     <tr>
 *       <td>minutes</td>
 *       <td>m</td>
 *     </tr>
 *     <tr>
 *       <td>seconds</td>
 *       <td>s</td>
 *     </tr>
 *     <tr>
 *       <td>milliseconds</td>
 *       <td>ms</td>
 *     </tr>
 * </table>
 */

export class DateUtilsV2 {

  public static readonly MAX_DATE = '20991231235959';
  public static readonly DAY_FIRST = '000000';
  public static readonly DAY_LAST = '235959';

  public static YMDHms = 'YYYYMMDDHHmmss';
  public static YMD = 'YYYYMMDD';
  public static YM = 'YYYYMM';

  public static I18N_DATE_HKUS = 'MM/DD/YYYY';
  public static I18N_DATE_CN = 'YYYY-MM-DD';
  public static I18N_DATE_YYYYMM_HKUS = 'MM/YYYY';
  public static I18N_DATE_YYYYMM_CN = 'YYYY-MM';

  public static I18N_DATE_MMDD_HKUS = 'DD/MM';
  public static I18N_DATE_MMDD_CN = 'MM-DD';


  public static I18N_DATETIME_HKUS = 'MM/DD/YYYY HH:mm:ss';
  public static I18N_DATETIME_CN = 'YYYY-MM-DD HH:mm:ss';

  public static getYearMonthFormatter(lang: string | undefined): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return this.I18N_DATE_YYYYMM_CN;
    } else if (lang === 'us' || lang === 'en-US') {
      return DateUtilsV2.I18N_DATE_YYYYMM_HKUS;
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return DateUtilsV2.I18N_DATE_YYYYMM_HKUS;
    }
    return DateUtilsV2.I18N_DATE_YYYYMM_HKUS;
  }

  public static getDateFormatter(lang: string | undefined): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return this.I18N_DATE_CN;
    } else if (lang === 'us' || lang === 'en-US') {
      return DateUtilsV2.I18N_DATE_HKUS;
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return DateUtilsV2.I18N_DATE_HKUS;
    }
    return DateUtilsV2.I18N_DATE_HKUS;
  }

  public static getDateTimeFormatter(lang: string | undefined): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return DateUtilsV2.I18N_DATETIME_CN;
    } else if (lang === 'us' || lang === 'en-US') {
      return DateUtilsV2.I18N_DATETIME_HKUS;
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return DateUtilsV2.I18N_DATETIME_HKUS;
    }
    return DateUtilsV2.I18N_DATETIME_CN;
  }

  public static getMDFormatter(lang: string | undefined): string {
    if (lang === 'cn' || lang === 'zh-CN') {
      return this.I18N_DATE_MMDD_CN;
    } else if (lang === 'us' || lang === 'en-US') {
      return DateUtilsV2.I18N_DATE_MMDD_HKUS;
    } else if (lang === 'hk' || lang === 'zh-HK') {
      return DateUtilsV2.I18N_DATE_MMDD_HKUS;
    }
    return DateUtilsV2.I18N_DATE_MMDD_CN;
  }

  /* ----------------------------- To String ---------------------------------------- */
  public static toLocalDateString(date: Date, lang: string | undefined): string {
    if (!date) {
      return date;
    }
    return moment(date).format(this.getDateFormatter(lang));
  }

  public static toLocalDateTimeString(date: Date, lang: string | undefined): string {
    return moment(date).format(this.getDateTimeFormatter(lang));
  }

  public static toLocalDateStringByYMDHms(date: string, lang: string | undefined): string {
    if (!date) {
      return date;
    }
    return moment(date, DateUtilsV2.YMDHms).format(this.getDateFormatter(lang));
  }

  public static toLocalDateTimeStringByYMDHms(date: string, lang: string | undefined): string {
    if (!date) {
      return date;
    }
    return moment(date, DateUtilsV2.YMDHms).format(this.getDateTimeFormatter(lang));
  }

  public static toLocalMDStringByYMDHms(date: Date, lang: string | undefined): string {
    if (!date) {
      return date;
    }
    return moment(date, DateUtilsV2.YMDHms).format(this.getMDFormatter(lang));
  }

  public static toStringByYMDHms(date: Date): string {
    return moment(date).format(DateUtilsV2.YMDHms);
  }

  public static toStringByYMD(date: Date): string {
    return moment(date).format(DateUtilsV2.YMD);
  }

  public static toString(date: Date, format: string): string {
    return moment(date).format(format);
  }

  public static toStringByDayFirst(date: string, format: string): string {
    const toDate = this.toDate(date, format);
    return this.toString(toDate, this.YMD) + this.DAY_FIRST;
  }

  public static toStringByDayLast(date: string, format: string): string {
    const toDate = this.toDate(date, format);
    return this.toString(toDate, this.YMD) + this.DAY_LAST;
  }

  public static toYMDByLang(date: string, lang: string | undefined): string {
    if (!date) {
      return date;
    }
    const format = this.getDateFormatter(lang);
    return moment(date, format).format(this.YMD);
  }

  public static toYMDHmsByLang(date: string, lang: string | undefined): string {
    if (!date) {
      return date;
    }
    const format = this.getDateTimeFormatter(lang);
    return moment(date, format).format(this.YMDHms);
  }

  /* ----------------------------- To Date ---------------------------------------- */
  public static maxDateTime(): Date {
    return moment(DateUtilsV2.MAX_DATE, DateUtilsV2.YMDHms).toDate();
  }

  public static toDateByYMDHms(date: string): Date {
    return moment(date, DateUtilsV2.YMDHms).toDate();
  }

  public static toDate(date: string, format: string): Date {
    return moment(date, format).toDate();
  }

  /* ----------------------------- Date Change ---------------------------------------- */
  /**
   * <+>
   * N unit 之后
   * @param date 变更目标
   * @param amount 变更量
   * @param unit 变更单位
   */
  public static add(date: Date, amount: any | number, unit: string): Date {
    return moment(date).add(unit, amount).toDate();
  }

  /**
   * <->
   * N unit 之前
   * @param date 变更目标
   * @param amount 变更量
   * @param unit 变更单位
   */
  public static subtract(date: Date, amount: any | number, unit: string): Date {
    return moment(date).subtract(unit, amount).toDate();
  }

  /**
   * 获取下月月初时间
   */
  public static getNextMonthFirst(date: Date): Date {
    return DateUtilsV2.getMonthFirst(date, 1);
  }

  /**
   * 获取下月月末时间
   */
  public static getNextMonthLast(date: Date): Date {
    return DateUtilsV2.getMonthLast(date, 1);
  }

  /**
   * N 月之前/之后的月初
   */
  public static getMonthFirst(date: Date, amountAfter?: number): Date {
    if (amountAfter) {
      return moment(date).add(amountAfter, 'M').startOf('M').toDate();
    }
    return moment(date).startOf('M').toDate();
  }

  /**
   * N 月之前/之后的月末
   */
  public static getMonthLast(date: Date, amountAfter?: number): Date {
    if (amountAfter) {
      return moment(date).add(amountAfter, 'M').endOf('M').toDate();
    }
    return moment(date).endOf('M').toDate();
  }


  public static getDayLast(date: Date, amountAfter?: number): Date {
    if (amountAfter) {
      return moment(date).add(amountAfter, 'd').endOf('d').toDate();
    }
    return moment(date).endOf('d').toDate();
  }

  public static getHourLast(date: Date, amountAfter?: number): Date {
    if (amountAfter) {
      return moment(date).add(amountAfter, 'h').endOf('h').toDate();
    }
    return moment(date).endOf('h').toDate();
  }

  public static getDayFirst(date: Date, amountAfter?: number): Date {
    if (amountAfter) {
      return moment(date).add(amountAfter, 'd').startOf('d').toDate();
    }
    return moment(date).startOf('d').toDate();
  }

  public static getHourFirst(date: Date, amountAfter?: number): Date {
    if (amountAfter) {
      return moment(date).add(amountAfter, 'h').startOf('h').toDate();
    }
    return moment(date).startOf('h').toDate();
  }

  /* ----------------------------- Date calculate ---------------------------------------- */
  public static difference(date0: Date, date1: Date): number {
    return moment(date0).diff(moment(date1));
  }

  public static diff(end: Date | string, start: Date | string, unit: 'days' | 'years' | 'months'): number {
    return moment(end).diff(moment(start), unit);
  }

  /* ----------------------------- Date Tools ---------------------------------------- */
  public static getYearMonthList(date: Date, count: number): { id: string; text: string }[] {
    const monthList: { id: string; text: string }[] = [];
    for (let i = 0; i < count; i++) {
      const yearMonth = this.toString(date, this.YM);
      monthList.push({id: yearMonth, text: yearMonth});
      date = this.subtract(date, 1, 'M');
    }
    return monthList;
  }

  static getNow(): Date {
    // TODO 时区改造时在此处修改
    // console.info(CacheManagerTools.getUserTenantId());
    return new Date();
  }
}
