import {AES, enc, mode, pad} from 'crypto-js';
import JSEncrypt from 'jsencrypt';

export class CryptoUtils {

  static encryptAES(data: string, key: string): string {
    try {
      const keyUtf8 = enc.Utf8.parse(key);
      const dataUtf8 = enc.Utf8.parse(data);
      const encrypted = AES.encrypt(dataUtf8, keyUtf8, {
        mode: mode.ECB,
        padding: pad.Pkcs7
      });
      return encrypted.toString();
    } catch (e) {
      return '';
    }
  }

  static decryptAES(data: string, key: string): { data: string, exception: boolean } {
    try {
      const keyUtf8 = enc.Utf8.parse(key);
      const decryptUtf8 = AES.decrypt(data, keyUtf8, {
        mode: mode.ECB,
        padding: pad.Pkcs7
      });
      return {
        data: enc.Utf8.stringify(decryptUtf8).toString(),
        exception: false
      };
    } catch (e) {
      return {data: '', exception: true};
    }
  }

  static encryptRSA(data: string, key: string): { data: string, exception: boolean } {
    try {
      const enctypt = new JSEncrypt();
      enctypt.setPublicKey(key);
      return {
        data: enctypt.encrypt(data) as string,
        exception: true
      };
    } catch (e) {
      return {data: '', exception: false};
    }
  }

  static random16Byte(): string {
    // 默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    const maxPos = chars.length;
    let pwd = '';
    for (let i = 0; i < 16; i++) {
      pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  }
}
