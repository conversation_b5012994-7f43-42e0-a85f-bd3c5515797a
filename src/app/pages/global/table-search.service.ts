import {NwHttpConfig} from '@ng-nwui/core';
import {EventEmitter} from '@angular/core';
import {SystemConstant} from './conts/system.constant';
import {NwTableData} from '@ng-nwui/components';
import {HttpClient} from '../../services/http.client';

export abstract class TableSearchService {

  searchCompleteMessage = new EventEmitter<any>();
  tableData!: NwTableData;
  currentSelected: any;
  allSelected: any = [];
  initSelected: any[] = [];
  rowIdKey = 'id';
  tableDataFlag = false;
  data!: any;
  countTimeFlag!: any;

  // 查询的缓存参数列表
  private queryParams: any;

  pageOptions = {
    pageNum: SystemConstant.DEFAULT_PAGE_NUM,
    pageSize: SystemConstant.DEFAULT_PAGE_SIZE,
    total: SystemConstant.DEFAULT_TOTAL
  };
  total!: any;
  readonly pageSizeList = SystemConstant.DEFAULT_PAGE_SIZE_LIST;

  constructor(protected httpService: HttpClient) {
  }

  abstract getSearchUrl(): string;

  getHttpConfig(): NwHttpConfig | undefined {
    return undefined;
  }

  getCurrentQueryParams(): any {
    return this.queryParams;
  }

  onSelectRow(result: any): void {
    this.currentSelected = result.currentSelected;
    this.allSelected = result.allSelected;
    if (this.allSelected.length === 0) {
      this.allSelected = null;
    }
  }

  setRowIdKey(rowIdKey: string): void {
    this.rowIdKey = rowIdKey;
  }

  setInitSelected(data: any, rowIdKey?: string): void {
    if (rowIdKey) {
      this.rowIdKey = rowIdKey;
    }
    this.initSelected = data;
  }

  onSearch(params: any): void {
    this.countTimeFlag = new Date().getTime();
    this.queryParams = params;
    this.resetTable();
    this.searchData(true);
  }

  onSearchWithoutResetTable(params: any): void {
    this.queryParams = params;
    this.tableData = {
      data: [],
      pagination: {
        currentPage: this.pageOptions.pageNum,
        pageSize: this.pageOptions.pageSize,
        totalRows: this.pageOptions.total
      }
    };
    this.currentSelected = null;
    this.allSelected = null;
    this.searchData(true);
  }

  onSearchWithoutPage(params: any): void {
    this.queryParams = params;
    this.resetTable();
    this.searchData(false);
  }

  onChangePage(pageNum: any): void {
    this.pageOptions.pageNum = pageNum;
    this.currentSelected = null;
    this.allSelected = null;
    this.searchData(true);
  }

  onChangePageSize(pageSize: any): void {
    this.pageOptions.pageSize = pageSize;
    this.pageOptions.pageNum = 1;
    this.currentSelected = null;
    this.allSelected = null;
    this.searchData(true);
  }

  public setTableData(inData: any) {
    this.tableData = {data: inData}
  }

  private searchData(pageFlag: boolean): void {
    if (this.total && this.total > 0) {
      this.pageOptions.total = this.total;
    }
    this.queryParams.offset = (this.pageOptions.pageNum - 1) * this.pageOptions.pageSize;
    this.queryParams.limit = this.pageOptions.pageSize;
    this.queryParams.total = this.pageOptions.total;
    this.queryParams.countTimeFlag = this.countTimeFlag;
    this.httpService.post(this.getSearchUrl(), this.queryParams, this.getHttpConfig()).subscribe((resp: any) => {
      this.tableDataFlag = true;
      this.data = resp.list;
      let inPagination;
      if (pageFlag) {
        inPagination = {
          currentPage: resp.pageNum,
          pageSize: resp.pageSize,
          totalRows: resp.total
        };
      }

      if (this.initSelected) {
        this.initSelected.forEach((key: any) => {
          const target = resp.list.find((item: any) => item[this.rowIdKey] === key);
          if (target) {
            if (!this.allSelected) {
              this.allSelected = [];
            }
            this.allSelected.push(target);
            this.onSelectRow({
              currentSelected: target,
              allSelected: this.allSelected
            });
          }
        });
      }
      if (pageFlag) {
        this.tableData = {data: resp.list, pagination: inPagination, initialSelected: this.initSelected};
      } else {
        this.tableData = {data: resp.list, initialSelected: this.initSelected};
      }
      this.searchCompleteMessage.emit(true);
    });
  }

  private resetTable(): void {
    this.pageOptions = {
      pageNum: SystemConstant.DEFAULT_PAGE_NUM,
      pageSize: SystemConstant.DEFAULT_PAGE_SIZE,
      total: SystemConstant.DEFAULT_TOTAL
    };
    this.tableData = {
      data: [],
      pagination: {
        currentPage: this.pageOptions.pageNum,
        pageSize: this.pageOptions.pageSize,
        totalRows: this.pageOptions.total
      }
    };
    this.currentSelected = null;
    this.allSelected = null;
  }

}
