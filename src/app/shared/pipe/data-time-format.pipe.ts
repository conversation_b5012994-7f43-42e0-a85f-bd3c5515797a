import {Pipe, PipeTransform} from '@angular/core';
import {DateUtils} from '../../pages/global/utils/DateUtils';
import {NwI18nService} from '@ng-nwui/core';

@Pipe({
  name: 'nwDTF'
})
export class DataTimeFormatPipe implements PipeTransform {

  constructor(protected nwI18nService: NwI18nService) {
  }

  transform(value: any, ...args: any[]): any {
    if (!value) {
      return '-';
    }
    if (args && args[0] === 'date') {
      return DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string);
    }
    return DateUtils.formatCompactDateTimeToLocale(value, this.nwI18nService.getCurrentLocale() as string);
  }

}
