import {Pipe, PipeTransform} from '@angular/core';
import {DateUtils} from '../../pages/global/utils/DateUtils';
import {NwI18nService} from '@ng-nwui/core';

@Pipe({
  name: 'nwDF'
})
export class DataFormatPipe implements PipeTransform {

  constructor(protected nwI18nService: NwI18nService) {
  }

  transform(value: any, ...args: any[]): any {
    if (!value||value===0) {
      return '-';
    }
    if (args && args[0] === 'date') {
      return DateUtils.TimestampToDateTimeString(value, this.nwI18nService.getCurrentLocale() as string);
    }
    return DateUtils.TimestampToDateTimeString(value, this.nwI18nService.getCurrentLocale() as string);
  }

}
