import {Injectable} from '@angular/core';

export type LayoutMode = 'horizontal' | 'vertical';
export type VerticalAlign = 'top' | 'center' | 'bottom' | undefined;
export type HorizontalAlign = 'left' | 'center' | 'right' | undefined;

@Injectable({
  providedIn: 'root'
})
export class FormFieldConfig {
  public labelHorizontalAlign: HorizontalAlign = 'right';
  public layout: LayoutMode = 'horizontal';
  public labelClass = 'col-lg-3';
  public fieldClass = 'col-lg-7';
  public fClass = '';

  constructor() {
  }

  static load(labelHorizontalAlign?: HorizontalAlign, layout?: LayoutMode,
              labelClass?: string, fieldClass?: string, fClass?: string): FormFieldConfig {
    const formFieldConfig = new FormFieldConfig();
    if (labelHorizontalAlign) {
      formFieldConfig.labelHorizontalAlign = labelHorizontalAlign;
    }
    if (layout) {
      formFieldConfig.layout = layout;
    }
    if (labelClass) {
      formFieldConfig.labelClass = labelClass;
    }
    if (fieldClass) {
      formFieldConfig.fieldClass = fieldClass;
    }
    if (fClass) {
      formFieldConfig.fClass = fClass;
    }
    return formFieldConfig;
  }

}
