import {Component, Input, OnInit} from '@angular/core';
import {FormFieldConfig, HorizontalAlign} from './form-field.config';

@Component({
  selector: 'busi-nw-form-field',
  templateUrl: './business-form-field.component.html',
  styleUrls: ['./business-form-field.component.scss']
})
export class BusinessFormFieldComponent implements OnInit {

  @Input() label!: string;

  _required = false;
  @Input()
  set required(value: string | boolean) {
    if (typeof value === 'boolean') {
      this._required = value;
    } else if (value === 'true') {
      this._required = true;
    }
  }

  @Input() labelHorizontalAlign: HorizontalAlign;
  @Input() layout: 'horizontal' | 'vertical';
  @Input() labelClass: string;
  @Input() fieldClass: string;
  @Input() fClass: string;

  constructor(public showConfig: FormFieldConfig) {
    this.labelHorizontalAlign = this.showConfig.labelHorizontalAlign;
    this.layout = this.showConfig.layout;
    this.labelClass = this.showConfig.labelClass;
    this.fieldClass = this.showConfig.fieldClass;
    this.fClass = this.showConfig.fClass;
  }


  ngOnInit(): void {
  }

}
