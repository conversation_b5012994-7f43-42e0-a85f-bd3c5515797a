import {Component, Input, OnInit} from '@angular/core';
import {NwI18nService} from '@ng-nwui/core';

@Component({
  selector: 'busi-nw-info-field',
  templateUrl: './business-info-field.component.html',
  styleUrls: ['./business-info-field.component.scss']
})
export class BusinessInfoFieldComponent implements OnInit {

  @Input()
  label: any;

  @Input()
  labelAlign?: 'left' | 'right';

  @Input()
  fieldClass?: string;

  @Input()
  labelClass?: string;


  constructor(protected nwI18nService: NwI18nService) {

  }

  ngOnInit(): void {
    if (!this.labelClass){
      this.labelClass = 'col-lg-4';
    }
    if (this.labelClass && !this.fieldClass && this.labelClass.split('-') && this.labelClass.split('-')[2]){
        this.fieldClass = 'col-sm-' + (12 - Number(this.labelClass.split('-')[2]));
    }
    if (this.labelAlign === undefined){
      this.labelAlign = 'right';
    }
    this.labelClass = this.labelClass?.concat(' text-sm-' + this.labelAlign);
  }

}
