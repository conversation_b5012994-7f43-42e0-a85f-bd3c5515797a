import {BusinessFormFieldComponent} from "./compnent/business-form-field/business-form-field.component";
import {NgModule} from "@angular/core";
import {NwFormFieldModule} from "@ng-nwui/components";
import {ShowAlertComponent} from "./compnent/show-alert/show-alert.component";
import {BusinessInfoFieldComponent} from "./compnent/business-info-field/business-info-field.component";
import {PipeModule} from "./pipe/pipe.module";
import {NwCoreModule} from "@ng-nwui/core";

const SHARED_COMPONENT = [
  BusinessFormFieldComponent,
  BusinessInfoFieldComponent,
  ShowAlertComponent
];

@NgModule({
  declarations: [
    ...SHARED_COMPONENT
  ],
  imports: [
    NwFormFieldModule,
    PipeModule,
    NwCoreModule
  ],
  exports: [
    ...SHARED_COMPONENT
  ]
})
export class SharedModule {
}
