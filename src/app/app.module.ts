import {NgModule} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {AppRoutingModule} from './app-routing.module';
import {AppComponent} from './app.component';
import {HttpClientModule} from '@angular/common/http';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {NwGlobalService, NwHttpService, NwI18nService, NwRouteAuthGuard, NwRouteReuseService, NwScrollerService} from '@ng-nwui/core';
import {CustomHttpDataAdapter} from './services/http.adapter';
import {CustomHttpLoading} from './services/http.loading';
import {NwRouteTransitionService} from '@ng-nwui/components';
import {NwMerchantManageApiService} from '@ng-nwui-frame/merchant-manage';
import {NwFrameCoreService} from '@ng-nwui-frame/core';
import {ProjectFrameInitializerService} from './services/project-frame-initializer.service';
import {NwPrivilegeManageApiService} from '@ng-nwui-frame/privilege-manage';
import {
  MerchantManageApiService,
  PrivilegeManageApiService,
  SystemManageApiService
} from './services/common-frame.service';
import {NwSystemManageApiService} from '@ng-nwui-frame/system-manage';

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    BrowserAnimationsModule,
  ],
  bootstrap: [AppComponent],
  providers: [
    NwGlobalService.rootProvider(),
    NwI18nService.rootProvider('assets/i18n', ['cn', 'us'], 'us'),
    NwRouteReuseService.rootProvider(),
    NwRouteAuthGuard.rootProvider('/login', ['/login', '/register']),
    NwHttpService.rootProvider(CustomHttpDataAdapter, CustomHttpLoading),
    NwScrollerService.rootProvider(),
    NwRouteTransitionService.rootProvider(),
    NwFrameCoreService.rootProvider({initializer: ProjectFrameInitializerService}),
    {provide: NwMerchantManageApiService, useExisting: MerchantManageApiService},
    {provide: NwPrivilegeManageApiService, useExisting: PrivilegeManageApiService},
    {provide: NwSystemManageApiService, useExisting: SystemManageApiService},
  ]
})
export class AppModule {
}
