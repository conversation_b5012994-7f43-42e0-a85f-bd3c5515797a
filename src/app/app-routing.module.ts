import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NwRouteAuthGuard} from '@ng-nwui/core';

const routes: Routes = [
  {
    path: 'login',
    loadChildren: () => import('./pages/login/login.module').then(m => m.LoginModule)
  },
  {
    path: '',
    canLoad: [NwRouteAuthGuard],
    canActivate: [NwRouteAuthGuard],
    loadChildren: () => import('./pages/home/<USER>').then(m => m.HomeModule)
  },
  {
    path: '**',
    redirectTo: '/dashboard',
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
