import {AfterViewInit, Component, OnInit, Renderer2} from '@angular/core';


@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, AfterViewInit {

  constructor(private renderer: Renderer2) {
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    this.renderer.selectRootElement('#nw-initial-loader-wrapper', true).classList.add('hidden');
    setTimeout(() => {
      this.renderer.selectRootElement('#nw-initial-loader-wrapper').style.display = 'none';
    }, 600);
  }

}
