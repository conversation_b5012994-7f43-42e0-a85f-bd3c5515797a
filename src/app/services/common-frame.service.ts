import {NwMerchantManageApiService} from '@ng-nwui-frame/merchant-manage';
import {Injectable} from '@angular/core';
import {environment} from '../../environments/environment';
import {NwPrivilegeManageApiService} from '@ng-nwui-frame/privilege-manage';
import {NwSystemManageApiService} from '@ng-nwui-frame/system-manage';


@Injectable({providedIn: 'root'})
export class MerchantManageApiService extends NwMerchantManageApiService {
    get baseUrl(): string {
        return environment.frameBaseUrl;
    }
  merchantManageConfig = {REGULAR_STAFF_PASSWORD: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[^]{8,18}$'};

}

@Injectable({providedIn: 'root'})
export class PrivilegeManageApiService extends NwPrivilegeManageApiService {
  get baseUrl(): string {
    return environment.frameBaseUrl;
  }
 // staffLogsApis = {ROLE_TREE: '/privilege/role/treeList'};

}


@Injectable({providedIn: 'root'})
export class SystemManageApiService extends NwSystemManageApiService {
  commonApis={TIMING_DOWNLOAD_MAX_NUM:'/base/getTimingDownloadMaxNum'}
  baseUrl = environment.frameBaseUrl;
  appLogsConfig = {showImsi: false, showMsisdn: false, showSim: false, showStatus: true};
}

