import {NwHttpDataAdapter} from '@ng-nwui/core';
import {Injectable} from '@angular/core';

/**
 * 自定义http响应数据格式
 */
export class CustomHttpDataType {
  code!: string;
  message!: string;
  data: any;
  page: any;
  list: any;
}

/**
 * 自定义http响应数据适配
 */
@Injectable({
  providedIn: 'root'
})
export class CustomHttpDataAdapter implements NwHttpDataAdapter<CustomHttpDataType> {
  code(resp: CustomHttpDataType): string {
    return resp.code;
  }

  data(resp: CustomHttpDataType): any {
    return resp.data || resp.page || resp.list;
  }

  message(resp: CustomHttpDataType): string {
    return resp.message;
  }

  success(resp: CustomHttpDataType): boolean {
    return resp && resp.code === '000';
  }

}
