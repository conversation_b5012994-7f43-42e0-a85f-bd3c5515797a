import {NwMerchantManageApiService} from '@ng-nwui-frame/merchant-manage';
import {Injectable} from '@angular/core';
import {environment} from '../../environments/environment';
import {NwPrivilegeManageApiService} from '@ng-nwui-frame/privilege-manage';
import {NwSystemManageApiService} from '@ng-nwui-frame/system-manage';


@Injectable({providedIn: 'root'})
export class MerchantManageApiService extends NwMerchantManageApiService {
  baseUrl = environment.frameBaseUrl;
  merchantManageConfig = {REGULAR_STAFF_PASSWORD: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[^]{8,18}$'};
}


@Injectable({providedIn: 'root'})
export class PrivilegeManageApiService extends NwPrivilegeManageApiService {
  baseUrl = environment.frameBaseUrl;
  REGULAR_STAFF_PASSWORD = '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[^]{8,18}$';
  staffLogsApis = {ROLE_TREE: '/privilege/role/treeList'};
}
@Injectable({providedIn: 'root'})
export class SystemManageApiService extends NwSystemManageApiService {
  baseUrl = environment.frameBaseUrl;

}
