import {EventEmitter, Injectable, Output} from '@angular/core';
import {NwI18nService, NwValidatorResult, NwValidators} from '@ng-nwui/core';
import {ValidatorFn} from '@angular/forms';
import {RegularConstant} from "../pages/global/conts/regular.constant";
import {ResourceTypeEnum} from "../pages/global/enums/resource-type.enum";

/**
 *  businessModule下的 单例 验证服务 只允许写 通用验证方法 和 验证常量
 */
@Injectable()
export class BusiCommonVerifyService {

  @Output() addNotify = new EventEmitter<any>();

  constructor(private i18nService: NwI18nService) {
  }

  inputRequiredVerify = NwValidators.syncValidator((control) => {
    const nwValidatorResult = new NwValidatorResult();
    nwValidatorResult.result = (control.value && control.value.trim().length > 0);
    return nwValidatorResult;
  }, 'i18n_public.field.input.required');
  requiredVerify = NwValidators.required('i18n_public.field.required');
  imsiPatternVerify = NwValidators.pattern(RegularConstant.IMSI, 'i18n_public.verify.imsi-regular');
  simPatternVerify = NwValidators.pattern(RegularConstant.SIM, 'i18n_public.verify.sim-regular');
  msisdnPatternVerify = NwValidators.pattern(RegularConstant.MSISDN, 'i18n_public.verify.msisdn-regular');
  positiveIntegerVerify = NwValidators.pattern(RegularConstant.POSITIVE_INTEGER, 'i18n_public.validator.positive-integer.tips');

  resPatternVerify = NwValidators.syncValidator((control) => {
    const nwValidatorResult = new NwValidatorResult();
    nwValidatorResult.result = true;
    if (control.value) {
      switch (control.parent?.value.resType) {
        case ResourceTypeEnum.SIM:
          nwValidatorResult.result = control.value.trim()?.match(RegularConstant.SIM);
          nwValidatorResult.message = 'i18n_public.verify.sim-regular';
          break;
        case ResourceTypeEnum.MSISDN:
          nwValidatorResult.result = control.value.trim()?.match(RegularConstant.MSISDN);
          nwValidatorResult.message = 'i18n_public.verify.msisdn-regular';
          break;
        case ResourceTypeEnum.IMEI:
          nwValidatorResult.result = control.value.trim()?.match(RegularConstant.IMEI);
          nwValidatorResult.message = 'i18n_public.verify.imei-regular';
          break;
        default:
          nwValidatorResult.result = control.value.trim()?.match(RegularConstant.IMSI);
          nwValidatorResult.message = 'i18n_public.verify.imsi-regular';
      }
    }
    return nwValidatorResult;
  }, 'i18n_public.verify.imsi-regular');

  moneyMaxNumVerify(num: number): ValidatorFn {
    return NwValidators.syncValidator((control) => {
          const nwValidatorResult = new NwValidatorResult();
          nwValidatorResult.result = true;
          if (control.value) {
            const fee = control.value.replace(',', '');
            nwValidatorResult.result = fee <= num;
            nwValidatorResult.message = this.i18nService.getI18nValue('i18n_public.validator.max.num.tips', '9999', [num]);
          }
          return nwValidatorResult;
        }, this.i18nService.getI18nValue('i18n_public.validator.max.num.tips', '9999', [num]));
  }


  maxNumVerify(num?: number): ValidatorFn {
    return NwValidators.max(num ? num : 9999, this.i18nService.getI18nValue('i18n_public.validator.max.num.tips', '9999', [num]));
  }
  rangNumVerify(min: number, max: number): ValidatorFn {
    const i18nValue = this.i18nService.getI18nValue('i18n_public.verify.num-rang', '', [min, max]);
    return NwValidators.syncValidator((control) => {
      const nwValidatorResult = new NwValidatorResult();
      if (control.value) {
        const num = control.value.trim();
        nwValidatorResult.result =
          RegularConstant.POSITIVE_INTEGER_AND_UNLIMIT.test(num) && num >= min && num <= max;
      } else {
        nwValidatorResult.result = true;
      }
      return nwValidatorResult;
    }, i18nValue);

    return NwValidators.pattern(RegularConstant.POSITIVE_INTEGER, i18nValue)
          && NwValidators.min(min, i18nValue)
          && NwValidators.max(max, i18nValue);
  }


  maxlengthVerify(max: number): ValidatorFn {
    return NwValidators.syncValidator((control) => {
      const nwValidatorResult = new NwValidatorResult();
      if (control.value) {
        nwValidatorResult.result = control.value.trim().length <= max;
      } else {
        nwValidatorResult.result = true;
      }
      return nwValidatorResult;
    }, this.i18nService.getI18nValue('i18n_public.public.from.length_tips', undefined, [max]));
  }




}


