import {NwHttpLoading} from '@ng-nwui/core';
import {Injectable} from '@angular/core';
import {NwDialogService} from '@ng-nwui/components';

@Injectable({
  providedIn: 'root'
})
export class CustomHttpLoading implements NwHttpLoading {
  constructor(private dialogService: NwDialogService) {
  }
  hide(): void {
    this.dialogService.closeLoading();
  }

  show(): void {
    this.dialogService.loading();
  }

}
