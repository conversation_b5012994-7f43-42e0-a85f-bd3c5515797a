import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {NwMenuData} from '@ng-nwui/components';


@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private menuUrl = 'assets/data/menus.json';

  constructor(private http: HttpClient) {
  }

  getMenus(): Observable<NwMenuData[]> {
    return this.http.get<NwMenuData[]>(this.menuUrl, {
      responseType: 'json'
    });
  }
}
