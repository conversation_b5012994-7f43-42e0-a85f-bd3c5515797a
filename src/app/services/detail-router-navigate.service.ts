import {Injectable} from '@angular/core';
import {Observable, Subject, Subscription} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DetailRouterNavigateService {
  private _params$: Subject<any> = new Subject<any>();

  getParams(): Observable<any> {
    return this._params$.asObservable();
  }

  setParams(value: any): void {
    this._params$.next(value);
  }

  cancelSubscribe(subscription: Subscription): void {
    if (subscription === null) {
      return;
    }
    subscription.unsubscribe();
  }
}
