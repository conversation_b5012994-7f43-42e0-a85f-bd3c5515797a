import {NwFrameCoreApis, NwFrameCoreInitializer, NwFrameCoreRoutes} from '@ng-nwui-frame/core';
import {Injectable} from '@angular/core';
import {NwHttpService} from '@ng-nwui/core';
import {environment} from '../../environments/environment';
import {NwMenuData} from '@ng-nwui/components';

@Injectable({providedIn: 'root'})
export class ProjectFrameInitializerService extends NwFrameCoreInitializer {
  constructor(private httpService: NwHttpService) {
    super();
  }

  get routes(): NwFrameCoreRoutes {
    return {
      login: '/login',
      home: '/'
    };
  }

  get baseUrl(): string | undefined {
    return environment.frameBaseUrl;
  }

  get apis(): NwFrameCoreApis | undefined {
    return {
      //menuList: this.httpService.post<NwMenuData[]>('http://127.0.0.1:8003/portal/base/menuList', undefined, {loading: false}),
      logout: '/base/logout'
    };
  }
}
