import {NwHttpErrorResponse, NwHttpService} from '@ng-nwui/core';
import {Injectable} from '@angular/core';
import {NwHttpConfig} from '@ng-nwui/core/http/http.config';
import {Observable, throwError} from 'rxjs';
import {catchError, tap} from 'rxjs/operators';
import {NwHttpDownloadOptions} from '@ng-nwui/core/http/http.service';
import {HttpErrorResponse} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class HttpClient {

  constructor(private httpService: NwHttpService) {
  }

  post(url: string, params?: any, config?: NwHttpConfig): Observable<any> {
    return this.httpService.post(url, params, config).pipe(
      tap(),
      catchError((error: any) => {
        if (error.httpStatus === 401 || error.httpStatus === 403) {
          return throwError(new HttpErrorResponse({}));
        }

        if (error.httpStatus === 400){
          return throwError(new NwHttpErrorResponse({
            unauthorized: false,
            httpStatus: error.httpStatus,
            httpResponse: error,
            message: 'missing parameter',
            apiResponse: {message: error.httpResponse.error.message},
          }));
        }
        return throwError(error);
      })
    );
  }

  download(url: string, params?: any, downloadOptions?: NwHttpDownloadOptions, config?: NwHttpConfig): Observable<any> {
    return this.httpService.download(url, params, downloadOptions, config);
  }

}
