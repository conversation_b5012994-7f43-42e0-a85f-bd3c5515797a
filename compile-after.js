const env = process.argv[process.argv.length - 1];

const fs = require('fs');
const path = require("path");

const buildConfig = JSON.parse(fs.readFileSync('./angular.json', {encoding: 'UTF-8'}));
const version = buildConfig.projects.version;
const appName = buildConfig.defaultProject;
const outDir = buildConfig.projects[appName].architect.build.options.outputPath;
const outputPath = path.resolve('.') + path.sep + outDir;

const zipName = buildConfig.defaultProject + "_" + env + "_v" + version;
var adm_zip = require("adm-zip");
var zip = new adm_zip();
zip.addLocalFolder(outputPath, zipName);
zip.writeZip("." + path.sep + zipName + ".zip");
